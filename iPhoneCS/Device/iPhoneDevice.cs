﻿using System.Diagnostics;
using System;
using System.Collections;
using System.Data;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Text.RegularExpressions;


using iTong.CoreFoundation;

#if IOS
using System.Drawing.Imaging;
using UIKit;
using Foundation;
using Image = UIKit.UIImage;

#elif MAC
using AppKit;
using Foundation;
using Image = AppKit.NSImage;

#endif

#if TOOLS
namespace iTong.CoreFoundation
#else
namespace iTong.Device
#endif
{
    //UDID =  Common.GetSha1Hex(this.SerialNumber + this.UniqueChipID + this.WiFiAddress + this.BluetoothAddress)

    public partial class iPhoneDevice : DeviceFunction, IDevice
    {
        private bool miTunes82 = true;
        private bool miTunes10 = true;
        private bool miTunes10_5 = true;
        private bool miTunes12_5 = true;

        private IntPtr mDeviceRef = IntPtr.Zero;
        private bool mIsConnectActive = false;
        private bool mIsSessionActive = false;
        private bool mIsConnected = false;
        private bool mJailbreaked = false;
        private int mInstallCydia = 0;
        private Dictionary<DeviceCapacityType, long> mDictCapacity = new Dictionary<DeviceCapacityType, long>(); //容量
        private Dictionary<DeviceCapacityType, int> mDictCapacityCount = new Dictionary<DeviceCapacityType, int>(); //数量

#if VS
        private int mDataTransfer_Buffer_Size = 4 * 1024 * 1024;
#elif MAC
        private int mDataTransfer_Buffer_Size = 1 * 1024 * 1024;
#else
        private int mDataTransfer_Buffer_Size = 2 * 1024 * 1024;
#endif

        public int DataTransfer_Buffer_Size
        {
            get { return this.mDataTransfer_Buffer_Size; }
        }

        private iPhoneFile syncLockdown = null;
        private PHBDeviceColorVariation mDeviceColor = PHBDeviceColorVariation.Default;

        private int mSocketSpringboardservices = 0;
        private int mSocketNotifications = 0;
        private int mSocketAfc = 0;
        private int mSocketAfc2 = 0;

        internal int mSocketMobileSync = 0;
        internal int mSocketScreenshot = 0;
        internal int mSocketImageMounter = 0;

        private IntPtr mHandleAfc = IntPtr.Zero;
        private IntPtr mHandleAfc2 = IntPtr.Zero;
        private IntPtr mHandleATHost = IntPtr.Zero;

        private ConnectMode mConnectMode = ConnectMode.USB;

        private string mPrefsValue = string.Empty;
        private Thread mThreadReadMsg = null;
        private ATHostMessage mATHostMsg = new ATHostMessage();

        private Dictionary<int, ServiceInfo> mDictSocketAndServiceInfo = new Dictionary<int, ServiceInfo>();
        private Dictionary<int, IntPtr> mDictAfcSocketAndConnHandle = new Dictionary<int, IntPtr>();
        private Dictionary<string, FileSharingPackageInfo> mDictInstalledApplications;
        private Dictionary<HouseArrestType, AfcInfo> mDictHouseArrest = new Dictionary<HouseArrestType, AfcInfo>();
        private Dictionary<string, AfcInfo> mDictAFC = new Dictionary<string, AfcInfo>();
        private object mLockerAFC = new object();

        private bool mHasLoadApplicationsDetail = false;
        private bool mHasLoadApplicationsiTunesMetaData = false;
        private bool mHasGetIsIPad = false;
        private bool mIsIPad = false;

        private string mIconStatePlist = string.Empty;
        private string mOriginalDeviceName = string.Empty;
        private string mDeviceName = string.Empty;
        private string mProductType = string.Empty;
        private string mProductVersion = string.Empty;
        private string mPartitionType = string.Empty;
        private string mIdentifier = string.Empty;
        private string mSerialNumber = string.Empty;
        private string mUniqueDeviceID = string.Empty;
        private string mUniqueDeviceID_New = string.Empty;
        private string mWiFiAddress = string.Empty;
        private string mBluetoothAddress = string.Empty;
        private string mICCID = string.Empty;
        private string mIMEI = string.Empty;
        private string mPhoneNumber = null;
        private string mBuildVersion = string.Empty;

        private int mVersionNumber = 0;
        private int mTimeZoneOffsetFromUTC = 28800;
        private string mImagePathEx = null;
        private string mDevColor = string.Empty;
        private string mDevColorBg = string.Empty;
        private string mCPUArchitecture = string.Empty;
        private string mUniqueChipID = string.Empty;
        //Private mInstallUpload As Boolean = False

        //同步推d.plist信息
        private static object mlockIDFA = new object();
        private string mIDFA = null;
        private string mIDFV = null;
        private DateTime mTuiEnterpriseShowDate = DateTime.MinValue;    //企业签名包第一次打开的时间（提示用户连接助手升级）

        private long mFairPlayDeviceType = -1;
        private byte[] mFairPlayCertificate = null;
        private string mFairPlayGUID = null;
        private long mKeyTypeSupportVersion = 0;

        private Thread mWIFIHeartbeatThread = null;
        private Thread mThreadCheckAppChanged = null;

        private object mServiceLock = new object();
        private DeviceEventSink DeviceHandleEventSink;
        private iPhonePath miPath;


        private bool mHasGetIsLivePhotoDevice = false;
        private bool mIsLivePhotoDevice = false;

        private kAMDError mConnectError = kAMDError.kAMDSuccess;

        public event EventHandler DeviceNameChanged;
        public event EventHandler<InstallApplicationCompletedEventArgs> InstallApplicationSucceed;
        public event EventHandler<UninstallApplicationCompletedEventArgs> UninstallApplicationSucceed;
        public event EventHandler<DeviceCapacityArgs> CapacityLoadCompleted;////空间加载结束事件
        public event EventHandler SyncCancelled;

        // PandaSpy需要无设备构造设备对象
        public iPhoneDevice(string serailNumber, string udid, string productType, iPhonePath iPath, int versionNumber)
            : this(serailNumber, udid, string.Empty)
        {
            this.mProductType = productType;
            this.miPath = iPath;
            this.mVersionNumber = versionNumber;
        }

        // 装机大师模拟数据需要无设备构造设备对象
        public iPhoneDevice(string serailNumber, string udid)
            : this(serailNumber, udid, string.Empty)
        {

        }

        public iPhoneDevice(string serailNumber, string udid, string deviceName)
        {
            this.mSerialNumber = serailNumber;
            this.mDeviceName = deviceName;
            this.mIdentifier = udid;
            this.mUniqueDeviceID = udid;

            this.mIsConnected = true;
            this.mConnectError = kAMDError.kAMDSuccess;
            this.mConnectMode = ConnectMode.USB;
            this.mVersionNumber = 1200;

            this.InitItunesVersion();
        }

        public iPhoneDevice(IntPtr deviceRef, ConnectMode mode)
        {
            this.InitItunesVersion();
            this.ConnectByToUSB(deviceRef, mode);
        }

        private void InitItunesVersion()
        {
#if !MAC
            if (MobileDevice.UserNew)
                mDataTransfer_Buffer_Size = 4 * 1024 * 1024;
#endif

#if MAC || IOS
			Version v1 = new Version("905.1.2.1");  //iTunes12.5.1.21;
#else
            if (!File.Exists(MobileDevice.FullPath))
                return;

            Version v1 = Common.GetFileVersion(MobileDevice.FullPath);
#endif

            //=== 判断 iTunesMobileDevice.dll 版本是否大等于 ********** （iTunes 8.2），大于的话用新的 AFCFileInfoOpen 函数，Anson Xiong 2009-06-08 22:52 ==================================

            Version v2 = new Version("**********"); //iTunes 8.2
            Version v3 = new Version("396.7.0.3");  //iTunes10
            Version v4 = new Version("503.1.0.3");  //iTunes10.5
            Version v5 = new Version("905.1.2.1");  //iTunes12.5.1.21

            //Common.Log(iTunesHelper.AppleMobileDeviceSupport);

            this.miTunes82 = System.Convert.ToBoolean(v1 >= v2);
            this.miTunes10 = System.Convert.ToBoolean(v1 >= v3);
            this.miTunes10_5 = System.Convert.ToBoolean(v1 >= v4);
            this.miTunes12_5 = System.Convert.ToBoolean(v1 >= v5);
        }

        internal void ConnectByToUSB(IntPtr deviceRef, ConnectMode mode)
        {
            this.mDeviceRef = deviceRef;
            this.mConnectMode = mode;

            this.mIsSessionActive = false;

            try
            {
                // iTunes12.5.1.21以上的版本去掉AMDeviceRetain函数接口
                if (!this.miTunes12_5)
                    MobileDevice.AMDeviceRetain(this.mDeviceRef);
            }
            catch (Exception ex)
            {
                Common.Log(ex.ToString());
                //Common.LogException(ex.ToString(), "ConnectByToUSB");
            }

            this.ConnectToPhone();
        }

        internal void InitVariable()
        {
            this.mDeviceName = this.GetDeviceValue(DeviceInfoKey.DeviceName) + "";
            this.mProductVersion = this.GetDeviceValue(DeviceInfoKey.ProductVersion) + "";
            this.mProductType = this.GetDeviceValue(DeviceInfoKey.ProductType) + "";

            if (mProductVersion.Length > 0)
            {
                mVersionNumber = Common.GetVersionNumber(mProductVersion);

                //iOS版本大于10.0下面方法就不适用了
                //mVersionNumber = System.Convert.ToInt32(mProductVersion.Replace(".", "").PadRight(3, '0').Substring(0, 3));
            }

            this.mOriginalDeviceName = this.mDeviceName;
        }

        internal void InitVariableEx()
        {
            this.mBuildVersion = this.GetDeviceValue(DeviceInfoKey.BuildVersion) + "";
            this.mSerialNumber = this.GetDeviceValue(DeviceInfoKey.SerialNumber) + "";
            this.mPartitionType = this.GetDeviceValue(DeviceInfoKey.PartitionType) + "";
            this.mTimeZoneOffsetFromUTC = Convert.ToInt32(Common.GetDigit(this.GetDeviceValue(DeviceInfoKey.TimeZoneOffsetFromUTC) + ""));
            this.mWiFiAddress = this.GetDeviceValue(DeviceInfoKey.WiFiAddress) + "";
            this.mBluetoothAddress = this.GetDeviceValue(DeviceInfoKey.BluetoothAddress) + "";
            this.mICCID = this.GetDeviceValue(DeviceInfoKey.IntegratedCircuitCardIdentity) + "";
            this.mCPUArchitecture = this.GetDeviceValue(DeviceInfoKey.CPUArchitecture) + "";
            this.mUniqueChipID = this.GetDeviceValue(DeviceInfoKey.UniqueChipID) + "";


            //If Me.mCPUArchitecture = "armv7s" OrElse Val(mCPUArchitecture) > 7 Then
            //    Me.mInstallUpload = True
            //End If

            //this.CheckUDIDIsFake();
            this.InitUniqueDeviceID();
            this.InitColor();
        }

        private void InitUniqueDeviceID()
        {
            this.mUniqueDeviceID = this.GetDeviceValue(DeviceInfoKey.UniqueDeviceID) + "";
            if (this.mUniqueDeviceID.Length == 25)
            {
                this.mUniqueDeviceID_New = "6097A4B0C20A0A9F" + this.mUniqueDeviceID.Replace("-", "").Replace("_", "");
            }
            else
            {
                this.mUniqueDeviceID_New = this.mUniqueDeviceID;
            }
        }

        private void InitColor()
        {
            this.mDevColor = this.GetDeviceValue(DeviceInfoKey.DeviceColor) + "";
            this.mDevColorBg = this.GetDeviceValue(DeviceInfoKey.DeviceEnclosureColor) + "";

            int DeviceTypeNumber = 0;
            int DeviceTypeNumberSmall = 0;
            bool isIpad = this.mProductType.Contains("iPad");

            //新设备颜色未知
            string tmpVersion = this.mProductType
                .Replace("iPhone", "")
                .Replace("iPad", "")
                .Replace("iPod", "");



            string[] arrValue = tmpVersion.Split(',');
            if (arrValue.Length == 2)
            {
                DeviceTypeNumber = Common.GetInteger(arrValue[0]);
                DeviceTypeNumberSmall = Common.GetInteger(arrValue[1]);
            }

            if (DeviceTypeNumber >= 10)
            {
                Common.LogException(string.Format("ProductType: {0}    ForeColor:{1}    BackColor: {2}", this.mProductType.PadRight(11, ' '), this.mDevColor, this.mDevColorBg));
            }

            if (!string.IsNullOrEmpty(this.mDevColorBg))
            {
                switch (this.mDevColorBg)
                {
                    case "white": //5
                        if (this.mDevColor == "black")
                            this.mDeviceColor = PHBDeviceColorVariation.Black;
                        else
                            this.mDeviceColor = PHBDeviceColorVariation.White;
                        break;

                    case "black": //5
                        if (this.mDevColor == "white")
                            this.mDeviceColor = PHBDeviceColorVariation.White;
                        else
                            this.mDeviceColor = PHBDeviceColorVariation.Black;
                        break;

                    case "1":
                        if (DeviceTypeNumber >= 14)
                        {
                            //iPhone13、iPhone14午夜色
                            this.mDeviceColor = PHBDeviceColorVariation.Midnight;
                        }
                        else if (DeviceTypeNumber == 12 || DeviceTypeNumber == 13 || DeviceTypeNumber == 9)
                        {
                            //iPhone11、iPhone12、iPhone7黑色
                            this.mDeviceColor = PHBDeviceColorVariation.Black;
                        }
                        else
                        {
                            this.mDeviceColor = PHBDeviceColorVariation.SpaceGray;
                        }

                        break;
                    case "8":      //8|iPhone X深空灰
                        if (this.mProductType.StartsWith("iPhone9,", StringComparison.OrdinalIgnoreCase))
                            this.mDeviceColor = PHBDeviceColorVariation.Black;
                        else if (this.mProductType.StartsWith("iPhone11,8", StringComparison.OrdinalIgnoreCase))
                            this.mDeviceColor = PHBDeviceColorVariation.Blue;
                        else
                            this.mDeviceColor = PHBDeviceColorVariation.SpaceGray;

                        break;
                    case "2":      //7银色、8|iPhone X银色、iPhone XS银色 iPhone11 pro 银色  iPhone11白色
                    case "silver": //iPod5 银色
                        if (this.mProductType.StartsWith("iPhone12,", StringComparison.OrdinalIgnoreCase))
                            this.mDeviceColor = PHBDeviceColorVariation.White;
                        else if (this.mDevColor == "black")
                            this.mDeviceColor = PHBDeviceColorVariation.Black;
                        else
                            this.mDeviceColor = PHBDeviceColorVariation.Silver;
                        break;

                    case "6":   //iPhone7红色 iPhone11红色
                    case "red": //iPod5红色
                    case "#c6353f"://iPod6红色
                        this.mDeviceColor = PHBDeviceColorVariation.Red;
                        break;

                    case "yellow": //iPod5
                    case "#faf189"://5C黄色
                        this.mDeviceColor = PHBDeviceColorVariation.Yellow;
                        break;

                    case "blue": //iPod5
                    case "#46abe0"://5C蓝色
                    case "#458dce"://iPod6
                        this.mDeviceColor = PHBDeviceColorVariation.Blue;
                        break;

                    case "pink": //iPod5
                    case "#fe767a"://5C粉色
                    case "#e75090"://iPod6
                        this.mDeviceColor = PHBDeviceColorVariation.Pink;
                        break;

                    case "#f5f4f7"://5C白色
                        this.mDeviceColor = PHBDeviceColorVariation.White;
                        break;

                    case "#a1e877": //5C绿色
                        this.mDeviceColor = PHBDeviceColorVariation.Green;
                        break;

                    case "#99989b"://5S/Air/Mini2
                    case "#b4b5b9"://6/Air2/Mini3/Mini4
                    case "#6b6a6d"://iPod6
                    case "#aeb1b8"://SE深空灰
                    case "#b9b7ba"://6s
                        this.mDeviceColor = PHBDeviceColorVariation.SpaceGray;
                        break;

                    case "#d7d9d8"://5S/6银白色/Air/Air2/Mini2/Mini3/Mini4
                    case "#dadcdb"://6s/iPod6
                    case "#dcdede"://SE银色
                        this.mDeviceColor = PHBDeviceColorVariation.Silver;
                        break;

                    case "3":       //7金色
                    case "7":       //8金色  iPhone11黄色
                    case "#d4c5b3": //5S土豪金/Air/Mini2
                    case "#e1ccb5": //6土豪金/Air2/Mini3/Mini4
                    case "#d6c8b9": //SE金色
                    case "#e1ccb7": //6s土豪金/Mini4/iPod6
                        if (this.mProductType.StartsWith("iPhone12,", StringComparison.OrdinalIgnoreCase))
                            this.mDeviceColor = PHBDeviceColorVariation.Yellow; //iPhone11 黄色(iPhone12,1)
                        else if (this.mProductType.StartsWith("iPad14,", StringComparison.OrdinalIgnoreCase))
                            this.mDeviceColor = PHBDeviceColorVariation.Purple; //iPad Mini 6 紫色(iPad14,1)
                        else
                            this.mDeviceColor = PHBDeviceColorVariation.Gold;
                        break;

                    case "4":       //7玫瑰金、iPhone XS金色
                    case "#e5bdb5": //SE玫瑰金
                    case "#e4c1b9": //6s玫瑰金
                        if (this.mProductType.StartsWith("iPhone14,", StringComparison.OrdinalIgnoreCase))
                            this.mDeviceColor = PHBDeviceColorVariation.StarLight; //iPhone 13 星光色
                        else if (this.mDevColor == "1")
                            this.mDeviceColor = PHBDeviceColorVariation.Gold;
                        else
                            this.mDeviceColor = PHBDeviceColorVariation.Rose;
                        break;

                    case "5":       //7亮黑色
                        this.mDeviceColor = PHBDeviceColorVariation.LightBlack;
                        break;

                    case "9":
                        //iPhone 12/13  天蓝色
                        //iPhone Xr     珊瑚色【橘红】
                        if (this.mProductType.StartsWith("iPhone11,", StringComparison.OrdinalIgnoreCase))
                            this.mDeviceColor = PHBDeviceColorVariation.Coral;          //iPhone Xr 珊瑚色【橘红】(iPhone11,8)
                        else
                            this.mDeviceColor = PHBDeviceColorVariation.PacificBlue;    //iPhone 12 Pro Max 海蓝色
                        break;

                    case "17":
                        //if (this.mProductType.StartsWith("iPhone12,1", StringComparison.OrdinalIgnoreCase))
                        this.mDeviceColor = PHBDeviceColorVariation.Purple;
                        break;

                    case "18":
                        //if (this.mProductType.StartsWith("iPhone12,1", StringComparison.OrdinalIgnoreCase))
                        if (DeviceTypeNumber >= 12)
                            this.mDeviceColor = PHBDeviceColorVariation.Green;
                        else
                            this.mDeviceColor = PHBDeviceColorVariation.DarkGreen;
                        break;


                    default:
                        Common.LogException(string.Format("ForeColor:{0}    BackColor: {1}", this.mDevColor, this.mDevColorBg));
                        break;

                }
            }

            if (this.mDeviceColor == PHBDeviceColorVariation.Default && !string.IsNullOrEmpty(mDevColor))
            {
                //4/4s/iPad1、2、3、4
                switch (this.mDevColor)
                {
                    case "white":
                        this.mDeviceColor = PHBDeviceColorVariation.White;
                        break;

                    case "silver":
                        this.mDeviceColor = PHBDeviceColorVariation.Silver;
                        break;

                    case "black":
                        this.mDeviceColor = PHBDeviceColorVariation.Black;
                        break;

                    default:
                        this.mDeviceColor = PHBDeviceColorVariation.Unknown;
                        break;

                }
            }

            string strFormat = string.Format("{0}\tForeColor:{1}\tBackColor:{2}\tColor:{3}", SummaryInfo.FormatProduct(this.mProductType), mDevColor, mDevColorBg, mDeviceColor.ToString());
            Console.WriteLine(strFormat);
            Common.LogException(strFormat);
        }

        private bool iTunesSetupComplete()
        {
            bool blnResult = false;

            if (this.VersionNumber < 700)
            {
                blnResult = true;
                goto DO_EXIT;
            }

            try
            {
                string strValue = this.GetDeviceValue("com.apple.mobile.iTunes", "iTunesSetupComplete") + "";
                if (!string.IsNullOrEmpty(strValue))
                    blnResult = System.Convert.ToBoolean(strValue);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iPhoneDevice.iTunesSetupComplete");
            }

        DO_EXIT:
            return blnResult;
        }

        #region --- 连接和断开 ---

        public kAMDError ConnectError
        {
            get
            {
                return this.mConnectError;
            }
        }

        public void CloseAFCConnection(ref IntPtr afcHandle, bool blnInclude)
        {
            if (afcHandle == IntPtr.Zero)
                return;

            try
            {
                MobileDevice.AFCConnectionClose(afcHandle);

                if (blnInclude)
                {
                    int intKey = 0;
                    foreach (KeyValuePair<int, IntPtr> pair in this.mDictAfcSocketAndConnHandle)
                    {
                        if (pair.Value == afcHandle)
                        {
                            intKey = System.Convert.ToInt32(pair.Key);
                            break;
                        }
                    }

                    if (intKey > 0)
                        this.mDictAfcSocketAndConnHandle.Remove(intKey);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iPhoneDevice.CloseAFCConnection");
            }

            afcHandle = IntPtr.Zero;
        }

        public IntPtr OpenAFCConnection(int inSocket, bool blnInclude)
        {
            IntPtr hAFC = IntPtr.Zero;

            try
            {
                //以成功的连接方式去打开连接
                kAMDError inError = (kAMDError)(MobileDevice.AFCConnectionOpen(inSocket, 0, ref hAFC));
                if (inError == kAMDError.kAMDSuccess)
                {
                    ServiceInfo info = this.GetService(inSocket);
                    if (this.mConnectMode == ConnectMode.WIFI || info != null && info.IsSSL)
                    {
                        int iResult = MobileDevice.AFCConnectionSetSecureContext(hAFC, info.hIOContext);
                    }

                    if (blnInclude)
                        this.mDictAfcSocketAndConnHandle[inSocket] = hAFC;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iPhoneDevice.OpenAFCConnection");
            }

            return hAFC;
        }

        private void CreateAFC2()
        {
            if (this.mSocketAfc2 > 0) //OrElse Me.mConnectMode = Device.ConnectMode.WIFI
                return;

            if (this.StartSpringboardservices())
            {
                object objPlist = this.GetIconState();
                if (objPlist != null)
                    mIconStatePlist = CoreFoundation.CreatePlistString(objPlist);
            }

            //创建 com.apple.afc2 连接
            if (this.StartService("com.apple.afc2", ref this.mSocketAfc2, false))
            {
                ///System/Library/Lockdown/Services.plist
                this.mJailbreaked = true;

                mInstallCydia = 1;
                //用Socket去打开连接
                this.mHandleAfc2 = this.OpenAFCConnection(this.mSocketAfc2, true);

            }
            else
            {
                this.mJailbreaked = false;

                if (mIconStatePlist.Contains("com.saurik.Cydia"))
                    mInstallCydia = 1;
                else
                    mInstallCydia = 0;

                //If Me.mInstallCydia = -1 Then
                //    Dim img As System.Drawing.Image = Me.GetIconPNGData("com.saurik.Cydia")
                //    If img IsNot Nothing Then
                //        Me.mInstallCydia = True
                //        img.Dispose()

                //        mInstallCydia = 1
                //    Else

                //        mInstallCydia = 0
                //    End If
                //End If
            }

        }

        private void CreateAFC()
        {
            if (this.mSocketAfc > 0)
                return;

            //创建 com.apple.afc 连接
            if (this.StartService("com.apple.afc", ref this.mSocketAfc, false))
            {
                //用Socket去打开连接
                this.mHandleAfc = this.OpenAFCConnection(this.mSocketAfc, true);
            }
        }

        private bool CheckiTunesVersionIsTooLow()
        {
            bool blnResult = false;

            Version ver = null;
            if (this.mVersionNumber < 500)
            {
                ver = new Version("*********");
            }
            else if (this.mVersionNumber < 600)
            {
                ver = new Version("**********");
            }
            else if (this.mVersionNumber < 700)
            {
                ver = new Version("**********");
            }
            else if (OSHelper.OverWin7 && this.mProductType.StartsWith("iPhone12,", StringComparison.OrdinalIgnoreCase))
            {
                //ios13的用旧版本，有的功能无法管理
                ver = new Version("********");
            }
            else if (OSHelper.OverWin7 && this.mProductType.StartsWith("iPhone11,", StringComparison.OrdinalIgnoreCase))
            {
                // 12.x以上的小于********** 很容易管理失败
                ver = new Version("**********");
            }
            else if (OSHelper.OverWin7 && this.mVersionNumber >= 1000)
            {
                // 10.x以上的小于********* 很容易管理失败
                ver = new Version("*********");
            }
            else
            {
                //7.x以上的小于11.1的就没办法识别了
                ver = new Version("**********");
            }

            if (iTunesHelper.iTunesVersion < ver && Folder.AppType != RunType.AirDroidParentalConnector)
            {
                LanguageInterface lang = LanguageInterface.Instance();
#if !MAC && !IOS && !TOOLS
                System.Windows.Forms.MessageBox.Show(lang.GetString("Media.Message.DownloadNewVersioniTunes").Replace("10.5", ver.ToString(4)), lang.GetString("Common.Info"), System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Information);
                Common.OpenExplorer(WebUrl.PageReInstalliTunes);
#endif
                blnResult = true;
            }

            return blnResult;
        }

        internal bool ConnectToPhone()
        {
            bool blnRetry = false;
            bool blnResult = false;
            bool blnSetupComplete = false;
            kAMDError inError = kAMDError.kAMDUndefinedError;

            try
            {

            DoRetry:
                this.mConnectError = kAMDError.kAMDSuccess;

                inError = this.Connect();
                if (inError != kAMDError.kAMDSuccess)
                {
                    Common.LogException("inError != kAMDSuccess gotoexit", "ConnectToPhone");
                    goto DoExit;
                }

                this.mIsSessionActive = true;

                //初始化设备常用信息
                this.InitVariable();

                if (this.CheckiTunesVersionIsTooLow())
                {
                    Common.LogException("inError = kAMDiTunesVersionTooLow gotoexit", "ConnectToPhone");
                    this.mConnectError = kAMDError.kAMDiTunesVersionTooLow;
                    goto DoExit;
                }

                //iOS7检查iTunes是否已经设置完成
                blnSetupComplete = this.iTunesSetupComplete();

                inError = (kAMDError)(MobileDevice.AMDeviceValidatePairing(this.mDeviceRef));
                if (inError != kAMDError.kAMDSuccess)
                {
                    if (this.miTunes10_5)
                    {
                        Dictionary<object, object> dict = new Dictionary<object, object>();
                        dict.Add("ExtendedPairingErrors", true);

                        IntPtr ptrOption = CoreFoundation.CFTypeFromManagedType(dict);
                        inError = (kAMDError)(MobileDevice.AMDevicePairWithOptions(this.mDeviceRef, ptrOption));
                    }
                    else
                    {
                        inError = (kAMDError)(MobileDevice.AMDevicePair(this.mDeviceRef));
                    }

                    if (inError != kAMDError.kAMDSuccess)
                    {
                        Common.LogException("inError != kAMDSuccess gotodisconnect first", "ConnectToPhone");
                        //Console.WriteLine(Convert.ToString(CInt(inError), 16).ToUpper())
                        this.mConnectError = inError;
                        goto DoDisconnect;
                    }
                }

                this.mIsSessionActive = false;
                inError = this.StartSession(false);
                if (inError != kAMDError.kAMDSuccess)
                {
                    Common.LogException("inError != kAMDSuccess gotodisconnect second", "ConnectToPhone");
                    goto DoDisconnect;
                }

                //初始化设备扩展信息
                this.InitVariableEx();

                if (this.mConnectMode == ConnectMode.WIFI)
                {
                    //启动WIFI心跳
                    this.mWIFIHeartbeatThread = new Thread(new System.Threading.ThreadStart(WIFIHeartbeatThread));
                    this.mWIFIHeartbeatThread.IsBackground = true;
                    this.mWIFIHeartbeatThread.Start();
                }

                //创建AFC服务，在这之后才能初始化Jailbreak属性，即InitVariable中的iPhonePath
                this.CreateAFC2();
                this.CreateAFC();

                //先初始化AFC2服务后面，才对JailBreak属性进行赋值
                this.miPath = new iPhonePath(this, mVersionNumber, this.mJailbreaked, System.Convert.ToBoolean(mProductType.ToLower().Contains("ipad")));

                if (this.mSocketAfc <= 0 && !blnRetry)
                {
                    blnRetry = true;

                    this.StopSession();
                    this.Disconnect();

                    //AFC创建失败，等待1秒重新尝试连接
                    Utility.WaitSeconds(1);

                    Common.LogException("ConnectToPhone DoRetry", "ConnectToPhone");
                    goto DoRetry;
                }

                //建立消息代理
                if (this.StartService("com.apple.mobile.notification_proxy", ref this.mSocketNotifications, false))
                {
                    //监听设备上用户取消同步
                    MobileDevice.AMDObserveNotification(this.mSocketNotifications, CoreFoundation.StringToCFString("com.apple.itunes-client.syncCancelRequest"));

                    //监听设备上软件卸载
                    MobileDevice.AMDObserveNotification(this.mSocketNotifications, CoreFoundation.StringToCFString("com.apple.mobile.application_uninstalled"));

                    //建立用户取消同步后的回调
                    this.DeviceHandleEventSink = new DeviceEventSink(this.DeviceNotification);
                    MobileDevice.AMDListenForNotifications(this.mSocketNotifications, this.DeviceHandleEventSink, IntPtr.Zero);
                }
                else
                {
                    //Common.Log(String.Format("notification_proxy = {0}", "false"))
                }

                inError = (kAMDError)(MobileDevice.AMSInitialize(IntPtr.Zero));

                this.mIsConnected = true;
                blnResult = true;


                this.StopSession();

            DoDisconnect:
                this.mIsSessionActive = false;
                this.Disconnect();

                if (this.mHandleAfc2 == IntPtr.Zero)
                    this.mHandleAfc2 = this.mHandleAfc;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ConnectToPhone");
            }

        DoExit:
            return blnResult;
        }

        internal bool DisconnectFromPhone()
        {
            try
            {
                if (this.IsConnected)
                {
#if !IOS && !TOOLS
                    //如果正在授权，则释放正在授权的线程
                    AuthorizeHelper.GetInstance(this).CancelAuthorize();
#endif

                    //释放ATHost连接
                    this.ATHostConnectionRelease();

                    //释放已经创建的AFC连接
                    foreach (KeyValuePair<int, IntPtr> pair in this.mDictAfcSocketAndConnHandle)
                    {
                        IntPtr hAFC = pair.Value;
                        int iSocket = pair.Key;

                        this.CloseAFCConnection(ref hAFC, false);
                        this.StopService(ref iSocket);
                    }

                    //释放已经创建的HouseArrest
                    foreach (AfcInfo info in this.mDictHouseArrest.Values)
                    {
                        this.CloseAFCConnection(ref info.hAfc, false);
                        this.StopService(ref info.inSocket);
                    }

                    if (this.mDictInstalledApplications != null)
                    {
                        this.mDictInstalledApplications.Clear();
                        this.mDictInstalledApplications = null;
                    }

                    this.StopService(ref this.mSocketMobileSync);
                    this.StopService(ref this.mSocketNotifications);
                    this.StopService(ref this.mSocketSpringboardservices);
                    this.StopService(ref this.mSocketScreenshot);

                    this.CloseAFCConnection(ref this.mHandleCrashReportCopyMobile, true);
                    this.StopService(ref this.mSocketCrashReportCopyMobile);
                    this.StopService(ref this.mSocketMCInstall);
                    this.StopService(ref this.mSocketMisAgent);
                    this.StopService(ref this.mSocketSyslogRelay);

                    this.StopSession();
                    this.Disconnect();

                    Utility.AbortThread(this.mThreadReadMsg);
                    Utility.AbortThread(this.mWIFIHeartbeatThread);
                    Utility.AbortThread(this.mWIFIHeartbeatThread);

                    List<int> listKeys = new List<int>();
                    foreach (int socket in this.mDictSocketAndServiceInfo.Keys)
                        listKeys.Add(socket);

                    foreach (int socket in listKeys)
                    {
                        int iSck = socket;
                        this.StopService(ref iSck);
                    }

                    try
                    {
                        if (this.mDeviceRef != IntPtr.Zero && !miTunes12_5)
                            MobileDevice.AMDeviceRelease(this.mDeviceRef);
                    }
                    catch (Exception ex)
                    {
                        Common.LogException(ex.ToString(), "DisconnectFromPhone");
                    }

                    this.mIsConnected = false;
                }

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        private void WIFIHeartbeatThread()
        {
            try
            {
                while (true)
                {
                    int inskoet = 0;
                    if (this.StartService("com.apple.mobile.installation_proxy", ref inskoet, true))
                        this.StopService(ref inskoet);

                    Thread.Sleep(3000);
                }
            }
            catch
            { }
        }

        #endregion

        #region --- 内部函数 ---

        internal bool StartService(string serviceName, ref int serviceSocket, bool blnHeartbeat)
        {
            return StartService(serviceName, ref serviceSocket, blnHeartbeat, null);
        }

        internal bool StartService(string serviceName, ref int serviceSocket, bool blnHeartbeat, Dictionary<object, object> dictOption)
        {
            //Console.WriteLine(serviceName)

            bool blnResult = false;
            bool blnRetry = false;

            lock (mServiceLock)
            {
                if (serviceSocket > 0)
                {
                    blnResult = true;
                    Console.WriteLine(serviceName + "\t" + "StartService DO_EXIST A");
                    goto DoExit;
                }

                bool blnConnectBySelf = false;
                bool blnStartSessionBySelf = false;
                kAMDError inError = kAMDError.kAMDSuccess;

            DoConnect:
                if (!this.mIsConnectActive)
                {
                    inError = this.Connect();
                    if (inError == kAMDError.kAMDSuccess)
                    {
                        blnConnectBySelf = true;
                    }
                    else
                    {
                        Console.WriteLine(serviceName + "\t" + "StartService failed B");
                        goto DoExit;
                    }
                }

                if (!this.mIsSessionActive)
                {
                    inError = this.StartSession(blnHeartbeat);
                    if (inError == kAMDError.kAMDSuccess)
                    {
                        blnStartSessionBySelf = true;
                    }
                    else if (!blnRetry)
                    {
                        this.Disconnect();
                        Console.WriteLine(serviceName + "\t" + "StartService failed RETRY" + inError);
                        blnRetry = true;
                        goto DoConnect;
                    }
                    else
                    {
                        Console.WriteLine(serviceName + "\t" + "StartService failed A" + inError);
                        goto DoDisconnect;
                    }
                }


                IntPtr hService = IntPtr.Zero;
                IntPtr hOption = IntPtr.Zero;

                if (dictOption != null)
                {
                    dictOption.Add("CloseOnInvalidate", false);
                    hOption = CoreFoundation.CFTypeFromManagedType(dictOption);
                }

                if (this.miTunes10 && (kAMDError)MobileDevice.AMDeviceSecureStartService(this.mDeviceRef, CoreFoundation.StringToCFString(serviceName), hOption, ref hService) == kAMDError.kAMDSuccess)
                {
                    serviceSocket = MobileDevice.AMDServiceConnectionGetSocket(hService);
                    if (serviceSocket > 0)
                    {
                        if (!blnHeartbeat)
                        {
                            ServiceInfo info = new ServiceInfo();
                            info.hService = hService;
                            info.hIOContext = MobileDevice.AMDServiceConnectionGetSecureIOContext(hService);
                            info.iSocket = serviceSocket;

#if LOG
                            Common.LogException(string.Format("{0} is SSL {1}.", serviceName, info.IsSSL));
#endif

                            this.mDictSocketAndServiceInfo[serviceSocket] = info;
                        }

                        blnResult = true;
                    }
                }
                else
                {

                    inError = (kAMDError)(MobileDevice.AMDeviceStartService(this.mDeviceRef, CoreFoundation.StringToCFString(serviceName), ref serviceSocket, IntPtr.Zero));
                    if (inError == kAMDError.kAMDSuccess)
                        blnResult = true;
                }

                if (blnStartSessionBySelf)
                    this.StopSession();

                DoDisconnect:
                if (blnConnectBySelf)
                    this.Disconnect();
            }

        DoExit:
            return blnResult;
        }

        internal kAMDError StopService(ref int inSocket)
        {
            kAMDError inError = kAMDError.kAMDSuccess;

            if (inSocket > 0)
            {
                try
                {
                    inError = (kAMDError)(MobileDevice.closesocket(inSocket));

                    if (this.mDictSocketAndServiceInfo.ContainsKey(inSocket))
                        this.mDictSocketAndServiceInfo.Remove(inSocket);
                }
                catch
                { }
            }
            inSocket = 0;

            return inError;
        }

        private string FullPath(string path1, string path2)
        {
            string[] path_parts;
            string strReturn = "";

            if ((path1 == null) || (path1 == string.Empty))
                path1 = "/";

            if ((path2 == null) || (path2 == string.Empty))
                path2 = "/";

            if (path2[0] == '/')
            {
                path_parts = path2.Split('/'.ToString().ToCharArray());
            }
            else if (path1[0] == '/')
            {
                path_parts = (path1 + "/" + path2).Split('/');
            }
            else
            {
                path_parts = ("/" + path1 + "/" + path2).Split('/');
            }

            string[] result_parts = new string[path_parts.Length - 1 + 1];
            int target_index = 0;
            for (int i = 0; i <= path_parts.Length - 1; i++)
            {

                if (path_parts[i] == "..")
                {
                    if (target_index > 0)
                        target_index--;

                    //Do nothing
                }
                else if ((path_parts[i] == ".") || (path_parts[i] == ""))
                {

                }
                else
                {
                    target_index++;
                    result_parts[target_index] = path_parts[i];
                }
            }

            strReturn = "/" + string.Join("/", result_parts, 0, target_index + 1);
            strReturn = strReturn.Replace("//", "/");

            return strReturn;
        }

        #endregion

        #region --- 工程内共享属性 ---

        public IntPtr DeviceRef
        {
            get
            {
                return this.mDeviceRef;
            }
        }

        internal IntPtr AfcHandle
        {
            get
            {
                return this.mHandleAfc2;
            }
        }

        #endregion

        #region - 装机大师扩展属性 -

        private string mOpenUDID = string.Empty;
        private string mZJ_IDFV = string.Empty;
        private string mZJ_IDFA = string.Empty;

        public string ZJ_OpenUDID
        {
            get
            {
                return this.mOpenUDID;
            }
            set
            {
                this.mOpenUDID = value;
            }
        }

        public string ZJ_IDFV
        {
            get
            {
                return this.mZJ_IDFV;
            }
            set
            {
                this.mZJ_IDFV = value;
            }
        }

        public string ZJ_IDFA
        {
            get
            {
                return this.mZJ_IDFA;
            }
            set
            {
                this.mZJ_IDFA = value;
            }
        }

        #endregion

        #region --- 对外属性 ---

        public string DeviceColorBg
        {
            get
            {
                return this.mDevColorBg;
            }
        }

        public string IconStatePlist
        {
            get
            {
                return this.mIconStatePlist;
            }
        }

        public string OriginalDeviceName
        {
            get
            {
                return this.mOriginalDeviceName;
            }
        }

        public Dictionary<DeviceCapacityType, long> DictCapacity
        {
            get
            {
                return this.mDictCapacity;
            }
        }

        public Dictionary<DeviceCapacityType, int> DictCapacityCount
        {
            get
            {
                return this.mDictCapacityCount;
            }
        }

        public string ImagePathEx
        {
            get
            {
                if (mImagePathEx == null)
                {
                    mImagePathEx = string.Empty;

                    string strLow = this.ProductType.ToLower();
                    if (strLow.StartsWith("iphone"))
                    {
                        int ver = System.Convert.ToInt32(Common.GetDigit(strLow.Replace("iphone", "").Replace(",", "")));
                        if (ver >= 31)
                            mImagePathEx = "@2x";
                    }
                    else if (strLow.StartsWith("ipad"))
                    {
                        int ver = System.Convert.ToInt32(Common.GetDigit(strLow.Replace("ipad", "").Replace(",", "")));
                        if (ver >= 31)
                            mImagePathEx = "@2x";
                    }
                    else if (strLow.StartsWith("ipod"))
                    {
                        int ver = System.Convert.ToInt32(Common.GetDigit(strLow.Replace("ipod", "").Replace(",", "")));
                        if (ver >= 41)
                            mImagePathEx = "@2x";
                    }
                }

                return mImagePathEx;
            }
        }

        public ConnectMode ConnectMode
        {
            get
            {
                return this.mConnectMode;
            }
        }

        public bool iTunes82
        {
            get
            {
                return this.miTunes82;
            }
        }

        public bool InstallCydia
        {
            get
            {
                return System.Convert.ToBoolean(mInstallCydia);
            }
        }

        public ATHostMessage ATHostMessage
        {
            get
            {
                return this.mATHostMsg;
            }
        }

        public IntPtr DefaultAfcHandle
        {
            get
            {
                return this.mHandleAfc2;
            }
        }

        public IntPtr MobileAfcHandle
        {
            get
            {
                return this.mHandleAfc;
            }
        }

        public iPhonePath iPath
        {
            get
            {
                return this.miPath;
            }
        }

        public string DeviceName
        {
            get
            {
                if (string.IsNullOrEmpty(this.mDeviceName))
                {
                    this.mDeviceName = this.GetDeviceValue(DeviceInfoKey.DeviceName) + "";
                }

                return this.mDeviceName;
            }
            set
            {
                if (!string.IsNullOrEmpty(value) && value != this.mDeviceName)
                {
                    if (this.SetDeviceValue(DeviceInfoKey.DeviceName, value))
                    {
                        this.mDeviceName = value;
                        if (DeviceNameChanged != null)
                            DeviceNameChanged(this, EventArgs.Empty);
                    }
                }
            }
        }

        public string DeviceID
        {
            get
            {
                return this.Identifier;
            }
        }

        public DeviceType DeviceType
        {
            get
            {
                return DeviceType.iOS;
            }
        }

        public string ProductType
        {
            get
            {
                if (string.IsNullOrEmpty(this.mProductType))
                    this.mProductType = this.GetDeviceValue(DeviceInfoKey.ProductType) + "";

                return this.mProductType;
            }
        }

        public string ProductVersion
        {
            get
            {
                if (string.IsNullOrEmpty(this.mProductVersion))
                    this.mProductVersion = this.GetDeviceValue(DeviceInfoKey.ProductVersion) + "";

                return this.mProductVersion;
            }
        }

        public int VersionNumber
        {
            get
            {
                return this.mVersionNumber;
            }
        }

        public string PartitionType
        {
            get
            {
                if (string.IsNullOrEmpty(this.mPartitionType))
                    this.mPartitionType = this.GetDeviceValue(DeviceInfoKey.PartitionType) + "";

                return this.mPartitionType;
            }
        }

        public string Identifier
        {
            get
            {
                if (string.IsNullOrEmpty(this.mIdentifier))
                {
                    IntPtr indentifierPtr = IntPtr.Zero;

                    try
                    {
                        indentifierPtr = MobileDevice.AMDeviceCopyDeviceIdentifier(this.mDeviceRef);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex.Message);
                    }
                    finally
                    {
                        if (indentifierPtr != IntPtr.Zero)
                        {
                            this.mIdentifier = CoreFoundation.ManagedTypeFromCFType(indentifierPtr).ToString();
                            CoreFoundation.CFRelease(indentifierPtr);
                        }
                    }
                }

                return this.mIdentifier;
            }
        }

        public string SerialNumber
        {
            get
            {
                if (string.IsNullOrEmpty(this.mSerialNumber))
                    this.mSerialNumber = this.GetDeviceValue(DeviceInfoKey.SerialNumber) + "";

                return this.mSerialNumber;
            }
        }

        public string UniqueDeviceID
        {
            get
            {
                if (string.IsNullOrEmpty(this.mUniqueDeviceID))
                    this.InitUniqueDeviceID();

                return this.mUniqueDeviceID;
            }
        }

        public string UniqueDeviceID_New
        {
            get
            {
                if (string.IsNullOrEmpty(this.mUniqueDeviceID_New))
                    this.InitUniqueDeviceID();

                return this.mUniqueDeviceID_New;
            }
        }

        public int TimeZoneOffsetFromUTC
        {
            get
            {
                return mTimeZoneOffsetFromUTC;
            }
        }

        public bool IsConnected
        {
            get
            {
                return this.mIsConnected;
            }
        }

        public bool Jailbreaked
        {
            get
            {
                return this.mJailbreaked;
            }
        }

        public PHBDeviceColorVariation DeviceColor
        {
            get
            {
                //获取手机颜色
                if (this.mDeviceColor == PHBDeviceColorVariation.Default)
                    this.InitColor();

                return mDeviceColor;
            }
        }

        public bool HasApplicationCache
        {
            get
            {
                string pathOnPhone = "/PublicStaging/";
                if (this.ExistsByAFC(pathOnPhone))
                {
                    return (this.GetFilesByAFC(pathOnPhone, true).Length > 0);
                }
                else
                {
                    return false;
                }
            }
        }

        public string WiFiAddress
        {
            get
            {
                if (string.IsNullOrEmpty(this.mWiFiAddress))
                    this.mWiFiAddress = this.GetDeviceValue(DeviceInfoKey.WiFiAddress) + "";

                return this.mWiFiAddress;
            }
        }

        public string BluetoothAddress
        {
            get
            {
                if (string.IsNullOrEmpty(this.mBluetoothAddress))
                    this.mBluetoothAddress = this.GetDeviceValue(DeviceInfoKey.BluetoothAddress) + "";

                return this.mBluetoothAddress;
            }
        }

        public string UniqueChipID
        {
            get
            {
                if (string.IsNullOrEmpty(this.mUniqueChipID))
                    this.mUniqueChipID = this.GetDeviceValue(DeviceInfoKey.UniqueChipID) + "";

                return this.mUniqueChipID;
            }
        }

        public string IMEI
        {
            get
            {
                if (string.IsNullOrEmpty(this.mIMEI))
                    this.mIMEI = this.GetDeviceValue(DeviceInfoKey.InternationalMobileEquipmentIdentity) + "";

                return this.mIMEI;
            }
        }

        public string PhoneNumber
        {
            get
            {
                if (this.mPhoneNumber == null)
                    this.mPhoneNumber = this.GetDeviceValue(DeviceInfoKey.PhoneNumber) + "";

                return this.mPhoneNumber;
            }
        }

        public string ICCID
        {
            get
            {
                if (string.IsNullOrEmpty(this.mICCID))
                    this.mICCID = this.GetDeviceValue(DeviceInfoKey.IntegratedCircuitCardIdentity) + "";

                return this.mICCID;
            }
        }

        public long FairPlayDeviceType
        {
            get
            {
                try
                {
                    if (this.mFairPlayDeviceType <= 0)
                    {
                        object objValue = this.GetDeviceValue("com.apple.mobile.iTunes", "FairPlayDeviceType");
                        if (objValue != null)
                            this.mFairPlayDeviceType = Convert.ToInt64(objValue.ToString());
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "DeviceType");
                }
                return this.mFairPlayDeviceType;
            }
            internal set
            {
                this.mFairPlayDeviceType = value;
            }
        }

        public byte[] FairPlayCertificate
        {
            get
            {
                try
                {
                    if (this.mFairPlayCertificate == null)
                    {
                        object objValue = this.GetDeviceValue("com.apple.mobile.iTunes", "FairPlayCertificate");
                        if (objValue != null)
                            this.mFairPlayCertificate = (byte[])(objValue);
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "Certificate");
                }
                return this.mFairPlayCertificate;
            }
            internal set
            {
                this.mFairPlayCertificate = value;
            }
        }

        public string FairPlayGUID
        {
            get
            {
                try
                {
                    if (this.mFairPlayGUID == null)
                    {
                        //object objValue1 = this.GetDeviceValue("com.apple.mobile.iTunes", null);
                        //if (objValue1 != null)
                        //    Console.WriteLine(CoreFoundation.CreatePlistString(objValue1));

                        object objValue = this.GetDeviceValue("com.apple.mobile.iTunes", "FairPlayGUID");
                        if (objValue != null)
                            this.mFairPlayGUID = (string)(objValue);
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "Certificate");
                }
                return this.mFairPlayGUID;
            }
            internal set
            {
                this.mFairPlayGUID = value;
            }
        }

        public long KeyTypeSupportVersion //mKeyTypeSupportVersion
        {
            get
            {
                try
                {
                    if (this.mKeyTypeSupportVersion == 0)
                    {
                        object objValue = this.GetDeviceValue("com.apple.mobile.iTunes", "KeyTypeSupportVersion");
                        if (objValue != null)
                            this.mKeyTypeSupportVersion = Convert.ToInt64(objValue.ToString());
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "SupportVersion");
                }
                return this.mKeyTypeSupportVersion;
            }
            internal set
            {
                this.mKeyTypeSupportVersion = value;
            }
        }

        public string CPUArchitecture
        {
            get
            {
                if (string.IsNullOrEmpty(this.mCPUArchitecture))
                    this.mCPUArchitecture = this.GetDeviceValue(DeviceInfoKey.CPUArchitecture) + "";

                return this.mCPUArchitecture;
            }
        }

        public bool IsIPad
        {
            get
            {
                if (!this.mHasGetIsIPad)
                {
                    this.mIsIPad = this.ProductType.ToLower().StartsWith("ipad");
                }

                return this.mIsIPad;
            }
        }

        public string BuildVersion
        {
            get
            {
                if (string.IsNullOrEmpty(this.mBuildVersion))
                    this.mBuildVersion = this.GetDeviceValue(DeviceInfoKey.BuildVersion) + "";

                return this.mBuildVersion;
            }
        }

        public bool FindMyPhone
        {
            get
            {
                bool blnIsAssociated = false;

                string IsAssociated = this.GetDeviceValue("com.apple.fmip", "IsAssociated") + "";
                if (!string.IsNullOrEmpty(IsAssociated))
                    bool.TryParse(IsAssociated, out blnIsAssociated);

                return blnIsAssociated;
            }
        }

        public string IDFA()
        {
            return this.IDFA(false);
        }

        public string IDFA(bool reload)
        {
            // 防止设备上没有安装同步推或者同步推没有打开的时候， 会频繁访问
            if (mIDFA != null && !reload)
            {
                goto DoExit;
            }

            this.AnalyseTuiDPlist();

        DoExit:
            return this.mIDFA;
        }

        public string IDFV(bool reload)
        {
            if (mIDFA != null && !reload)
            {
                goto DoExit;
            }

            this.AnalyseTuiDPlist();

        DoExit:
            return this.mIDFV;
        }

        public DateTime TuiEnterpriseShowDate(bool reload)
        {
            if (mTuiEnterpriseShowDate != DateTime.MinValue && !reload)
            {
                goto DoExit;
            }

            this.AnalyseTuiDPlist();

        DoExit:
            return this.mTuiEnterpriseShowDate;
        }

        private void AnalyseTuiDPlist()
        {
            lock (mlockIDFA)
            {
                if (mIDFA != null)
                    return;

                try
                {
                    Dictionary<string, FileSharingPackageInfo> dict = this.InstalledApplications(ApplicationType.Any, false);
                    if (dict.Count == 0)
                        return;

                    foreach (FileSharingPackageInfo info in dict.Values)
                    {
                        if (!info.IsTui)
                            continue;

                        string strRoot = string.Empty;
                        string bundleId = info.Identifier;
                        IntPtr hAFC = IntPtr.Zero;

                        if (this.CheckUIFileSharingEnabled(info.Identifier))
                        {
                            hAFC = this.GetAfcByFileSharing(bundleId, HouseArrestType.Internal, true);

                            if (hAFC != IntPtr.Zero)
                                goto DO_NEXT;
                        }

                        if (hAFC == IntPtr.Zero && this.Jailbreaked && this.StartTongbuFairy())
                        {
                            hAFC = this.DefaultAfcHandle;
                            strRoot = this.GetAppContainerPath(bundleId);
                        }

                    DO_NEXT:
                        string strPathOnPC = Folder.GetTempFilePath();
                        if (this.DownFromPhone(hAFC, strRoot + "/Documents/Tongbu/d.plist", strPathOnPC))
                        {

                        }
                        else if (this.DownFromPhone(hAFC, strRoot + "/Library/tongbu/d.plist", strPathOnPC))
                        {
                        }
                        else if (this.DownFromPhone(hAFC, strRoot + "/Documents/d.plist", strPathOnPC))
                        {

                        }
                        else
                        {
                            Common.Log(string.Format("DeviceIDFA:{0}", "File not found."));
                            continue;
                        }

                        if (!Common.DecryptDESFile(strPathOnPC, strPathOnPC))
                            continue;

                        Dictionary<object, object> dictDPlist = CoreFoundation.ReadPlist_managed(strPathOnPC) as Dictionary<object, object>;
                        if (dictDPlist == null)
                            continue;

                        if (dictDPlist.ContainsKey("advertisingIdentifier"))
                            this.mIDFA = dictDPlist["advertisingIdentifier"].ToString();

                        if (dictDPlist.ContainsKey("identifierForVendor"))
                            this.mIDFV = dictDPlist["identifierForVendor"].ToString();

                        if (info.CrackedInfo == CrakedInfo.Enterprise && dictDPlist.ContainsKey("updateTuiForEnterpriseDate"))
                        {
                            try
                            {
                                this.mTuiEnterpriseShowDate = Convert.ToDateTime(dictDPlist["updateTuiForEnterpriseDate"].ToString());
                            }
                            catch (Exception ex)
                            {
                                Common.LogException(ex.ToString(), "updateTuiForEnterpriseDate_Error");
                            }
                        }
                        Common.Log(string.Format("DeviceIDFA:{0}", this.mIDFA));
                    }
                    dict.Clear();

                    // 如果获取失败的话， 则不再继续获取
                    if (this.mIDFA == null)
                    {
                        this.mIDFA = "";
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "AnalyseTuiDPlist");
                }
            }
        }

        public bool IsLivePhotoDevice
        {
            get
            {
                //判断当前设备是不是livePhoto的设备
                if (!this.mHasGetIsLivePhotoDevice)
                {
                    if (this.ProductType.Contains("iPhone") && Convert.ToInt32(this.ProductType.Substring(6, 1)) > 7)
                    {
                        this.mIsLivePhotoDevice = true;
                    }
                    else if (this.VersionNumber >= 910)
                    {
                        this.mIsLivePhotoDevice = true;
                    }
                    this.mHasGetIsLivePhotoDevice = true;
                }
                return this.mIsLivePhotoDevice;
            }
        }

        #endregion

        #region --- Get/Set Value ---

        public bool CheckUDIDIsFake()
        {
            string strInfo = this.SerialNumber + this.UniqueChipID + this.WiFiAddress + this.BluetoothAddress;
            string udid = Common.GetSha1Hex(strInfo);

            //Console.WriteLine("UDID: " + udid);

            return string.Compare(udid, this.mIdentifier) != 0;
        }

        public string GetDeviceInfo(DeviceInfoType key)
        {
            string strReturn = string.Empty;
            IntPtr info = IntPtr.Zero;

            kAMDError inError = kAMDError.kAMDSuccess;

            inError = (kAMDError)(MobileDevice.AFCDeviceInfoOpen(this.mHandleAfc2, ref info));
            if (inError == kAMDError.kAMDSuccess && info != IntPtr.Zero)
            {

                while (true)
                {
                    string name = string.Empty;
                    string value = string.Empty;

                    IntPtr intName = IntPtr.Zero;
                    IntPtr intValue = IntPtr.Zero;

                    if (MobileDevice.AFCKeyValueRead(info, ref intName, ref intValue) != 0)
                        break;

                    name = Marshal.PtrToStringAnsi(intName);
                    value = Marshal.PtrToStringAnsi(intValue);

                    if (string.IsNullOrEmpty(name))
                    {
                        break;
                    }

                    if (name.ToLower() == key.ToString().ToLower())
                    {
                        strReturn = value;
                        break;
                    }
                }

                MobileDevice.AFCKeyValueClose(info);
            }

            return strReturn;
        }

        public object GetDeviceValue(DeviceInfoKey key)
        {
            return this.GetDeviceValue(null, key);
        }

        public object GetDeviceValue(string key)
        {
            return this.GetDeviceValue(null, key);
        }

        public object GetDeviceValue(string domain, DeviceInfoKey key)
        {
            return this.GetDeviceValue(domain, key.ToString());
        }

        public object GetDeviceValue(string domain, string key)
        {
            object returnValue = null;

            try
            {
                bool blnConnectBySelf = false;
                bool blnStartSessionBySelf = false;
                kAMDError inError = kAMDError.kAMDSuccess;

                if (!this.mIsConnectActive)
                {
                    inError = this.Connect();
                    if (inError == kAMDError.kAMDSuccess)
                    {
                        blnConnectBySelf = true;
                    }
                    else
                    {
                        goto DoExit;
                    }
                }

                if (!this.mIsSessionActive)
                {
                    inError = this.StartSession(false);
                    if (inError == kAMDError.kAMDSuccess)
                    {
                        blnStartSessionBySelf = true;
                    }
                    else
                    {
                        goto DoDisconnect;
                    }
                }

                //returnValue = MobileDevice.AMDeviceCopyValue(Me.mDeviceRef, domain, "BasebandGoldCertId")
                returnValue = MobileDevice.AMDeviceCopyValue(this.mDeviceRef, domain, key);


                if (blnStartSessionBySelf)
                    this.StopSession();

                DoDisconnect:
                if (blnConnectBySelf)
                    this.Disconnect();

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iPhoneDevice.GetDeviceValue");
            }

        DoExit:
            return returnValue;
        }

        public bool SetDeviceValue(DeviceInfoKey key, object objValue)
        {
            return this.SetDeviceValue(key.ToString(), objValue);
        }

        public bool SetDeviceValue(string strKey, object objValue)
        {
            return this.SetDeviceValue(null, strKey, objValue);
        }

        public bool SetDeviceValue(string strDomain, string strKey, object objValue)
        {
            bool blnResult = false;
            try
            {
                bool blnConnectBySelf = false;
                bool blnStartSessionBySelf = false;
                kAMDError inError = kAMDError.kAMDSuccess;

                if (!this.mIsConnectActive)
                {
                    inError = this.Connect();
                    if (inError == kAMDError.kAMDSuccess)
                    {
                        blnConnectBySelf = true;
                    }
                    else
                    {
                        goto DoExit;
                    }
                }

                if (!this.mIsSessionActive)
                {
                    inError = this.StartSession(false);
                    if (inError == kAMDError.kAMDSuccess)
                    {
                        blnStartSessionBySelf = true;
                    }
                    else
                    {
                        goto DoDisconnect;
                    }
                }

                blnResult = MobileDevice.AMDeviceSetValue(this.mDeviceRef, strDomain, strKey, objValue);

                if (blnStartSessionBySelf)
                    this.StopSession();

                DoDisconnect:
                if (blnConnectBySelf)
                    this.Disconnect();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iPhoneDevice.SetDeviceValue");
            }

        DoExit:
            return blnResult;
        }

        public bool RemoveDeviceValue(string strDomain, string strKey)
        {
            bool blnResult = false;
            try
            {
                bool blnConnectBySelf = false;
                bool blnStartSessionBySelf = false;
                kAMDError inError = kAMDError.kAMDSuccess;

                if (!this.mIsConnectActive)
                {
                    inError = this.Connect();
                    if (inError == kAMDError.kAMDSuccess)
                    {
                        blnConnectBySelf = true;
                    }
                    else
                    {
                        goto DoExit;
                    }
                }

                if (!this.mIsSessionActive)
                {
                    inError = this.StartSession(false);
                    if (inError == kAMDError.kAMDSuccess)
                    {
                        blnStartSessionBySelf = true;
                    }
                    else
                    {
                        goto DoDisconnect;
                    }
                }

                blnResult = MobileDevice.AMDeviceRemoveValue(this.mDeviceRef, strDomain, strKey);

                if (blnStartSessionBySelf)
                    this.StopSession();

                DoDisconnect:
                if (blnConnectBySelf)
                    this.Disconnect();

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iPhoneDevice.RemoveDeviceValue");
            }

        DoExit:
            return blnResult;
        }

        #endregion

        #region --- 容量、电池 ---

        /// ===================Added on 2011.04.20=========================
        //' 	TotalDataAvailable = 70			'' 可用数据总容量



        //'  TotalDataCapacity = 71			'' 数据总容量



        //'  TotalDiskCapacity = 72			'' 设备总容量



        //'  TotalSystemAvailable = 73		'' 系统可用总容量



        //'  TotalSystemCapacity = 74		'' 系统总容量



        //'  AmountDataReserved = 75			'' 保留数据量



        //'  AmountDataAvailable = 76		'' 可用数据量



        //'  MobileApplicationUsage = 77		''手机应用程序容量
        private void InitCapacity(object objInfo)
        {
            if (objInfo == null || objInfo is string && string.IsNullOrEmpty(objInfo.ToString()))
            {
                return;
            }

            mDictCapacity.Clear();

            //Console.WriteLine(CoreFoundation.CreatePlistString(objInfo))
            foreach (KeyValuePair<object, object> pair in (IEnumerable)objInfo)
            {
                //Console.WriteLine(pair.Key & vbTab & pair.Value)
                if (pair.Key.ToString() == "TotalDiskCapacity")
                {
                    mDictCapacity[DeviceCapacityType.TotalDiskCapacity] = Convert.ToInt64(pair.Value);
                }
                else if (pair.Key.ToString() == "TotalSystemCapacity")
                {
                    mDictCapacity[DeviceCapacityType.TotalSystemCapacity] = Convert.ToInt64(pair.Value);
                }
                else if (pair.Key.ToString() == "TotalDataCapacity")
                {
                    mDictCapacity[DeviceCapacityType.TotalDataCapacity] = Convert.ToInt64(pair.Value);
                }
                else if (pair.Key.ToString() == "TotalSystemAvailable")
                {
                    mDictCapacity[DeviceCapacityType.TotalSystemAvailable] = Convert.ToInt64(pair.Value);
                }
                else if (pair.Key.ToString() == "TotalDataAvailable")
                {
                    mDictCapacity[DeviceCapacityType.TotalDataAvailable] = Convert.ToInt64(pair.Value);
                }
                else if (pair.Key.ToString() == "MobileApplicationUsage")
                {
                    mDictCapacity[DeviceCapacityType.MobileApplicationUsage] = Convert.ToInt64(pair.Value);
                }
                else if (pair.Key.ToString() == "AmountDataReserved")
                {
                    mDictCapacity[DeviceCapacityType.AmountDataReserved] = Convert.ToInt64(pair.Value);
                }
                else if (pair.Key.ToString() == "AmountDataAvailable")
                {
                    mDictCapacity[DeviceCapacityType.AmountDataAvailable] = Convert.ToInt64(pair.Value);
                }
            }

        }

        private void InitCapacity(Dictionary<object, object> dictPlist)
        {
            try
            {
                if (dictPlist == null || !dictPlist.ContainsKey("Params"))
                    return;

                this.mDictCapacity.Clear();
                this.mDictCapacityCount.Clear();

                Dictionary<object, object> dictParams = dictPlist["Params"] as Dictionary<object, object>;
                Dictionary<object, object> dictCapacityInfo;

                this.GetDataCapacityInfo(dictParams, "_FreeSize", DeviceCapacityType.TotalDataAvailable); //可用数据总容量				
                this.GetDataCapacityInfo(dictParams, "_PhysicalSize", DeviceCapacityType.TotalDataCapacity); //数据总容量

                this.GetCapacityInfo(dictParams, "Application", DeviceCapacityType.MobileApplicationUsage); //应用程序
                this.GetCapacityInfo(dictParams, "Book", DeviceCapacityType.EBookCapacity); //书籍
                this.GetCapacityInfo(dictParams, "Ringtone", DeviceCapacityType.RingtoneCapacity); //铃声
                this.GetCapacityInfo(dictParams, "UserData", DeviceCapacityType.UserDataCapacity); //文档与数据
                this.GetCapacityInfo(dictParams, "VoiceMemo", DeviceCapacityType.VoiceMemoCapacity); //语音备忘录								

                string strTypeKey = "Media";
                if (dictParams.ContainsKey(strTypeKey))
                {
                    dictCapacityInfo = dictParams[strTypeKey] as Dictionary<object, object>;

                    //音频
                    this.GetCapacityInfo(dictCapacityInfo, "Music", DeviceCapacityType.AudioCapacity); //音乐
                    this.GetCapacityInfo(dictCapacityInfo, "Audiobook", DeviceCapacityType.AudiobookCapacity); //有声读物
                    this.GetCapacityInfo(dictCapacityInfo, "Podcast", DeviceCapacityType.PodcastCapacity); //Podcast音频

                    //视频
                    this.GetCapacityInfo(dictCapacityInfo, "Movie", DeviceCapacityType.VideoCapacity); //视频
                    this.GetCapacityInfo(dictCapacityInfo, "MusicVideo", DeviceCapacityType.MusicVideoCapacity); //音乐视频
                    this.GetCapacityInfo(dictCapacityInfo, "TVEpisode", DeviceCapacityType.TVEpisodeCapacity); //TV Show
                    this.GetCapacityInfo(dictCapacityInfo, "VideoPodcast", DeviceCapacityType.VideoPodcastCapacity); //Podcast视频
                    this.GetCapacityInfo(dictCapacityInfo, "iTunesUVideo", DeviceCapacityType.iTunesUVideoCapacity); //iTunesU
                }

                strTypeKey = "Photo";
                if (dictParams.ContainsKey(strTypeKey))
                {
                    dictCapacityInfo = dictParams[strTypeKey] as Dictionary<object, object>;

                    this.GetCapacityInfo(dictCapacityInfo, "CameraRoll", DeviceCapacityType.CameraCapacity); //照相机
                    this.GetCapacityInfo(dictCapacityInfo, "Photo", DeviceCapacityType.PhotoCapacity); //图库
                }

                if (CapacityLoadCompleted != null)
                    CapacityLoadCompleted(this, new DeviceCapacityArgs(this.Identifier, this.mDictCapacity, this.mDictCapacityCount));
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iPhoneDevice.InitCapacity");
            }
        }

        //获取系统的数据容量
        private void GetDataCapacityInfo(Dictionary<object, object> dictCapacityInfo, string strTypeKey, DeviceCapacityType capacityType)
        {
            if (dictCapacityInfo == null)
                return;

            if (dictCapacityInfo.ContainsKey(strTypeKey))
            {
                //Console.WriteLine(strTypeKey & vbTab & capacityType.ToString() & vbTab & dictCapacityInfo(strTypeKey).ToString())
                this.mDictCapacity[capacityType] = Convert.ToInt64(dictCapacityInfo[strTypeKey]);
            }
        }

        //获取资料的容量信息（大小和数量）
        private void GetCapacityInfo(Dictionary<object, object> dictCapacityInfo, string strTypeKey, DeviceCapacityType capacityType)
        {
            if (dictCapacityInfo == null)
                return;

            string strSizeKey = "_PhysicalSize";
            string strCountKey = "_Count";
            Dictionary<object, object> dictType;

            if (dictCapacityInfo.ContainsKey(strTypeKey))
            {
                dictType = dictCapacityInfo[strTypeKey] as Dictionary<object, object>;

                if (dictType.ContainsKey(strSizeKey))
                {
                    //Console.WriteLine(strTypeKey & vbTab & capacityType.ToString() & vbTab & dictType(strSizeKey).ToString())
                    this.mDictCapacity[capacityType] = Convert.ToInt64(dictType[strSizeKey]);
                }

                if (dictType.ContainsKey(strCountKey))
                {
                    //Console.WriteLine(strTypeKey & vbTab & capacityType.ToString() & vbTab & dictType(strCountKey).ToString())
                    this.mDictCapacityCount[capacityType] = Convert.ToInt32(dictType[strCountKey]);

                    //有些数量为0，但是大小不等于0
                    if (this.mDictCapacityCount[capacityType] == 0)
                    {
                        this.mDictCapacity[capacityType] = 0;
                    }
                }
            }
        }

        public long GetDeviceUsageCapacity(DeviceCapacityType devKey)
        {

            long retValue = 0;

            try
            {
                bool connectBySelf = false;

                if (mIsSessionActive == false)
                {
                    this.Connect();
                    this.StartSession(false);
                    connectBySelf = true;
                }

                if (mIsSessionActive)
                {
                    object objRet = MobileDevice.AMDeviceCopyValue(this.mDeviceRef, "com.apple.disk_usage", devKey.ToString());
                    //objRet = MobileDevice.AMDeviceCopyValue(Me.mDeviceRef, "com.apple.disk_usage", Nothing)
                    if (objRet != null)
                        retValue = Convert.ToInt64(objRet);
                }

                //如果是自己启动的连接，要自己关闭
                if (connectBySelf)
                {
                    this.StopSession();
                    this.Disconnect();
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iPhoneDevice.GetDeviceUsageCapacity");
            }

            return retValue;
        }

        public object GetDeviceUsageCapacity(bool blnRefresh)
        {
            object objRet = null;

            try
            {
                bool connectBySelf = false;
                if (mIsSessionActive == false)
                {
                    this.Connect();
                    this.StartSession(false);
                    connectBySelf = true;
                }

                if (mIsSessionActive)
                {
                    if (blnRefresh)
                    {
                        objRet = MobileDevice.AMDeviceCopyValue(this.mDeviceRef, "com.apple.disk_usage", "CalculateDiskUsage");
                        Utility.WaitSeconds(0.5);
                    }

                    objRet = MobileDevice.AMDeviceCopyValue(this.mDeviceRef, "com.apple.disk_usage", null);

                    //if (objRet != null)
                    //	Console.WriteLine("com.apple.disk_usage\r\n" + CoreFoundation.CreatePlistString(objRet));

                    this.InitCapacity(objRet);
                }

                //如果是自己启动的连接，要自己关闭
                if (connectBySelf)
                {
                    this.StopSession();
                    this.Disconnect();
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iPhoneDevice.GetDeviceUsageCapacity.blnRefresh");
            }

            return objRet;
        }

        public int BatteryCurrentCapacity()
        {
            int returnValue = -1;

            try
            {
                object objValue = this.GetDeviceValue("com.apple.mobile.battery", "BatteryCurrentCapacity");
                if (objValue != null)
                    returnValue = Convert.ToInt32(objValue);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iPhoneDevice.BatteryCurrentCapacity");
            }

            return returnValue;
        }

        public bool BatteryIsCharging()
        {
            //插上 USB 就是在充电，所以默认返回值就是 True
            bool returnValue = true;

            try
            {
                object objValue = this.GetDeviceValue("com.apple.mobile.battery", "BatteryIsCharging");
                if (objValue != null && objValue is bool)
                    returnValue = System.Convert.ToBoolean(objValue);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iPhoneDevice.BatteryIsCharging");
            }

            return returnValue;
        }

        #endregion

        #region --- 正在同步 ---

        public bool CheckIsSync()
        {
            return (this.syncLockdown != null);
        }

        public void StartSync()
        {
            try
            {
                if (this.syncLockdown == null)
                {
                    IntPtr serviceNamePtr = CoreFoundation.StringToCFString("com.apple.itunes-mobdev.syncWillStart");
                    kAMDError inError = (kAMDError)(MobileDevice.AMDPostNotification(this.mSocketNotifications, serviceNamePtr, 0));
                    CoreFoundation.CFRelease(serviceNamePtr);
                    System.Threading.Thread.Sleep(200);

                    this.syncLockdown = iPhoneFile.OpenWrite(this, "/com.apple.itunes.lock_sync");

                    System.Threading.Thread.Sleep(50);

                    serviceNamePtr = CoreFoundation.StringToCFString("com.apple.itunes-mobdev.syncLockRequest");
                    inError = (kAMDError)(MobileDevice.AMDPostNotification(this.mSocketNotifications, serviceNamePtr, 0));
                    CoreFoundation.CFRelease(serviceNamePtr);
                    System.Threading.Thread.Sleep(200);

                    this.syncLockdown.Lock(40);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
        }

        public void EndSync()
        {
            try
            {
                if (this.syncLockdown != null)
                {
                    this.syncLockdown.Unlock();
                    this.syncLockdown.Close();
                }

                this.syncLockdown = null;
                IntPtr serviceNamePtr = CoreFoundation.StringToCFString("com.apple.itunes-mobdev.syncDidFinish");
                kAMDError inError = (kAMDError)(MobileDevice.AMDPostNotification(this.mSocketNotifications, serviceNamePtr, 0));
                CoreFoundation.CFRelease(serviceNamePtr);
                System.Threading.Thread.Sleep(200);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
        }

        #endregion

        #region --- 事件 ---

        private void DeviceNotification(IntPtr str, IntPtr user)
        {
            if (CoreFoundation.ReadCFStringFromIntPtr(str) == "com.apple.itunes-client.syncCancelRequest")
            {
                //终止同步
                this.EndSync();
                if (SyncCancelled != null)
                    SyncCancelled(this, EventArgs.Empty);
            }
            else if (CoreFoundation.ReadCFStringFromIntPtr(str) == "com.apple.mobile.application_uninstalled")
            {
                // 检查软件变化
                this.CheckDeviceAppChanged();
            }
        }

        #endregion

        #region --- 文件操作 ---

        public string[] GetFiles(string strPath)
        {
            return GetFiles(this.mHandleAfc2, strPath);
        }

        public string[] GetFilesByAFC(string strPath)
        {
            return GetFiles(this.mHandleAfc, strPath);
        }

        public string[] GetFiles(IntPtr afcHandle, string strPath)
        {
            return GetFiles(afcHandle, strPath, false);
        }

        public string[] GetFiles(string strPath, bool includeDirectory)
        {
            return GetFiles(this.mHandleAfc2, strPath, includeDirectory);
        }

        public string[] GetFilesByAFC(string strPath, bool includeDirectory)
        {
            return GetFiles(this.mHandleAfc, strPath, includeDirectory);
        }

        public string[] GetFiles(IntPtr afcHandle, string strPath, bool includeDirectory)
        {
            if (!this.IsConnected)
                throw (new Exception("Not connected to phone"));

            IntPtr hAFCDir = new IntPtr();
            kAMDError inError = kAMDError.kAMDSuccess;
            IntPtr hAFCPath = CoreFoundation.StringToHeap(strPath, System.Text.Encoding.UTF8);

            inError = (kAMDError)(MobileDevice.AFCDirectoryOpen(afcHandle, hAFCPath, ref hAFCDir));
            //CoreFoundation.FreeCoTaskMem(hAFCPath)

            if (inError != kAMDError.kAMDSuccess)
                return new string[] { };

            string strValue = null;
            ArrayList aryPath = new ArrayList();

            do
            {
                MobileDevice.AFCDirectoryRead(afcHandle, hAFCDir, ref strValue);

                if (strValue != null && strValue != "." && strValue != "..")
                {
                    if (!includeDirectory)
                    {
                        //当只需要显示文件列表时，才进行IsDirectory验证
                        string strSubPath = string.Empty;
                        if (strPath.LastIndexOf('/') == strPath.Length - 1)
                            strSubPath = string.Format("{0}{1}", strPath, strValue);
                        else
                            strSubPath = string.Format("{0}/{1}", strPath, strValue);

                        if (!IsDirectory(afcHandle, strSubPath))
                            aryPath.Add(strValue);
                    }
                    else
                    {
                        aryPath.Add(strValue);
                    }
                }
                System.Windows.Forms.Application.DoEvents();

            } while (strValue != null);

            MobileDevice.AFCDirectoryClose(afcHandle, hAFCDir);

            return ((string[])(aryPath.ToArray(typeof(string))));
        }

        public string[] GetFiles(IntPtr afcHandle, string strPath, bool includeDirectory, bool blnOperation)
        {
            if (blnOperation)
                return OperationGetFiles(afcHandle, strPath, includeDirectory);
            else
                return GetFiles(afcHandle, strPath, includeDirectory);
        }

        public long FileSize(string strPath)
        {
            return FileSize(this.mHandleAfc2, strPath);
        }

        public long FileSizeByAFC(string strPath)
        {
            if (this.mJailbreaked)
                return FileSize(this.mHandleAfc2, "/var/mobile/Media" + strPath);
            else
                return FileSize(this.mHandleAfc, strPath);
        }

        public long FileSize(IntPtr afcHandle, string strPath)
        {
            long lngSize = 0;

            IntPtr hCFData = IntPtr.Zero;
            IntPtr hCurrentData = IntPtr.Zero;

            uint data_size = 0;
            uint offset = 0;

            string strName = string.Empty;
            string strValue = string.Empty;

            kAMDError ret = kAMDError.kAMDSuccess;

            if (miTunes82)
            {
                IntPtr hAFCPath = CoreFoundation.StringToHeap(strPath, Encoding.UTF8);
                ret = (kAMDError)MobileDevice.AFCFileInfoOpen(afcHandle, hAFCPath, ref hCFData);
                //CoreFoundation.FreeCoTaskMem(hAFCPath)

                if (ret != kAMDError.kAMDSuccess)
                    return -1;

                while (true)
                {
                    IntPtr intName = IntPtr.Zero;
                    IntPtr intValue = IntPtr.Zero;

                    MobileDevice.AFCKeyValueRead(hCFData, ref intName, ref intValue);

                    strName = Marshal.PtrToStringAnsi(intName);
                    strValue = Marshal.PtrToStringAnsi(intValue);

                    if (strName == null || strName.Length == 0)
                    {
                        break;
                    }
                    else if (strName == "st_size")
                    {
                        lngSize = long.Parse(strValue);
                    }
                }

                MobileDevice.AFCKeyValueClose(hCFData);
            }
            else
            {
                IntPtr hAFCPath = CoreFoundation.StringToHeap(strPath, System.Text.Encoding.UTF8);
                ret = (kAMDError)MobileDevice.AFCGetFileInfo(afcHandle, hAFCPath, ref hCFData, ref data_size);
                //CoreFoundation.FreeCoTaskMem(hAFCPath)

                if (ret != kAMDError.kAMDSuccess)
                    return -1;

                offset = 0;
                while (offset < data_size)
                {
                    hCurrentData = Common.CreateIntPtr(hCFData, offset);
                    strName = Marshal.PtrToStringAnsi(hCurrentData);
                    offset += (uint)(strName.Length + 1);

                    hCurrentData = Common.CreateIntPtr(hCFData, offset);
                    strValue = Marshal.PtrToStringAnsi(hCurrentData);
                    offset += (uint)(strValue.Length + 1);

                    if (strName != null && strName == "st_size")
                    {
                        lngSize = long.Parse(strValue);
                    }
                }
            }

            return lngSize;
        }

        public long FileSize(IntPtr afcHandle, string strPath, bool blnOperation)
        {
            if (blnOperation)
                return this.OperationFileSize(afcHandle, strPath);
            else
                return this.FileSize(afcHandle, strPath);
        }

        public bool CreateFileByAFC(string strPath)
        {
            if (!this.CreateFile(this.mHandleAfc, strPath))
            {
                return this.CreateFile(this.mHandleAfc2, "/var/mobile/Media" + strPath);
            }
            return true;
        }

        public bool CreateFile(IntPtr afcHandle, string strPath)
        {
            iPhoneFile iFile = null;
            bool blnReturn = false;
            try
            {
                iFile = iPhoneFile.OpenWrite(this, afcHandle, strPath);
                iFile.Write(null, 0, 0);
                iFile.Close();
                blnReturn = true;
            }
            catch (Exception)
            {
                if (iFile != null)
                {
                    iFile.Close();
                    this.DeleteFile(afcHandle, strPath);
                }
            }
            return blnReturn;
        }

        public bool CreateFileByClean(long lngSize)
        {
            iPhoneFile iFile = null;
            bool blnReturn = false;
            string strFilePath = this.iPath.GarbageCleanFolder + "clean.tmp";
            try
            {
                iFile = iPhoneFile.OpenWrite(this, this.DefaultAfcHandle, strFilePath);
                iFile.Seek(lngSize, SeekOrigin.Begin);
                iFile.WriteByte(0);
                iFile.Close();
                blnReturn = true;
            }
            catch (Exception)
            {
                if (iFile != null)
                {
                    iFile.Close();
                    this.DeleteFile(this.DefaultAfcHandle, strFilePath);
                }
            }
            if (blnReturn)
            {
                Utility.WaitSeconds(3);
                this.DeleteFileByClean();
            }
            return blnReturn;
        }

        public void DeleteFileByClean()
        {
            string strFilePath = this.iPath.GarbageCleanFolder + "clean.tmp";
            if (this.Exists(this.DefaultAfcHandle, strFilePath))
            {
                try
                {
                    IntPtr hAFCPath = CoreFoundation.StringToHeap(strFilePath, System.Text.Encoding.UTF8);

                    MobileDevice.AFCRemovePath(this.DefaultAfcHandle, hAFCPath);
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "iPhoneDevice.DeleteFile");
                }
            }
        }

        public bool CreateDirectoryByAFC(string strPath)
        {
            if (!this.CreateDirectory(this.mHandleAfc, strPath))
                return this.CreateDirectory(this.mHandleAfc2, "/var/mobile/Media" + strPath);

            return true;
        }

        public bool CreateDirectory(string strPath)
        {
            return this.CreateDirectory(this.mHandleAfc2, strPath);
        }

        public bool CreateDirectory(IntPtr afcHandle, string strPath)
        {
            IntPtr hAFCPath = CoreFoundation.StringToHeap(strPath, System.Text.Encoding.UTF8);
            int ret = MobileDevice.AFCDirectoryCreate(afcHandle, hAFCPath);
            //CoreFoundation.FreeCoTaskMem(hAFCPath)

            return (ret == 0);
        }

        public bool CreateDirectory(IntPtr afcHandle, string strPath, bool blnOperation)
        {
            if (blnOperation)
                return this.OperationCreateDirectory(afcHandle, strPath);
            else
                return this.CreateDirectory(afcHandle, strPath);
        }

        public string[] GetDirectories(string path)
        {
            return this.GetDirectories(this.mHandleAfc2, path);
        }

        public string[] GetDirectories(IntPtr afcHandle, string strPath)
        {

            if (!this.IsConnected)
                throw (new Exception("Not connected to phone"));

            IntPtr hAFCDir = new IntPtr();
            kAMDError inError = kAMDError.kAMDSuccess;
            IntPtr hAFCPath = CoreFoundation.StringToHeap(strPath, System.Text.Encoding.UTF8);

            inError = (kAMDError)(MobileDevice.AFCDirectoryOpen(afcHandle, hAFCPath, ref hAFCDir));
            //CoreFoundation.FreeCoTaskMem(hAFCPath)

            if (inError != kAMDError.kAMDSuccess)
                return new string[] { };

            string strValue = null;
            ArrayList aryPath = new ArrayList();

            do
            {
                MobileDevice.AFCDirectoryRead(afcHandle, hAFCDir, ref strValue);

                if (strValue != null && strValue != "." && strValue != "..")
                {
                    string strSubPath = string.Empty;
                    if (strPath.LastIndexOf('/') == strPath.Length - 1)
                        strSubPath = string.Format("{0}{1}", strPath, strValue);
                    else
                        strSubPath = string.Format("{0}/{1}", strPath, strValue);

                    if (IsDirectory(afcHandle, strSubPath))
                        aryPath.Add(strValue);
                }
                System.Windows.Forms.Application.DoEvents();

            } while (strValue != null);

            MobileDevice.AFCDirectoryClose(afcHandle, hAFCDir);

            return ((string[])(aryPath.ToArray(typeof(string))));
        }

        public string[] GetDirectories(IntPtr afcHandle, string strPath, bool blnOperation)
        {
            if (blnOperation)
                return this.OperationGetDirectories(afcHandle, strPath);
            else
                return this.GetDirectories(afcHandle, strPath);
        }

        public bool HasDirectories(string strPath, params string[] args)
        {
            return HasDirectories(this.mHandleAfc2, strPath, args);
        }

        public bool HasDirectoriesByAFC(string strPath, params string[] args)
        {
            if (this.mJailbreaked)
                return HasDirectories(this.mHandleAfc2, "/var/mobile/Media" + strPath, args);
            else
                return HasDirectories(this.mHandleAfc, strPath, args);
        }

        public bool HasDirectories(IntPtr afcHandle, string strPath, params string[] args)
        {

            if (!this.IsConnected)
                return false;

            IntPtr hAFCDir = new IntPtr();
            kAMDError inError = kAMDError.kAMDSuccess;
            IntPtr hAFCPath = CoreFoundation.StringToHeap(strPath, System.Text.Encoding.UTF8);

            inError = (kAMDError)(MobileDevice.AFCDirectoryOpen(afcHandle, hAFCPath, ref hAFCDir));
            //CoreFoundation.FreeCoTaskMem(hAFCPath)

            if (inError != kAMDError.kAMDSuccess)
                return false;

            bool blnReturn = false;

            string strValue = null;
            do
            {
                MobileDevice.AFCDirectoryRead(afcHandle, hAFCDir, ref strValue);

                if (strValue != null && strValue != "." && strValue != "..")
                {
                    string strSubPath = string.Empty;
                    if (strPath.LastIndexOf('/') == strPath.Length - 1)
                        strSubPath = string.Format("{0}{1}", strPath, strValue);
                    else
                        strSubPath = string.Format("{0}/{1}", strPath, strValue);

                    if (IsDirectory(afcHandle, strSubPath))
                    {
                        blnReturn = true;
                        foreach (string filter in args)
                        {
                            if (strValue.Trim().ToLower() == filter.Trim().ToLower())
                                blnReturn = false;
                        }

                        if (blnReturn)
                            break;
                    }
                }
                System.Windows.Forms.Application.DoEvents();

            } while (strValue != null);

            MobileDevice.AFCDirectoryClose(afcHandle, hAFCDir);

            return blnReturn;
        }

        public bool HasDirectories(IntPtr afcHandle, string strPath, bool blnOperation, params string[] args)
        {
            if (blnOperation)
                return this.OperationHasDirectories(afcHandle, strPath);
            else
                return this.HasDirectories(afcHandle, strPath);
        }

        public bool RenameByAFC(string sourceName, string destName)
        {
            if (this.mJailbreaked)
                return Rename(this.mHandleAfc2, "/var/mobile/Media" + sourceName, "/var/mobile/Media" + destName);
            else
                return Rename(this.mHandleAfc, sourceName, destName);
        }

        public bool Rename(string sourceName, string destName)
        {
            return Rename(this.mHandleAfc2, sourceName, destName);
        }

        public bool Rename(IntPtr afcHandle, string sourceName, string destName)
        {
            IntPtr hAFCPathSrc = CoreFoundation.StringToHeap(sourceName, System.Text.Encoding.UTF8);
            IntPtr hAFCPathDes = CoreFoundation.StringToHeap(destName, System.Text.Encoding.UTF8);
            int intRet = 0;

            intRet = MobileDevice.AFCRenamePath(afcHandle, hAFCPathSrc, hAFCPathDes);

            //CoreFoundation.FreeCoTaskMem(hAFCPathSrc)
            //CoreFoundation.FreeCoTaskMem(hAFCPathDes)

            return (intRet == 0);
        }

        public bool Rename(IntPtr afcHandle, string sourceName, string destName, bool blnOperation)
        {
            if (blnOperation)
                return this.OperationRename(afcHandle, sourceName, destName);
            else
                return this.Rename(afcHandle, sourceName, destName);
        }

        public bool ExistsByAFC(string strPath)
        {
            return this.Exists(this.mHandleAfc, strPath);
        }

        public bool Exists(string strPath)
        {
            return this.Exists(this.mHandleAfc2, strPath);
        }

        public bool Exists(IntPtr afcHandle, string strPath)
        {
            bool result = false;

            IntPtr hAFCPath = IntPtr.Zero;
            IntPtr hData = IntPtr.Zero;

            try
            {
                if (!this.IsConnected)
                    return result;

                uint data_size = 0;
                hAFCPath = CoreFoundation.StringToHeap(strPath, System.Text.Encoding.UTF8);
                if (miTunes82)
                {
                    if (MobileDevice.AFCFileInfoOpen(afcHandle, hAFCPath, ref hData) == 0)
                        result = true;
                }
                else
                {
                    if (MobileDevice.AFCGetFileInfo(afcHandle, hAFCPath, ref hData, ref data_size) == 0)
                        result = true;
                }

                //CoreFoundation.FreeCoTaskMem(hAFCPath)

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iPhoneDevice.Exists");
            }

            return result;
        }

        public bool Exists(IntPtr afcHandle, string strPath, bool blnOperation)
        {
            if (blnOperation)
                return this.OperationExists(afcHandle, strPath);
            else
                return this.Exists(afcHandle, strPath);
        }

        public iPhoneFileInfo GetFileInfo(string strPath)
        {
            return GetFileInfo(this.mHandleAfc2, strPath);
        }

        public iPhoneFileInfo GetFileInfo(IntPtr afcHandle, string strPath)
        {
            iPhoneFileInfo fileInfo = null;

            this.IsDirectory(afcHandle, strPath, ref fileInfo);

            return fileInfo;
        }

        public iPhoneFileInfo GetFileInfo(IntPtr afcHandle, string strPath, bool blnOperation)
        {
            if (blnOperation)
                return this.OperationGetFileInfo(afcHandle, strPath);
            else
                return this.GetFileInfo(afcHandle, strPath);
        }

        public bool IsDirectory(string strPath)
        {
            iPhoneFileInfo iInfo = null;
            return IsDirectory(strPath, ref iInfo);
        }

        public bool IsDirectoryByAFC(string strPath)
        {
            iPhoneFileInfo iInfo = null;
            return IsDirectory(this.mHandleAfc, strPath, ref iInfo);
        }

        public bool IsDirectory(IntPtr afcHandle, string strPath)
        {
            iPhoneFileInfo iInfo = null;
            return IsDirectory(afcHandle, strPath, ref iInfo);
        }

        public bool IsDirectory(string strPath, ref iPhoneFileInfo e)
        {
            return IsDirectory(this.mHandleAfc2, strPath, ref e);
        }

        public bool IsDirectoryByAFC(string strPath, ref iPhoneFileInfo e)
        {
            return IsDirectory(this.mHandleAfc, strPath, ref e);
        }

        public bool IsDirectory(IntPtr afcHandle, string strPath, ref iPhoneFileInfo e)
        {
            if (!this.IsConnected)
                return false;

            bool is_dir = false;
            string strAttributes = "";
            IntPtr hAFCPath = CoreFoundation.StringToHeap(strPath, System.Text.Encoding.UTF8);

            kAMDError inError = kAMDError.kAMDUndefinedError;
            IntPtr data = IntPtr.Zero;

            string strName = string.Empty;
            string strValue = string.Empty;

            if (miTunes82)
            {
                inError = (kAMDError)(MobileDevice.AFCFileInfoOpen(afcHandle, hAFCPath, ref data));

                if (inError != kAMDError.kAMDSuccess)
                {
                    goto DO_EXIST;
                }

                while (true)
                {
                    IntPtr intName = IntPtr.Zero;
                    IntPtr intValue = IntPtr.Zero;

                    MobileDevice.AFCKeyValueRead(data, ref intName, ref intValue);
                    strName = Marshal.PtrToStringAnsi(intName);

#if MAC || IOS
					strValue = Marshal.PtrToStringAnsi(intValue);
#else
                    //"目标位置"有时包含中文，因此使用UTF8获取字符串
                    int length = API.lstrlenA(intValue);
                    if (length > 0)
                    {
                        byte[] arrData = new byte[length];
                        Marshal.Copy(intValue, arrData, 0, length);
                        strValue = Encoding.UTF8.GetString(arrData);
                    }
#endif

                    if (string.IsNullOrEmpty(strName))
                        break;

                    //记录 Attributes 字串
                    strAttributes += strName + "=" + strValue + ";";

                    if (strName == "st_ifmt")
                    {
                        if (strValue == "S_IFDIR")
                        {
                            is_dir = true;
                        }
                        else if (strValue == "S_IFLNK")
                        {

                            //如果该文件是链接文件，那么进一步判断此链接文件所连接的目标文件是否是目录
                            //-------------------------------------------------------------------------------------
                            IntPtr hAFCDir = new IntPtr();

                            if (MobileDevice.AFCDirectoryOpen(afcHandle, hAFCPath, ref hAFCDir) == 0)
                            {
                                is_dir = true;

                                MobileDevice.AFCDirectoryClose(afcHandle, hAFCDir);
                            }
                            else
                            {
                                is_dir = false;
                            }
                        }
                        else
                        {
                            is_dir = false;
                        }
                    }
                }

                MobileDevice.AFCKeyValueClose(data);
            }
            else
            {
                IntPtr current_data = IntPtr.Zero;

                uint data_size = 0;
                uint offset = 0;

                inError = (kAMDError)(MobileDevice.AFCGetFileInfo(afcHandle, hAFCPath, ref data, ref data_size));

                if (inError != kAMDError.kAMDSuccess)
                {
                    goto DO_EXIST;
                }

                offset = 0;
                while (offset < data_size)
                {
                    current_data = Common.CreateIntPtr(data, offset);
                    strName = Marshal.PtrToStringAnsi(current_data);
                    offset += (uint)(strName.Length + 1);

                    current_data = Common.CreateIntPtr(data, offset);
                    strValue = Marshal.PtrToStringAnsi(current_data);
                    offset += (uint)(strValue.Length + 1);

                    //记录 Attributes 字串
                    strAttributes += strName + "=" + strValue + ";";

                    if (strName == "st_ifmt")
                    {
                        if (strValue == "S_IFDIR")
                        {
                            is_dir = true;
                        }
                        else if (strValue == "S_IFLNK")
                        {

                            //如果该文件是链接文件，那么进一步判断此链接文件所连接的目标文件是否是目录
                            //-------------------------------------------------------------------------------------
                            IntPtr hAFCDir = new IntPtr();

                            if (MobileDevice.AFCDirectoryOpen(afcHandle, hAFCPath, ref hAFCDir) == 0)
                            {
                                is_dir = true;

                                MobileDevice.AFCDirectoryClose(afcHandle, hAFCDir);
                            }
                            else
                            {
                                is_dir = false;
                            }
                        }
                        else
                        {
                            is_dir = false;
                        }
                    }
                }
            }

            e = new iPhoneFileInfo(strAttributes, is_dir, mTimeZoneOffsetFromUTC, strPath);

        DO_EXIST:
            //CoreFoundation.FreeCoTaskMem(hAFCPath)

            return is_dir;
        }

        public bool IsDirectory(IntPtr afcHandle, string strPath, ref iPhoneFileInfo e, bool blnOperation)
        {
            if (blnOperation)
                return this.OperationIsDirectory(afcHandle, strPath, ref e);
            else
                return this.IsDirectory(afcHandle, strPath, ref e);
        }

        private bool InternalDeleteDirectory(string path)
        {
            return InternalDeleteDirectory(this.mHandleAfc2, path);
        }

        private bool InternalDeleteDirectory(IntPtr afcHandle, string path)
        {
            return InternalDeleteDirectory(afcHandle, path, null);
        }

        private bool InternalDeleteDirectory(IntPtr afcHandle, string path, FileDeleteHandler callback)
        {
            if (!this.IsConnected)
                return false;

            IntPtr hAFCDir = new IntPtr();
            kAMDError inError = kAMDError.kAMDSuccess;
            IntPtr hAFCPath = CoreFoundation.StringToHeap(path, System.Text.Encoding.UTF8);
            //Dim hAFCPath As IntPtr = CoreFoundation.StringToCFString(path)

            inError = (kAMDError)(MobileDevice.AFCDirectoryOpen(afcHandle, hAFCPath, ref hAFCDir));
            //CoreFoundation.FreeCoTaskMem(hAFCPath)

            if (inError != kAMDError.kAMDSuccess)
                return false;

            string buffer = null;
            List<string> strFolderList = new List<string>();
            List<string> strFileList = new List<string>();

            FileDeleteEventArgs args = new FileDeleteEventArgs(path);

            do
            {
                if (!this.IsConnected)
                    return false;

                System.Windows.Forms.Application.DoEvents();
                MobileDevice.AFCDirectoryRead(afcHandle, hAFCDir, ref buffer);

                if (buffer != null && buffer != "." && buffer != "..")
                {
                    string strSubPath = string.Empty;
                    if (path.LastIndexOf('/') == path.Length - 1) // path.EndsWith("/")
                        strSubPath = string.Format("{0}{1}", path, buffer);
                    else
                        strSubPath = string.Format("{0}/{1}", path, buffer);

                    if (IsDirectory(afcHandle, strSubPath))
                        strFolderList.Add(strSubPath);
                    else
                        strFileList.Add(strSubPath);
                }

            } while (buffer != null);

            MobileDevice.AFCDirectoryClose(afcHandle, hAFCDir);

            foreach (string strPath in strFileList)
            {
                if (callback != null)
                {
                    args.FilePath = strPath;
                    args.IsDirectory = false;
                    callback(args);

                    if (args.Cancel)
                        return false;
                }

                System.Windows.Forms.Application.DoEvents();
                DeleteFile(afcHandle, strPath);
            }

            foreach (string strPath in strFolderList)
            {
                if (!InternalDeleteDirectory(afcHandle, strPath, callback))
                    return false;
            }

            if (callback != null)
            {
                args.FilePath = path;
                args.IsDirectory = true;
                callback(args);

                if (args.Cancel)
                    return false;
            }

            System.Windows.Forms.Application.DoEvents();
            this.DeleteDirectory(afcHandle, path);

            return true;
        }

        public void DeleteDirectoryByAFC(string strPath)
        {
            this.DeleteDirectoryByAFC(strPath, true, null);
        }

        public void DeleteDirectoryByAFC(string strPath, bool recursive)
        {
            this.DeleteDirectoryByAFC(strPath, recursive, null);
        }

        public void DeleteDirectoryByAFC(string strPath, bool recursive, FileDeleteHandler callback)
        {
            if (this.mJailbreaked)
            {
                DeleteDirectory(this.mHandleAfc2, "/var/mobile/Media" + strPath, true, callback);
            }
            else
            {
                DeleteDirectory(this.mHandleAfc, strPath, true, callback);
            }
        }

        public void DeleteDirectory(string strPath)
        {
            DeleteDirectory(this.mHandleAfc2, strPath);
        }

        public void DeleteDirectory(IntPtr afcHandle, string strPath)
        {

            if (IsDirectory(afcHandle, strPath))
            {
                try
                {
                    IntPtr hAFCPath = CoreFoundation.StringToHeap(strPath, System.Text.Encoding.UTF8);

                    MobileDevice.AFCRemovePath(afcHandle, hAFCPath);
                    //CoreFoundation.FreeCoTaskMem(hAFCPath)
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "iPhoneDevice.DeleteDirectory");
                }
            }

        }

        public void DeleteDirectory(string strPath, bool recursive)
        {
            DeleteDirectory(this.mHandleAfc2, strPath, recursive, null);
        }

        public void DeleteDirectory(IntPtr afcHandle, string strPath, bool recursive)
        {
            DeleteDirectory(afcHandle, strPath, recursive, null);
        }

        public void DeleteDirectory(IntPtr afcHandle, string strPath, bool recursive, FileDeleteHandler callback)
        {

            if (!recursive)
            {
                DeleteDirectory(afcHandle, strPath);
            }
            else
            {
                if (IsDirectory(afcHandle, strPath))
                {
                    InternalDeleteDirectory(afcHandle, strPath, callback);
                }
            }
        }

        public void DeleteDirectory(IntPtr afcHandle, string strPath, bool recursive, FileDeleteHandler callback, bool blnOperation)
        {
            if (blnOperation)
                this.OperationDeleteDirectory(afcHandle, strPath, recursive, callback);
            else
                this.DeleteDirectory(afcHandle, strPath, recursive, callback);
        }

        public void DeleteFileByAFC(string strPath)
        {
            if (this.mJailbreaked)
                this.DeleteFile(this.mHandleAfc2, "/var/mobile/Media" + strPath);
            else
                this.DeleteFile(this.mHandleAfc, strPath);
        }

        public void DeleteFile(string strPath)
        {
            this.DeleteFile(this.mHandleAfc2, strPath);
        }

        public void DeleteFile(IntPtr afcHandle, string strPath)
        {
            if (this.Exists(afcHandle, strPath))
            {
                try
                {
                    IntPtr hAFCPath = CoreFoundation.StringToHeap(strPath, System.Text.Encoding.UTF8);

                    MobileDevice.AFCRemovePath(afcHandle, hAFCPath);
                    //CoreFoundation.FreeCoTaskMem(hAFCPath)
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "iPhoneDevice.DeleteFile");
                }
            }
        }

        public void DeleteFile(IntPtr afcHandle, string strPath, bool blnOperation)
        {
            if (blnOperation)
                this.OperationDeleteFile(afcHandle, strPath);
            else
                this.DeleteFile(afcHandle, strPath);
        }

        public void CheckFolderByAFC(string strFolder)
        {
            this.CheckFolder(strFolder, this.mHandleAfc);
        }

        public void CheckFolder(string strFolder)
        {
            this.CheckFolder(strFolder, this.mHandleAfc2);
        }

        public void CheckFolder(string strFolder, IntPtr hAFC)
        {
            if (!this.Exists(hAFC, strFolder))
            {
                string[] strNames = strFolder.Split(new char[] { '/' }, StringSplitOptions.RemoveEmptyEntries);
                string strPath = "";
                foreach (string strName in strNames)
                {
                    strPath += "/" + strName;

                    if (!this.Exists(hAFC, strPath))
                        this.CreateDirectory(hAFC, strPath);
                }
            }
        }

        public string FormatToMobilePath(string strPath)
        {
            return strPath.Replace("/private/var/mobile/Media", "").Replace("/var/mobile/Media", "");
        }

        public string GetDirectoryName(string strPath)
        {
            strPath = strPath.TrimEnd('/');

            int index = strPath.LastIndexOf("/");
            if (index < 0)
                index = strPath.LastIndexOf("\\");

            if (index >= 0)
                return strPath.Substring(0, index);
            else
                return string.Empty;
        }

        public string GetFileName(string strPath)
        {
            strPath = strPath.TrimEnd('/');

            int index = strPath.LastIndexOf("/");
            if (index < 0)
                index = strPath.LastIndexOf("\\");

            if (index >= 0)
                return strPath.Substring(index + 1, strPath.Length - index - 1);
            else
                return string.Empty;
        }

        public string GetFileNameWithoutExtension(string strPath)
        {
            string strName = string.Empty;

            strPath = strPath.TrimEnd('/'.ToString().ToCharArray());

            int index = strPath.LastIndexOf("/");
            if (index < 0)
                index = strPath.LastIndexOf("\\");

            if (index >= 0)
            {
                strName = strPath.Substring(index + 1, strPath.Length - index - 1);
                int indexDot = strName.LastIndexOf(".");
                if (indexDot > 0)
                {
                    strName = strName.Substring(0, indexDot);
                }
            }

            return strName;
        }

        public string GetExtension(string strPath)
        {
            string strExtension = string.Empty;

            strPath = strPath.TrimEnd('/'.ToString().ToCharArray());

            int index = strPath.LastIndexOf("/");
            if (index < 0)
                index = strPath.LastIndexOf("\\");

            if (index >= 0)
            {
                string strName = strPath.Substring(index + 1, strPath.Length - index - 1);
                int indexDot = strName.LastIndexOf(".");
                if (indexDot > 0)
                    strExtension = strName.Substring(indexDot, strName.Length - indexDot);
            }

            return strExtension;
        }

        #endregion

        #region --- 文件传输 ---

        public bool CopyToPhoneByAFC(string strPathOnPC, string strPathOnPhone)
        {
            bool blnCancel = false;
            List<string> list = null;
            ErrorType errType = ErrorType.Unknown;

            return this.CopyToPhoneByAFC(strPathOnPC, strPathOnPhone, null, ref list, ref blnCancel, ref errType);
        }

        public bool CopyToPhoneByAFC(string strPathOnPC, string strPathOnPhone, DataTransferEventHandler progressCallback)
        {
            bool blnCancel = false;
            List<string> list = null;
            ErrorType errType = ErrorType.Unknown;

            return this.CopyToPhoneByAFC(strPathOnPC, strPathOnPhone, progressCallback, ref list, ref blnCancel, ref errType);
        }

        public bool CopyToPhoneByAFC(string strPathOnPC, string strPathOnPhone, DataTransferEventHandler progressCallback, ref List<string> succeedListFile)
        {
            bool blnCancel = false;
            ErrorType errType = ErrorType.Unknown;

            return this.CopyToPhoneByAFC(strPathOnPC, strPathOnPhone, progressCallback, ref succeedListFile, ref blnCancel, ref errType);
        }

        public bool CopyToPhoneByAFC(string strPathOnPC, string strPathOnPhone, DataTransferEventHandler progressCallback, ref List<string> succeedListFile, ref bool blnCancel)
        {
            ErrorType errType = ErrorType.Unknown;
            return this.CopyToPhoneByAFC(strPathOnPC, strPathOnPhone, progressCallback, ref succeedListFile, ref blnCancel, ref errType);
        }

        public bool CopyToPhoneByAFC(string strPathOnPC, string strPathOnPhone, DataTransferEventHandler progressCallback, ref bool blnCancel, ref ErrorType errType)
        {
            List<string> list = null;
            return this.CopyToPhoneByAFC(strPathOnPC, strPathOnPhone, progressCallback, ref list, ref blnCancel, ref errType);
        }

        public bool CopyToPhoneByAFC(string strPathOnPC, string strPathOnPhone, DataTransferEventHandler progressCallback, ref List<string> succeedListFile, ref bool blnCancel, ref ErrorType errType)
        {
            bool blnResult = false;

            if (this.CopyToPhone(this.mHandleAfc, strPathOnPC, strPathOnPhone, progressCallback, ref succeedListFile))
            {
                blnResult = true;
            }
            else if (!blnCancel && this.mJailbreaked && errType != ErrorType.DiskIsFull)
            {
                string strTmpPath = "/var/mobile/Media" + strPathOnPhone;
                bool blnExist = false;
                if (this.Exists(this.mHandleAfc2, strTmpPath))
                {
                    this.DeleteFile(strTmpPath);
                    blnExist = true;
                }
                if (blnExist && this.CopyToPhone(this.mHandleAfc, strPathOnPC, strPathOnPhone, progressCallback, ref succeedListFile, ref blnCancel))
                {
                    blnResult = true;
                }
                else if (this.CopyToPhone(this.mHandleAfc2, strPathOnPC, strTmpPath, progressCallback, ref succeedListFile, ref blnCancel))
                {
                    blnResult = true;
                }
            }

            return blnResult;
        }

        public bool CopyToPhone(string fileOnPC, string fileOnPhone)
        {
            List<string> list = null;
            return this.CopyToPhone(this.mHandleAfc2, fileOnPC, fileOnPhone, null, ref list);
        }

        public bool CopyToPhone(string fileOnPC, string fileOnPhone, DataTransferEventHandler progressCallback)
        {
            List<string> list = null;
            return this.CopyToPhone(this.mHandleAfc2, fileOnPC, fileOnPhone, progressCallback, ref list);
        }

        public bool CopyToPhone(string fileOnPC, string fileOnPhone, DataTransferEventHandler progressCallback, ref List<string> succeedListFile)
        {
            return this.CopyToPhone(this.mHandleAfc2, fileOnPC, fileOnPhone, progressCallback, ref succeedListFile);
        }

        public bool CopyToPhone(IntPtr afcHandle, string fileOnPC, string fileOnPhone)
        {
            List<string> list = null;
            return this.CopyToPhone(afcHandle, fileOnPC, fileOnPhone, null, ref list);
        }

        public bool CopyToPhone(IntPtr afcHandle, string fileOnPC, string fileOnPhone, DataTransferEventHandler progressCallback)
        {
            List<string> list = null;
            return this.CopyToPhone(afcHandle, fileOnPC, fileOnPhone, progressCallback, ref list);
        }

        public bool CopyToPhone(IntPtr afcHandle, string fileOnPC, string fileOnPhone, DataTransferEventHandler progressCallback, ref List<string> succeedListFile)
        {
            bool blnCancel = false;
            return this.CopyToPhone(afcHandle, fileOnPC, fileOnPhone, progressCallback, ref succeedListFile, ref blnCancel);
        }

        public bool CopyToPhone(IntPtr afcHandle, string fileOnPC, string fileOnPhone, DataTransferEventHandler progressCallback, ref List<string> succeedListFile, ref bool blnCancel)
        {
            bool blnReturn = true;
            byte[] arrBuffer = null;

            fileOnPC = fileOnPC.TrimEnd('\\');

            //递归循环上传时，会自动对fileOnPhone以“/”结尾进行赋值，此时需要重新组装目标路径fileOnPhone
            if (fileOnPhone.EndsWith("/"))
                fileOnPhone = fileOnPhone + Path.GetFileName(fileOnPC);

            long lngPosition = 0;
            if (File.Exists(fileOnPC))
            {
                arrBuffer = new byte[mDataTransfer_Buffer_Size + 1];
                //上传文件
                DataTransferEventArgs args = new DataTransferEventArgs(fileOnPhone, fileOnPC);

                using (System.IO.FileStream fs = System.IO.File.OpenRead(fileOnPC))
                {
                    iPhoneFile iFile = null;

                    try
                    {
                        iFile = iPhoneFile.OpenWrite(this, afcHandle, fileOnPhone);
                        if (iFile == null)
                        {
                            blnReturn = false;
                            args.Success = false;

                            goto DO_NEXT;
                        }

                        int readCount = fs.Read(arrBuffer, 0, arrBuffer.Length);

                        DateTime dblTimer = DateTime.Now;
                        long lngBuffer = 0;
                        int intSpeed = 0;

                        args.FileSize = fs.Length;

                        while (readCount > 0)
                        {

                            if (progressCallback != null)
                            {
                                //=== 计算传输速度、更新状态 =========================================
                                lngBuffer += readCount;
                                double dblTimePast = ((TimeSpan)DateTime.Now.Subtract(dblTimer)).TotalSeconds;
                                if (dblTimePast >= 1)
                                {
                                    intSpeed = (int)(lngBuffer / dblTimePast);
                                    dblTimer = DateTime.Now;
                                    lngBuffer = 0;
                                }

                                args.Speed = intSpeed;
                                args.LastTransSize = readCount;
                                //==========================================================

                                progressCallback(args);

                                //用户是否放弃传输
                                if (args.Cancel)
                                {
                                    //删除已经上传了一部分的文件
                                    this.DeleteFile(afcHandle, fileOnPhone);
                                    blnCancel = true;
                                    blnReturn = false;
                                    break;
                                }
                            }


                            iFile.Write(arrBuffer, 0, readCount);
                            args.TransSize += readCount;

                            //Console.WriteLine(buffer.Length & "," & file.Position & "," & e.TransSize)

                            //每写入100M进入写入位置校验一次
                            if (args.TransSize % 104857600 == 0)
                            {
                                lngPosition = iFile.Position;
                                if (lngPosition == 0 || lngPosition < args.TransSize)
                                {
                                    args.ErrorType = ErrorType.DiskIsFull;
                                    throw (new Exception("磁盘空间不足！"));
                                }
                            }

                            readCount = fs.Read(arrBuffer, 0, arrBuffer.Length);
                        }

                        args.LastTransSize = 0;
                        args.TransSize = args.FileSize;

                        if (!args.Cancel && args.FileSize != iFile.Position)
                        {
                            args.ErrorType = ErrorType.DiskIsFull;
                            blnCancel = true;
                            throw (new Exception("磁盘空间不足！"));
                        }

                        iFile.Close();
                    }
                    catch (Exception ex)
                    {
                        if (iFile != null)
                        {
                            iFile.Close();
                            this.DeleteFile(afcHandle, fileOnPhone);
                        }

                        Common.Log(ex.ToString() + "\t" + "lngPosition:" + lngPosition + "\t" + "TransSize:" + args.TransSize);

                        blnReturn = false;
                        args.LastErrMsg = ex.Message;
                        args.Success = blnReturn;

                    }
                DO_NEXT:
                    1.GetHashCode(); //nop
                }


                if (progressCallback != null)
                    progressCallback(args);

                if (blnReturn && succeedListFile != null)
                    //添加已经上传成功的文件列表
                    succeedListFile.Add(fileOnPhone);

            }
            else if (Directory.Exists(fileOnPC))
            {
                //上传文件夹
                if (!this.Exists(afcHandle, fileOnPhone))
                {
                    if (!this.CreateDirectory(afcHandle, fileOnPhone))
                        blnReturn = false;
                }

                //添加要上传的目录
                if (succeedListFile != null)
                    succeedListFile.Add(fileOnPhone);

                //如果上传的目录，同时搜索该目录下的其他文件
                string strOniPhone = fileOnPhone;
                if (!strOniPhone.EndsWith("/"))
                    strOniPhone = fileOnPhone + "/";

                foreach (string strfile in Directory.GetFiles(fileOnPC))
                {
                    if (!this.CopyToPhone(afcHandle, strfile, strOniPhone, progressCallback, ref succeedListFile, ref blnCancel))
                        blnReturn = false;

                    if (blnCancel)
                        goto DO_EXIST;
                }

                //如果上传的目录，同时搜索该目录下的其他文件夹
                foreach (string strfolder in System.IO.Directory.GetDirectories(fileOnPC))
                {
                    if (!this.CopyToPhone(afcHandle, strfolder, strOniPhone, progressCallback, ref succeedListFile, ref blnCancel))
                        blnReturn = false;

                    if (blnCancel)
                        goto DO_EXIST;
                }
            }
            else
            {
                //文件或者文件夹不存在
                blnReturn = false;
            }

        DO_EXIST:
            if (arrBuffer != null)
                arrBuffer = null;

            //全部传送成功才算成功，如果有任意一个或多个文件传送失败，那么就会返回 False
            return blnReturn;
        }

        public bool DownFromPhoneByAFC(string fileOnPhone, string fileOnPC)
        {
            bool blnCancel = false;
            List<string> list = null;

            return this.DownFromPhoneByAFC(fileOnPhone, fileOnPC, null, ref list, ref blnCancel);
        }

        public bool DownFromPhoneByAFC(string fileOnPhone, string fileOnPC, DataTransferEventHandler progressCallback)
        {
            bool blnCancel = false;
            List<string> list = null;

            return this.DownFromPhoneByAFC(fileOnPhone, fileOnPC, progressCallback, ref list, ref blnCancel);
        }

        public bool DownFromPhoneByAFC(string fileOnPhone, string fileOnPC, DataTransferEventHandler progressCallback, ref List<string> succeedListFile)
        {
            bool blnCancel = false;

            return this.DownFromPhoneByAFC(fileOnPhone, fileOnPC, progressCallback, ref succeedListFile, ref blnCancel);
        }

        public bool DownFromPhoneByAFC(string fileOnPhone, string fileOnPC, DataTransferEventHandler progressCallback, ref List<string> succeedListFile, ref bool blnCancel)
        {
            bool blnResult = false;

            if (this.DownFromPhone(this.mHandleAfc, fileOnPhone, fileOnPC, progressCallback, ref succeedListFile, ref blnCancel))
            {
                blnResult = true;
            }
            else if (!blnCancel && this.mJailbreaked)
            {
                string tmpPath = "/var/mobile/Media" + fileOnPhone;
                if (this.DownFromPhone(this.mHandleAfc2, tmpPath, fileOnPC, progressCallback, ref succeedListFile, ref blnCancel))
                    blnResult = true;
            }

            return blnResult;
        }

        public bool DownFromPhone(string fileOnPhone, string fileOnPC)
        {
            List<string> list = null;
            return this.DownFromPhone(this.mHandleAfc2, fileOnPhone, fileOnPC, null, ref list);
        }

        public bool DownFromPhone(string fileOnPhone, string fileOnPC, DataTransferEventHandler progressCallback)
        {
            List<string> list = null;
            return this.DownFromPhone(this.mHandleAfc2, fileOnPhone, fileOnPC, progressCallback, ref list);
        }

        public bool DownFromPhone(string fileOnPhone, string fileOnPC, DataTransferEventHandler progressCallback, ref List<string> succeedListFile)
        {
            return this.DownFromPhone(this.mHandleAfc2, fileOnPhone, fileOnPC, progressCallback, ref succeedListFile);
        }

        public bool DownFromPhone(IntPtr afcHandle, string fileOnPhone, string fileOnPC)
        {
            List<string> list = null;
            return this.DownFromPhone(afcHandle, fileOnPhone, fileOnPC, null, ref list);
        }

        public bool DownFromPhone(IntPtr afcHandle, string fileOnPhone, string fileOnPC, DataTransferEventHandler progressCallback)
        {
            List<string> list = null;
            return this.DownFromPhone(afcHandle, fileOnPhone, fileOnPC, progressCallback, ref list);
        }

        public bool DownFromPhone(IntPtr afcHandle, string fileOnPhone, string fileOnPC, DataTransferEventHandler progressCallback, ref List<string> succeedListFile)
        {
            bool blnCancel = false;
            return this.DownFromPhone(afcHandle, fileOnPhone, fileOnPC, progressCallback, ref succeedListFile, ref blnCancel);
        }

        public bool DownFromPhone(IntPtr afcHandle, string fileOnPhone, string fileOnPC, DataTransferEventHandler progressCallback, ref List<string> succeedListFile, ref bool blnCancel)
        {
            if (File.Exists(fileOnPC))
            {
                try
                {
                    File.Delete(fileOnPC);
                }
                catch
                {
                    return false;
                }
            }

            if (!this.Exists(afcHandle, fileOnPhone))
            {
                //Console.WriteLine("Not Exit: " + fileOnPhone);
                return false;
            }

            bool blnReturn = true;

            byte[] arrBuffer = null;
            string strErrMsg = string.Empty;

            iPhoneFileInfo info = null;

            //递归循环下载时，会自动对fileOnPC以“\\”结尾进行赋值，此时需要重新组装下载路径
            if (fileOnPC.EndsWith(Path.DirectorySeparatorChar.ToString()))
                fileOnPC = fileOnPC + Utility.ReplaceWinIllegalName(GetFileName(fileOnPhone));

            //如果下载的是文件
            if (!this.IsDirectory(afcHandle, fileOnPhone, ref info))
            {
                if (info == null)
                    goto DO_EXIT;

                DataTransferEventArgs e = new DataTransferEventArgs(fileOnPhone, fileOnPC);
                arrBuffer = new byte[mDataTransfer_Buffer_Size + 1];

                try
                {
                    if (info.LinkTarget.Length > 0)
                    {
                        if (info.LinkTarget.StartsWith("/"))
                            fileOnPhone = info.LinkTarget;
                        else
                            fileOnPhone = this.GetDirectoryName(fileOnPhone) + "/" + info.LinkTarget;
                    }

                    using (System.IO.FileStream fs = new FileStream(fileOnPC, FileMode.Create))
                    {
                        iPhoneFile iFile = iPhoneFile.OpenRead(this, afcHandle, fileOnPhone);
                        if (iFile == null)
                        {
                            blnReturn = false;

                            goto DO_NEXT;
                        }

                        int iReadCount = iFile.Read(arrBuffer, 0, arrBuffer.Length);
                        if (iReadCount < 0)
                        {
                            iFile.Close();
                            blnReturn = false;

                            goto DO_EXIT;
                        }

                        if (iReadCount > arrBuffer.Length)
                        {
                            iFile.Close();
                            blnReturn = false;

                            Console.WriteLine("iFile.Read error iReadCount > arrBuffer.Length");

                            goto DO_EXIT;
                        }

                        DateTime dblTimer = DateTime.Now;
                        long lngBuffer = 0;
                        int intSpeed = 0;

                        e.FileSize = this.FileSize(afcHandle, fileOnPhone);

                        try
                        {
                            while (iReadCount > 0)
                            {
                                if (progressCallback != null)
                                {
                                    //=== 计算传输速度、更新状态 =========================================
                                    lngBuffer += iReadCount;
                                    double dblTimePast = ((TimeSpan)DateTime.Now.Subtract(dblTimer)).TotalSeconds;
                                    if (dblTimePast >= 1)
                                    {
                                        intSpeed = (int)(lngBuffer / dblTimePast);
                                        dblTimer = DateTime.Now;
                                        lngBuffer = 0;
                                    }

                                    e.Speed = intSpeed;
                                    e.LastTransSize = iReadCount;
                                    //==========================================================

                                    progressCallback(e);

                                    //用户是否放弃传输
                                    if (e.Cancel)
                                    {
                                        blnCancel = true;
                                        blnReturn = false;

                                        break;
                                    }
                                }

                                fs.Write(arrBuffer, 0, iReadCount);
                                e.TransSize += iReadCount;

                                iReadCount = iFile.Read(arrBuffer, 0, arrBuffer.Length);
                            }

                            iFile.Close();
                            e.LastTransSize = 0;
                            e.TransSize = e.FileSize;

                        }
                        catch (Exception exFile)
                        {
                            if (iFile != null)
                                iFile.Close();

                            blnReturn = false;
                        }
                    }

                DO_NEXT:
                    if (e.Cancel)
                    {
                        try
                        {
                            if (File.Exists(fileOnPC))
                                File.Delete(fileOnPC);
                        }
                        catch (Exception)
                        { }
                    }

                }
                catch (Exception ex)
                {
                    try
                    {
                        //当下载失败时，如果下载的文件大小为0，则直接删除空文件
                        if (File.Exists(fileOnPC) && new FileInfo(fileOnPC).Length == 0)
                            File.Delete(fileOnPC);
                    }
                    catch
                    { }
                    strErrMsg = ex.Message;

                    blnReturn = false;
                }

                if (progressCallback != null)
                    progressCallback(e);

                if (blnReturn && succeedListFile != null)
                {
                    //添加已经下载成功的文件列表
                    succeedListFile.Add(fileOnPC);
                }

            }
            else
            {
                //如果下载的是目录
                if (!System.IO.Directory.Exists(fileOnPC))
                    System.IO.Directory.CreateDirectory(fileOnPC);

                //添加要下载的目录
                if (succeedListFile != null)
                    succeedListFile.Add(fileOnPC);

                //如果下载的是目录，同时搜索该目录下的其他文件
                string strOnComputer = fileOnPC + Path.DirectorySeparatorChar.ToString();
                foreach (string strfile in this.GetFiles(afcHandle, fileOnPhone, true))
                {
                    if (!this.DownFromPhone(afcHandle, fileOnPhone.TrimEnd('/'.ToString().ToCharArray()) + "/" + strfile, strOnComputer, progressCallback, ref succeedListFile, ref blnCancel))
                        blnReturn = false;

                    if (blnCancel)
                        goto DO_EXIT;
                }

            }

        DO_EXIT:
            if (arrBuffer != null)
                arrBuffer = null;

            //Console.WriteLine("Download: " + blnReturn.ToString() + "\t" + fileOnPhone);

            //全部传送成功才算成功，如果有任意一个或多个文件传送失败，那么就会返回 False
            return blnReturn;
        }

        public MemoryStream ReadFileFromPhone(string fileOnPhone)
        {
            return this.ReadFileFromPhone(this.mHandleAfc2, fileOnPhone, null);
        }

        public MemoryStream ReadFileFromPhone(string fileOnPhone, DataTransferEventHandler progressCallback)
        {
            return this.ReadFileFromPhone(this.mHandleAfc2, fileOnPhone, progressCallback);
        }

        public MemoryStream ReadFileFromPhone(IntPtr afcHandle, string fileOnPhone, DataTransferEventHandler progressCallback)
        {
            if (!this.Exists(afcHandle, fileOnPhone))
                return null;

            MemoryStream stream = null;

            byte[] buffer = new byte[mDataTransfer_Buffer_Size + 1];
            string strErrMsg = "";

            iPhoneFileInfo info = null;

            //如果下载的是文件
            if (this.IsDirectory(afcHandle, fileOnPhone, ref info) == false)
            {
                DataTransferEventArgs e = new DataTransferEventArgs(fileOnPhone, "");

                try
                {
                    if (info.LinkTarget.Length > 0)
                        fileOnPhone = this.GetDirectoryName(fileOnPhone) + "/" + info.LinkTarget;

                    iPhoneFile iFile = iPhoneFile.OpenRead(this, afcHandle, fileOnPhone);
                    if (iFile == null)
                        goto DO_NEXT;

                    int readCount = iFile.Read(buffer, 0, buffer.Length);
                    if (readCount < 0)
                    {
                        iFile.Close();
                        goto DO_NEXT;
                    }

                    DateTime dblTimer = DateTime.Now;
                    long lngBuffer = 0;
                    int intSpeed = 0;

                    e.FileSize = this.FileSize(afcHandle, fileOnPhone);
                    stream = new MemoryStream();

                    try
                    {
                        while (readCount > 0)
                        {
                            if (progressCallback != null)
                            {
                                //=== 计算传输速度、更新状态 =========================================
                                lngBuffer += readCount;
                                double dblTimePast = ((TimeSpan)DateTime.Now.Subtract(dblTimer)).TotalSeconds;
                                if (dblTimePast >= 1)
                                {
                                    intSpeed = (int)(lngBuffer / dblTimePast);
                                    dblTimer = DateTime.Now;
                                    lngBuffer = 0;
                                }

                                e.Speed = intSpeed;
                                e.LastTransSize = readCount;
                                //==========================================================

                                progressCallback(e);

                                //用户是否放弃传输
                                if (e.Cancel)
                                    break;
                            }

                            stream.Write(buffer, 0, readCount);
                            readCount = iFile.Read(buffer, 0, buffer.Length);
                            e.TransSize += readCount;
                        }

                        iFile.Close();
                        e.TransSize = e.FileSize;

                    }
                    catch (Exception)
                    {
                        if (iFile != null)
                            iFile.Close();
                    }

                }
                catch (Exception ex)
                {
                    strErrMsg = ex.Message;
                    Common.LogException(ex.ToString(), "iPhoneDevice.ReadFileFromPhone");
                }

            DO_NEXT:
                if (progressCallback != null)
                    progressCallback(e);
            }

            return stream;
        }

        #endregion

        #region --- Operation文件操作 ---

        public string[] OperationGetFiles(IntPtr afcHandle, string strPath)
        {
            return OperationGetFiles(afcHandle, strPath, false);
        }

        public string[] OperationGetFiles(IntPtr afcHandle, string strPath, bool includeDirectory)
        {
            List<string> list = new List<string>();

            IntPtr hPath = IntPtr.Zero;
            IntPtr hResult = IntPtr.Zero;
            try
            {
                hPath = CoreFoundation.StringToCFString(strPath);

                //hResult=MobileDevice.AFCOperationCreateReadDirectory(

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "OperationGetFiles");
            }
            finally
            {
                if (hPath != IntPtr.Zero)
                    CoreFoundation.CFRelease(hPath);

                if (hResult != IntPtr.Zero)
                    CoreFoundation.CFRelease(hResult);
            }

            return list.ToArray();
        }

        public long OperationFileSize(IntPtr afcHandle, string strPath)
        {
            long lngSize = 0;

            return lngSize;
        }

        public bool OperationCreateDirectory(IntPtr afcHandle, string strPath)
        {
            return true;
        }

        public string[] OperationGetDirectories(IntPtr afcHandle, string strPath)
        {
            ArrayList aryPath = new ArrayList();


            return ((string[])(aryPath.ToArray(typeof(string))));
        }

        public bool OperationHasDirectories(IntPtr afcHandle, string strPath, params string[] args)
        {
            bool blnReturn = false;


            return blnReturn;
        }

        public bool OperationRename(IntPtr afcHandle, string sourceName, string destName)
        {
            IntPtr hAFCPathSrc = CoreFoundation.StringToHeap(sourceName, System.Text.Encoding.UTF8);
            IntPtr hAFCPathDes = CoreFoundation.StringToHeap(destName, System.Text.Encoding.UTF8);
            int intRet = 0;

            intRet = MobileDevice.AFCRenamePath(afcHandle, hAFCPathSrc, hAFCPathDes);

            return (intRet == 0);
        }

        public bool OperationExists(IntPtr afcHandle, string strPath)
        {
            bool result = false;

            try
            {
                uint data_size = 0;
                IntPtr data = IntPtr.Zero;
                IntPtr hAFCPath = CoreFoundation.StringToHeap(strPath, System.Text.Encoding.UTF8);

                if (miTunes82)
                {
                    if (MobileDevice.AFCFileInfoOpen(afcHandle, hAFCPath, ref data) == 0)
                        result = true;
                }
                else
                {
                    if (MobileDevice.AFCGetFileInfo(afcHandle, hAFCPath, ref data, ref data_size) == 0)
                        result = true;
                }

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iPhoneDevice.OperationExists");
            }

            return result;
        }

        public iPhoneFileInfo OperationGetFileInfo(IntPtr afcHandle, string strPath)
        {
            iPhoneFileInfo fileInfo = null;

            this.IsDirectory(afcHandle, strPath, ref fileInfo);

            return fileInfo;
        }

        public bool OperationIsDirectory(IntPtr afcHandle, string strPath)
        {
            iPhoneFileInfo iInfo = null;
            return OperationIsDirectory(afcHandle, strPath, ref iInfo);
        }

        public bool OperationIsDirectory(IntPtr afcHandle, string strPath, ref iPhoneFileInfo e)
        {
            bool is_dir = false;
            string strAttributes = "";
            IntPtr hAFCPath = CoreFoundation.StringToHeap(strPath, System.Text.Encoding.UTF8);

            kAMDError inError = kAMDError.kAMDUndefinedError;
            IntPtr data = IntPtr.Zero;

            string strName = string.Empty;
            string strValue = string.Empty;

            if (miTunes82)
            {
                inError = (kAMDError)(MobileDevice.AFCFileInfoOpen(afcHandle, hAFCPath, ref data));

                if (inError != kAMDError.kAMDSuccess)
                {
                    goto DO_EXIST;
                }

                while (true)
                {
                    IntPtr intName = IntPtr.Zero;
                    IntPtr intValue = IntPtr.Zero;

                    MobileDevice.AFCKeyValueRead(data, ref intName, ref intValue);
                    strName = Marshal.PtrToStringAnsi(intName);

#if MAC || IOS
					strValue = Marshal.PtrToStringAnsi(intValue);
#else
                    //"目标位置"有时包含中文，因此使用UTF8获取字符串
                    int length = API.lstrlenA(intValue);
                    if (length > 0)
                    {
                        byte[] arrData = new byte[length];
                        Marshal.Copy(intValue, arrData, 0, length);
                        strValue = Encoding.UTF8.GetString(arrData);
                    }
#endif

                    if (string.IsNullOrEmpty(strName))
                        break;

                    //记录 Attributes 字串
                    strAttributes += strName + "=" + strValue + ";";

                    if (strName == "st_ifmt")
                    {
                        if (strValue == "S_IFDIR")
                        {
                            is_dir = true;
                        }
                        else if (strValue == "S_IFLNK")
                        {

                            //如果该文件是链接文件，那么进一步判断此链接文件所连接的目标文件是否是目录
                            //-------------------------------------------------------------------------------------
                            IntPtr hAFCDir = new IntPtr();

                            if (MobileDevice.AFCDirectoryOpen(afcHandle, hAFCPath, ref hAFCDir) == 0)
                            {
                                is_dir = true;

                                MobileDevice.AFCDirectoryClose(afcHandle, hAFCDir);
                            }
                            else
                            {
                                is_dir = false;
                            }
                        }
                        else
                        {
                            is_dir = false;
                        }
                    }
                }

                MobileDevice.AFCKeyValueClose(data);
            }

            e = new iPhoneFileInfo(strAttributes, is_dir, mTimeZoneOffsetFromUTC, strPath);

        DO_EXIST:
            //CoreFoundation.FreeCoTaskMem(hAFCPath)

            return is_dir;
        }

        private bool OperationInternalDeleteDirectory(IntPtr afcHandle, string path, FileDeleteHandler callback)
        {
            if (!this.IsConnected)
                return false;

            IntPtr hAFCDir = new IntPtr();
            kAMDError inError = kAMDError.kAMDSuccess;
            IntPtr hAFCPath = CoreFoundation.StringToHeap(path, System.Text.Encoding.UTF8);
            //Dim hAFCPath As IntPtr = CoreFoundation.StringToCFString(path)

            inError = (kAMDError)(MobileDevice.AFCDirectoryOpen(afcHandle, hAFCPath, ref hAFCDir));
            //CoreFoundation.FreeCoTaskMem(hAFCPath)

            if (inError != kAMDError.kAMDSuccess)
                return false;

            string buffer = null;
            List<string> strFolderList = new List<string>();
            List<string> strFileList = new List<string>();

            FileDeleteEventArgs args = new FileDeleteEventArgs(path);

            do
            {
                if (!this.IsConnected)
                    return false;

                System.Windows.Forms.Application.DoEvents();
                MobileDevice.AFCDirectoryRead(afcHandle, hAFCDir, ref buffer);

                if (buffer != null && buffer != "." && buffer != "..")
                {
                    string strSubPath = string.Empty;
                    if (path.LastIndexOf('/') == path.Length - 1) // path.EndsWith("/")
                        strSubPath = string.Format("{0}{1}", path, buffer);
                    else
                        strSubPath = string.Format("{0}/{1}", path, buffer);

                    if (IsDirectory(afcHandle, strSubPath))
                        strFolderList.Add(strSubPath);
                    else
                        strFileList.Add(strSubPath);
                }

            } while (buffer != null);

            MobileDevice.AFCDirectoryClose(afcHandle, hAFCDir);

            foreach (string strPath in strFileList)
            {
                if (callback != null)
                {
                    args.FilePath = strPath;
                    args.IsDirectory = false;
                    callback(args);

                    if (args.Cancel)
                        return false;
                }

                System.Windows.Forms.Application.DoEvents();
                DeleteFile(afcHandle, strPath);
            }

            foreach (string strPath in strFolderList)
            {
                if (!OperationInternalDeleteDirectory(afcHandle, strPath, callback))
                    return false;
            }

            if (callback != null)
            {
                args.FilePath = path;
                args.IsDirectory = true;
                callback(args);

                if (args.Cancel)
                    return false;
            }

            System.Windows.Forms.Application.DoEvents();
            this.OperationDeleteDirectory(afcHandle, path);

            return true;
        }

        public void OperationDeleteDirectory(IntPtr afcHandle, string strPath)
        {

            if (OperationIsDirectory(afcHandle, strPath))
            {
                try
                {
                    IntPtr hAFCPath = CoreFoundation.StringToHeap(strPath, System.Text.Encoding.UTF8);

                    MobileDevice.AFCRemovePath(afcHandle, hAFCPath);
                    //CoreFoundation.FreeCoTaskMem(hAFCPath)
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "iPhoneDevice.OperationDeleteDirectory");
                }
            }

        }

        public void OperationDeleteDirectory(string strPath, bool recursive)
        {
            OperationDeleteDirectory(this.mHandleAfc2, strPath, recursive, null);
        }

        public void OperationDeleteDirectory(IntPtr afcHandle, string strPath, bool recursive)
        {
            OperationDeleteDirectory(afcHandle, strPath, recursive, null);
        }

        public void OperationDeleteDirectory(IntPtr afcHandle, string strPath, bool recursive, FileDeleteHandler callback)
        {
            ////if (!recursive)
            ////{
            ////    OperationDeleteDirectory(afcHandle, strPath);
            ////}
            ////else
            ////{
            ////    if (OperationIsDirectory(afcHandle, strPath))
            ////    {
            ////        OperationInternalDeleteDirectory(afcHandle, strPath, callback);
            ////    }
            ////}
        }

        public void OperationDeleteFile(IntPtr afcHandle, string strPath)
        {
            if (this.OperationExists(afcHandle, strPath))
            {
                try
                {
                    IntPtr hAFCPath = CoreFoundation.StringToHeap(strPath, System.Text.Encoding.UTF8);

                    MobileDevice.AFCRemovePath(afcHandle, hAFCPath);
                    //CoreFoundation.FreeCoTaskMem(hAFCPath)
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "iPhoneDevice.DeleteFile");
                }
            }
        }

        public void OperationCheckFolder(string strFolder, IntPtr hAFC)
        {
            if (!this.OperationExists(hAFC, strFolder))
            {
                string[] strNames = strFolder.Split(new char[] { '/' }, StringSplitOptions.RemoveEmptyEntries);
                string strPath = "";
                foreach (string strName in strNames)
                {
                    strPath += "/" + strName;

                    if (!this.OperationExists(hAFC, strPath))
                        this.OperationCreateDirectory(hAFC, strPath);
                }
            }
        }

        #endregion

        #region --- Operation文件传输 ---

        public bool OperationCopyToPhone(IntPtr afcHandle, string fileOnPC, string fileOnPhone)
        {
            List<string> list = null;
            return this.OperationCopyToPhone(afcHandle, fileOnPC, fileOnPhone, null, ref list);
        }

        public bool OperationCopyToPhone(IntPtr afcHandle, string fileOnPC, string fileOnPhone, DataTransferEventHandler progressCallback)
        {
            List<string> list = null;
            return this.OperationCopyToPhone(afcHandle, fileOnPC, fileOnPhone, progressCallback, ref list);
        }

        public bool OperationCopyToPhone(IntPtr afcHandle, string fileOnPC, string fileOnPhone, DataTransferEventHandler progressCallback, ref List<string> succeedListFile)
        {
            bool blnCancel = false;
            return this.OperationCopyToPhone(afcHandle, fileOnPC, fileOnPhone, progressCallback, ref succeedListFile, ref blnCancel);
        }

        private bool OperationCopyToPhone(IntPtr afcHandle, string fileOnPC, string fileOnPhone, DataTransferEventHandler progressCallback, ref List<string> succeedListFile, ref bool blnCancel)
        {
            bool blnReturn = true;
            byte[] arrBuffer = null;

            fileOnPC = fileOnPC.TrimEnd(Path.DirectorySeparatorChar);

            //递归循环上传时，会自动对fileOnPhone以“/”结尾进行赋值，此时需要重新组装目标路径fileOnPhone
            if (fileOnPhone.EndsWith("/"))
                fileOnPhone = fileOnPhone + Path.GetFileName(fileOnPC);

            long lngPosition = 0;
            if (File.Exists(fileOnPC))
            {
                arrBuffer = new byte[mDataTransfer_Buffer_Size + 1];
                //上传文件
                DataTransferEventArgs args = new DataTransferEventArgs(fileOnPhone, fileOnPC);

                using (System.IO.FileStream fs = System.IO.File.OpenRead(fileOnPC))
                {
                    iPhoneFile iFile = null;

                    try
                    {
                        iFile = iPhoneFile.OpenWrite(this, afcHandle, fileOnPhone);
                        if (iFile == null)
                        {
                            blnReturn = false;
                            args.Success = false;

                            goto DO_NEXT;
                        }

                        int readCount = fs.Read(arrBuffer, 0, arrBuffer.Length);

                        DateTime dblTimer = DateTime.Now;
                        long lngBuffer = 0;
                        int intSpeed = 0;

                        args.FileSize = fs.Length;

                        while (readCount > 0)
                        {

                            if (progressCallback != null)
                            {
                                //=== 计算传输速度、更新状态 =========================================
                                lngBuffer += readCount;
                                double dblTimePast = ((TimeSpan)DateTime.Now.Subtract(dblTimer)).TotalSeconds;
                                if (dblTimePast >= 1)
                                {
                                    intSpeed = (int)(lngBuffer / dblTimePast);
                                    dblTimer = DateTime.Now;
                                    lngBuffer = 0;
                                }

                                args.Speed = intSpeed;
                                args.LastTransSize = readCount;
                                //==========================================================

                                progressCallback(args);

                                //用户是否放弃传输
                                if (args.Cancel)
                                {
                                    //删除已经上传了一部分的文件
                                    this.DeleteFile(afcHandle, fileOnPhone);
                                    blnCancel = true;
                                    blnReturn = false;
                                    break;
                                }
                            }


                            iFile.Write(arrBuffer, 0, readCount);
                            args.TransSize += readCount;

                            //Console.WriteLine(buffer.Length & "," & file.Position & "," & e.TransSize)

                            //每写入100M进入写入位置校验一次
                            if (args.TransSize % 104857600 == 0)
                            {
                                lngPosition = iFile.Position;
                                if (lngPosition == 0 || lngPosition < args.TransSize)
                                {
                                    args.ErrorType = ErrorType.DiskIsFull;
                                    throw (new Exception("磁盘空间不足！"));
                                }
                            }

                            readCount = fs.Read(arrBuffer, 0, arrBuffer.Length);
                        }

                        args.LastTransSize = 0;
                        args.TransSize = args.FileSize;

                        if (!args.Cancel && args.FileSize != iFile.Position)
                        {
                            args.ErrorType = ErrorType.DiskIsFull;
                            blnCancel = true;
                            throw (new Exception("磁盘空间不足！"));
                        }

                        iFile.Close();
                    }
                    catch (Exception ex)
                    {
                        if (iFile != null)
                        {
                            iFile.Close();
                            this.DeleteFile(afcHandle, fileOnPhone);
                        }

                        Common.Log(ex.ToString() + "\t" + "lngPosition:" + lngPosition + "\t" + "TransSize:" + args.TransSize);

                        blnReturn = false;
                        args.LastErrMsg = ex.Message;
                        args.Success = blnReturn;

                    }
                DO_NEXT:
                    1.GetHashCode(); //nop
                }


                if (progressCallback != null)
                    progressCallback(args);

                if (blnReturn && succeedListFile != null)
                    //添加已经上传成功的文件列表
                    succeedListFile.Add(fileOnPhone);

            }
            else if (Directory.Exists(fileOnPC))
            {
                //上传文件夹
                if (!this.Exists(afcHandle, fileOnPhone))
                {
                    if (!this.CreateDirectory(afcHandle, fileOnPhone))
                        blnReturn = false;
                }

                //添加要上传的目录
                if (succeedListFile != null)
                    succeedListFile.Add(fileOnPhone);

                //如果上传的目录，同时搜索该目录下的其他文件
                string strOniPhone = fileOnPhone;
                if (!strOniPhone.EndsWith("/"))
                    strOniPhone = fileOnPhone + "/";

                foreach (string strfile in Directory.GetFiles(fileOnPC))
                {
                    if (!this.OperationCopyToPhone(afcHandle, strfile, strOniPhone, progressCallback, ref succeedListFile, ref blnCancel))
                        blnReturn = false;

                    if (blnCancel)
                        goto DO_EXIST;
                }

                //如果上传的目录，同时搜索该目录下的其他文件夹
                foreach (string strfolder in System.IO.Directory.GetDirectories(fileOnPC))
                {
                    if (!this.OperationCopyToPhone(afcHandle, strfolder, strOniPhone, progressCallback, ref succeedListFile, ref blnCancel))
                        blnReturn = false;

                    if (blnCancel)
                        goto DO_EXIST;
                }
            }
            else
            {
                //文件或者文件夹不存在
                blnReturn = false;
            }

        DO_EXIST:
            if (arrBuffer != null)
                arrBuffer = null;

            //全部传送成功才算成功，如果有任意一个或多个文件传送失败，那么就会返回 False
            return blnReturn;
        }

        public bool OperationDownFromPhone(IntPtr afcHandle, string fileOnPhone, string fileOnPC)
        {
            List<string> list = null;
            return this.OperationDownFromPhone(afcHandle, fileOnPhone, fileOnPC, null, ref list);
        }

        public bool OperationDownFromPhone(IntPtr afcHandle, string fileOnPhone, string fileOnPC, DataTransferEventHandler progressCallback)
        {
            List<string> list = null;
            return this.OperationDownFromPhone(afcHandle, fileOnPhone, fileOnPC, progressCallback, ref list);
        }

        public bool OperationDownFromPhone(IntPtr afcHandle, string fileOnPhone, string fileOnPC, DataTransferEventHandler progressCallback, ref List<string> list)
        {
            bool blnCancel = false;
            return this.OperationDownFromPhone(afcHandle, fileOnPhone, fileOnPC, progressCallback, ref list, ref blnCancel);
        }

        private bool OperationDownFromPhone(IntPtr afcHandle, string fileOnPhone, string fileOnPC, DataTransferEventHandler progressCallback, ref List<string> succeedListFile, ref bool blnCancel)
        {
            if (File.Exists(fileOnPC))
            {
                try
                {
                    File.Delete(fileOnPC);
                }
                catch
                {
                    return false;
                }
            }

            if (!this.OperationExists(afcHandle, fileOnPhone))
                return false;

            bool blnReturn = true;

            byte[] arrBuffer = null;
            string strErrMsg = string.Empty;

            iPhoneFileInfo info = null;

            //递归循环下载时，会自动对fileOnPC以“\\”结尾进行赋值，此时需要重新组装下载路径
            if (fileOnPC.EndsWith(Path.DirectorySeparatorChar.ToString()))
                fileOnPC = fileOnPC + Utility.ReplaceWinIllegalName(GetFileName(fileOnPhone));

            //如果下载的是文件
            if (!this.OperationIsDirectory(afcHandle, fileOnPhone, ref info))
            {
                if (info == null)
                    goto DO_EXIT;

                DataTransferEventArgs e = new DataTransferEventArgs(fileOnPhone, fileOnPC);
                arrBuffer = new byte[mDataTransfer_Buffer_Size + 1];

                try
                {
                    if (info.LinkTarget.Length > 0)
                    {
                        if (info.LinkTarget.StartsWith("/"))
                            fileOnPhone = info.LinkTarget;
                        else
                            fileOnPhone = this.GetDirectoryName(fileOnPhone) + "/" + info.LinkTarget;
                    }

                    using (System.IO.FileStream fs = new FileStream(fileOnPC, FileMode.Create))
                    {
                        //If fileOnPhone = "/PhotoData/Photos.sqlite" Then
                        //    Stop
                        //End If

                        iPhoneFile iFile = iPhoneFile.OpenRead(this, afcHandle, fileOnPhone);
                        if (iFile == null)
                        {
                            blnReturn = false;

                            goto DO_NEXT;
                        }

                        int iReadCount = iFile.Read(arrBuffer, 0, arrBuffer.Length);
                        if (iReadCount < 0)
                        {
                            iFile.Close();
                            blnReturn = false;

                            goto DO_EXIT;
                        }

                        DateTime dblTimer = DateTime.Now;
                        long lngBuffer = 0;
                        int intSpeed = 0;

                        e.FileSize = this.OperationFileSize(afcHandle, fileOnPhone);

                        try
                        {
                            while (iReadCount > 0)
                            {
                                if (progressCallback != null)
                                {
                                    //=== 计算传输速度、更新状态 =========================================
                                    lngBuffer += iReadCount;
                                    double dblTimePast = ((TimeSpan)DateTime.Now.Subtract(dblTimer)).TotalSeconds;
                                    if (dblTimePast >= 1)
                                    {
                                        intSpeed = (int)(lngBuffer / dblTimePast);
                                        dblTimer = DateTime.Now;
                                        lngBuffer = 0;
                                    }

                                    e.Speed = intSpeed;
                                    e.LastTransSize = iReadCount;
                                    //==========================================================

                                    progressCallback(e);

                                    //用户是否放弃传输
                                    if (e.Cancel)
                                    {
                                        blnCancel = true;
                                        blnReturn = false;

                                        break;
                                    }
                                }

                                //Dim lenCount As Integer = IIf(buffer.Length >= iReadCount, iReadCount, buffer.Length)
                                //If iReadCount > buffer.Length Then
                                //    blnReturn = False
                                //    GoTo DO_NEXT
                                //End If
                                fs.Write(arrBuffer, 0, iReadCount);
                                e.TransSize += iReadCount;

                                iReadCount = iFile.Read(arrBuffer, 0, arrBuffer.Length);
                            }

                            iFile.Close();
                            e.LastTransSize = 0;
                            e.TransSize = e.FileSize;

                        }
                        catch (Exception)
                        {
                            if (iFile != null)
                                iFile.Close();

                            blnReturn = false;
                        }
                    }

                DO_NEXT:
                    if (e.Cancel)
                    {
                        try
                        {
                            if (File.Exists(fileOnPC))
                                File.Delete(fileOnPC);
                        }
                        catch (Exception)
                        { }
                    }

                }
                catch (Exception ex)
                {
                    try
                    {
                        //当下载失败时，如果下载的文件大小为0，则直接删除空文件
                        if (File.Exists(fileOnPC) && new FileInfo(fileOnPC).Length == 0)
                            File.Delete(fileOnPC);
                    }
                    catch
                    { }
                    strErrMsg = ex.Message;

                    blnReturn = false;
                }

                if (progressCallback != null)
                    progressCallback(e);

                if (blnReturn && succeedListFile != null)
                {
                    //添加已经下载成功的文件列表
                    succeedListFile.Add(fileOnPC);
                }

            }
            else
            {
                //如果下载的是目录
                if (!System.IO.Directory.Exists(fileOnPC))
                    System.IO.Directory.CreateDirectory(fileOnPC);

                //添加要下载的目录
                if (succeedListFile != null)
                    succeedListFile.Add(fileOnPC);

                //如果下载的是目录，同时搜索该目录下的其他文件
                string strOnComputer = fileOnPC + Path.DirectorySeparatorChar.ToString();
                foreach (string strfile in this.OperationGetFiles(afcHandle, fileOnPhone, true))
                {
                    if (!this.OperationDownFromPhone(afcHandle, fileOnPhone.TrimEnd('/'.ToString().ToCharArray()) + "/" + strfile, strOnComputer, progressCallback, ref succeedListFile, ref blnCancel))
                        blnReturn = false;

                    if (blnCancel)
                        goto DO_EXIT;
                }

            }

        DO_EXIT:
            if (arrBuffer != null)
                arrBuffer = null;

            //全部传送成功才算成功，如果有任意一个或多个文件传送失败，那么就会返回 False
            return blnReturn;
        }

        #endregion

        #region --- 对外函数 ---

        public IntPtr CreateMobileConnection(ref int inSocket)
        {
            //=== 以 com.apple.afc 方式建立连接，并传输软件到 Device ===================================
            if (!this.StartService("com.apple.afc", ref inSocket, false))
                return IntPtr.Zero;

            return this.OpenAFCConnection(inSocket, true);
        }

        public kAMDError CloseMobileConnection(ref int inSocket)
        {
            if (this.mDictAfcSocketAndConnHandle.ContainsKey(inSocket))
            {
                IntPtr hAFC = this.mDictAfcSocketAndConnHandle[inSocket];
                this.CloseAFCConnection(ref hAFC, true);

                this.mDictAfcSocketAndConnHandle.Remove(inSocket);
            }

            kAMDError inError = this.StopService(ref inSocket);

            return inError;
        }

        public kAMDError Connect()
        {
            kAMDError inError = kAMDError.kAMDSuccess;

            try
            {
                if (!this.mIsConnectActive)
                {
                    inError = (kAMDError)(MobileDevice.AMDeviceConnect(this.mDeviceRef));
                    if (inError == kAMDError.kAMDSuccess)
                        mIsConnectActive = true;
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                Common.LogException(ex.ToString(), "iPhoneDevice.Connect");
            }

            return inError;
        }

        public kAMDError Disconnect()
        {
            kAMDError inError = kAMDError.kAMDSuccess;

            this.mIsConnectActive = false;
            try
            {
                inError = (kAMDError)(MobileDevice.AMDeviceDisconnect(this.mDeviceRef));
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                Common.LogException(ex.ToString(), "iPhoneDevice.Disconnect");
            }

            return inError;
        }

        public kAMDError StartSession()
        {
            return StartSession(false);
        }

        public kAMDError StartSession(bool blnHeartbeat)
        {
            kAMDError inError = kAMDError.kAMDSuccess;

            try
            {
                if (!this.mIsSessionActive)
                {
                    inError = (kAMDError)(MobileDevice.AMDeviceStartSession(this.mDeviceRef));

                    switch (inError)
                    {
                        case kAMDError.kAMDInvalidHostIDError:
                            //iPCU got an invalid host error while attaching to the device. iPCU will try to unpair and re-pair.
                            if ((kAMDError)MobileDevice.AMDeviceUnpair(this.mDeviceRef) == kAMDError.kAMDSuccess)
                            {
                                //Un-pairing worked, trying to re-pair now."
                                if ((kAMDError)MobileDevice.AMDevicePair(this.mDeviceRef) == kAMDError.kAMDSuccess)
                                {
                                    //Re-pairing with the device worked, trying to start another session.
                                    inError = (kAMDError)(MobileDevice.AMDeviceStartSession(this.mDeviceRef));
                                }
                                else
                                {
                                    //Re-pairing with the device was not successful.
                                }
                            }
                            else
                            {
                                //"Un-pairing with the device was not successful."
                            }
                            break;
                        case kAMDError.kAMDSuccess:
                            //If blnHeartbeat Then
                            //    Me.mSerialNumber = CStr(Me.GetDeviceValue(DeviceInfoKey.SerialNumber))
                            //End If
                            this.mIsSessionActive = true;
                            break;

                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                Common.LogException(ex.ToString(), "iPhoneDevice.StartSession");
            }

            return inError;
        }

        public kAMDError StopSession()
        {
            kAMDError inError = kAMDError.kAMDSuccess;
            this.mIsSessionActive = false;

            try
            {
                inError = (kAMDError)(MobileDevice.AMDeviceStopSession(this.mDeviceRef));
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                Common.LogException(ex.ToString(), "iPhoneDevice.StopSession");
            }

            return inError;
        }

        public bool EnterRecoveryMode()
        {
            bool returnValue = false;

            try
            {
                bool blnConnectBySelf = false;
                bool blnStartSessionBySelf = false;
                kAMDError inError = kAMDError.kAMDSuccess;

                if (!this.mIsConnectActive)
                {
                    inError = this.Connect();
                    if (inError == kAMDError.kAMDSuccess)
                        blnConnectBySelf = true;
                    else
                        goto DO_EIXT;
                }

                if (!this.mIsSessionActive)
                {
                    inError = this.StartSession(false);
                    if (inError == kAMDError.kAMDSuccess)
                        blnStartSessionBySelf = true;
                    else
                        goto DO_DISCONNECT;
                }

                inError = (kAMDError)(MobileDevice.AMDeviceEnterRecovery(this.mDeviceRef));
                if (inError == kAMDError.kAMDSuccess)
                    returnValue = true;

                if (blnStartSessionBySelf)
                    this.StopSession();

                DO_DISCONNECT:
                if (blnConnectBySelf)
                    this.Disconnect();

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "DeviceEnterRecovery");
            }

        DO_EIXT:
            return returnValue;
        }

        #endregion

        #region --- 消息收发 ---

        private ServiceInfo GetService(int iSocket)
        {
            ServiceInfo info = null;

            if (mDictSocketAndServiceInfo.ContainsKey(iSocket))
                info = mDictSocketAndServiceInfo[iSocket];

            return info;
        }

        public object receive_message(int sock)
        {
            object objValue = null;

            try
            {
                if (sock < 0)
                    goto DoExit;

                int recvLen = -1;
                UInt32 dataSize = 0;
                UInt32 socketDataSize = 0;
                IntPtr recvPtr = IntPtr.Zero;
                UInt32 replySize = 0;

                ServiceInfo info = this.GetService(sock);

                if (this.mConnectMode == ConnectMode.USB && (info == null || !info.IsSSL))
                {
                    recvLen = MobileDevice.recv_UInt32(sock, ref socketDataSize, 4, 0);
                    if (recvLen != 4)
                    {
                        Common.LogException("Could not receive size of message.");
                        goto DoExit;
                    }

                    dataSize = MobileDevice.ntohl(socketDataSize);

                    //dataSize数据量大的时候，会比较大
                    if (dataSize < 0)
                    {
                        Common.LogException("receive size error, dataSize:" + dataSize);
                        goto DoExit;
                    }
                    else if (dataSize == 0)
                    {
                        goto DoExit;
                    }

                    recvPtr = Marshal.AllocCoTaskMem(System.Convert.ToInt32(dataSize));
                    if (recvPtr == IntPtr.Zero)
                    {
                        Common.LogException("Could not allocate message buffer.");
                        goto DoExit;
                    }


                    IntPtr tmpRecvPtr = recvPtr;
                    while (replySize < dataSize)
                    {
                        //接收的时候指针要移动
                        recvLen = MobileDevice.recv(sock, tmpRecvPtr, Convert.ToInt32(dataSize - replySize), 0);
                        if (recvLen <= -1)
                        {
                            Common.LogException(string.Format("Could not receive secure message: {0}", recvLen));
                            replySize = System.Convert.ToUInt32(dataSize + 1);
                        }
                        else if (recvLen == 0)
                        {
                            Common.LogException("receive size is zero. ");
                            break;
                        }
                        else
                        {
                            replySize += (uint)recvLen;
                            //指针要移动recvLen
                            tmpRecvPtr = Common.CreateIntPtr(tmpRecvPtr, recvLen);
                        }
                    }
                }
                else
                {
                    recvLen = MobileDevice.AMDServiceConnectionReceive_UInt32(info.hService, ref socketDataSize, 4);
                    if (recvLen != 4)
                    {
                        Common.LogException(string.Format("Could not receive size of message2.recvLen: {0}, socketDataSize: {1}", recvLen, socketDataSize));
                        goto DoExit;
                    }

                    dataSize = MobileDevice.ntohl(socketDataSize);

                    //dataSize数据量大的时候，会比较大
                    if (dataSize <= 0)
                    {
                        Common.LogException("receive size error, dataSize2:" + dataSize);
                        goto DoExit;
                    }

                    recvPtr = Marshal.AllocCoTaskMem(System.Convert.ToInt32(dataSize));
                    if (recvPtr == IntPtr.Zero)
                    {
                        Common.LogException("Could not allocate message buffer2.");
                        goto DoExit;
                    }


                    IntPtr tmpRecvPtr = recvPtr;
                    while (replySize < dataSize)
                    {
                        //接收的时候指针要移动
                        recvLen = MobileDevice.AMDServiceConnectionReceive(info.hService, tmpRecvPtr, System.Convert.ToInt32(dataSize - replySize));
                        if (recvLen <= -1)
                        {
                            Common.LogException(string.Format("WIFI Could not receive secure message2: {0}", recvLen));
                            replySize = System.Convert.ToUInt32(dataSize + 1);
                        }
                        else if (recvLen == 0)
                        {
                            Common.LogException("WIFI receive size is zero2. ");
                            break;
                        }
                        else
                        {
                            replySize += (uint)recvLen;
                            //指针要移动recvLen
                            tmpRecvPtr = Common.CreateIntPtr(tmpRecvPtr, recvLen);
                        }
                    }
                }

                IntPtr dataPtr = IntPtr.Zero;
                IntPtr drictionaryPtr = IntPtr.Zero;
                if (replySize == dataSize)
                {
                    //dataPtr = CoreFoundation.CFDataCreateWithBytesNoCopy(CoreFoundation.kCFAllocatorDefault, recvPtr, CInt(dataSize), IntPtr.Zero)
                    dataPtr = CoreFoundation.CFDataCreate(CoreFoundation.kCFAllocatorDefault, recvPtr, System.Convert.ToInt32(dataSize));
                    if (dataPtr == IntPtr.Zero)
                    {
                        Common.LogException("Could not create CFData for message");
                    }
                    else
                    {
                        IntPtr errorString = IntPtr.Zero;
                        drictionaryPtr = CoreFoundation.CFPropertyListCreateFromXMLData(CoreFoundation.kCFAllocatorDefault, dataPtr, CFPropertyListMutabilityOptions.kCFPropertyListImmutable, ref errorString);

                        if (drictionaryPtr == IntPtr.Zero)
                        {
                            Common.LogException("Could not convert raw xml into a dictionary: " + CoreFoundation.ManagedTypeFromCFType(errorString).ToString());
                            goto DoExit;
                        }
                    }
                }

                if (dataPtr != IntPtr.Zero)
                    CoreFoundation.CFRelease(dataPtr);

                if (recvPtr != IntPtr.Zero)
                    Marshal.FreeCoTaskMem(recvPtr);

                objValue = CoreFoundation.ManagedTypeFromCFType(drictionaryPtr);
                if (drictionaryPtr != IntPtr.Zero)
                    CoreFoundation.CFRelease(drictionaryPtr);

#if LOG
                if (objValue != null)
                    Common.LogException(CoreFoundation.CreatePlistString(objValue));
#endif

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "receive_message");
            }

        DoExit:
            return objValue;
        }


        /// Remark on 2011.03.23
        //Public Function send_message_managed(ByVal sock As Integer, ByVal dict As IDictionary(Of Object, Object)) As Boolean
        //    Dim msgPtr As IntPtr = CoreFoundation.CFTypeFromManagedType(dict)
        //    Return Me.send_message(sock, msgPtr)
        //End Function

        /// Add on 2011.03.23
        public bool send_message_managed(int sock, object dict)
        {
            //Console.WriteLine(CoreFoundation.CreatePlistString(dict));

            IntPtr msgPtr = CoreFoundation.CFTypeFromManagedType(dict);
            return this.send_message(sock, msgPtr);
        }

        public bool send_message(int sock, IntPtr hMessage)
        {
            bool blnResult = false;

            if (sock < 0 || hMessage == IntPtr.Zero)
                goto DO_RETURN;

            //Dim intLen As Integer = CoreFoundation.CFDataGetLength(message)
            //Dim msgPtr As IntPtr = CoreFoundation.CFDataCreate(CoreFoundation.kCFAllocatorDefault, message, intLen)
            //Dim xmlPtr As IntPtr = CoreFoundation.CFPropertyListCreateFromXMLData(CoreFoundation.kCFAllocatorDefault, message, Nothing, Nothing)
            //Dim xmlPtr As IntPtr = CoreFoundation.CFPropertyListCreateXMLData(CoreFoundation.kCFAllocatorDefault, message)

            //Dim xmlDataPtr As IntPtr = CoreFoundation.CFPropertyListCreateXMLData(CoreFoundation.kCFAllocatorDefault, message)

            IntPtr hPlist = CoreFoundation.CFWriteStreamCreateWithAllocatedBuffers(IntPtr.Zero, IntPtr.Zero);
            if (hPlist == IntPtr.Zero)
                goto DO_RETURN;

            if (!CoreFoundation.CFWriteStreamOpen(hPlist))
                goto DO_RETURN;

            IntPtr errorString = IntPtr.Zero;
            if (CoreFoundation.CFPropertyListWriteToStream(hMessage, hPlist, CFPropertyListFormat.kCFPropertyListBinaryFormat_v1_0, ref errorString) <= 0)
                goto DO_EXIT;

            IntPtr hProperty = CoreFoundation.kCFStreamPropertyDataWritten;
            IntPtr hCopy = CoreFoundation.CFWriteStreamCopyProperty(hPlist, hProperty);
            IntPtr hBuffer = CoreFoundation.CFDataGetBytePtr(hCopy);

            int xmlLength = CoreFoundation.CFDataGetLength(hCopy);
            UInt32 socketDataSize = MobileDevice.htonl((uint)xmlLength);
            int len = Marshal.SizeOf(socketDataSize);

            ServiceInfo info = this.GetService(sock);

            if (this.mConnectMode == ConnectMode.USB && (info == null || !info.IsSSL))
            {
                if (MobileDevice.send_UInt32(sock, ref socketDataSize, len, 0) != len)
                {
                    Common.LogException("Could not send message size");
                }
                else if (MobileDevice.send(sock, hBuffer, xmlLength, 0) != xmlLength)
                {
                    Common.LogException("Could not send message.");
                }
                else
                {
                    blnResult = true;
                }
            }
            else
            {
                if (MobileDevice.AMDServiceConnectionSend_UInt32(info.hService, ref socketDataSize, len) != len)
                {
                    Common.LogException("WIFI Could not send message size");
                }
                else if (MobileDevice.AMDServiceConnectionSend(info.hService, hBuffer, xmlLength) != xmlLength)
                {
                    Common.LogException("WIFI Could not send message.");
                }
                else
                {
                    blnResult = true;
                }
            }

            CoreFoundation.CFRelease(hCopy);

        DO_EXIT:
            CoreFoundation.CFWriteStreamClose(hPlist);

        DO_RETURN:

#if LOG
            if (hMessage != IntPtr.Zero)
                Common.LogException(CoreFoundation.CreatePlistString(CoreFoundation.ManagedTypeFromCFType(hMessage)));
#endif
            return blnResult;
        }

        #endregion

        #region --- Springboardservices ---

        public bool StartSpringboardservices()
        {
            if (this.mSocketSpringboardservices <= 0)
                return this.StartService("com.apple.springboardservices", ref this.mSocketSpringboardservices, false);
            else
                return true;
        }

        public void CloseSpringboardservices()
        {
            this.StopService(ref this.mSocketSpringboardservices);
        }

        /// <summary>
        /// 获取程序图标
        /// </summary>
        /// <param name="bundleId">程序bundleID</param>
        /// <returns></returns>
        /// <remarks></remarks>
        public Image GetIconPNGData(string bundleId)
        {
            if (string.IsNullOrEmpty(bundleId))
                return null;

            Image retImage = null;

            bool blnConnectSelf = false;
            int inSocket = this.mSocketSpringboardservices;

            if (inSocket <= 0)
            {
                if (this.StartService("com.apple.springboardservices", ref inSocket, false))
                    blnConnectSelf = true;
                else
                    return null;
            }

            Dictionary<object, object> message = new Dictionary<object, object>();
            message.Add("command", "getIconPNGData");
            message.Add("bundleId", bundleId);

            if (this.send_message_managed(inSocket, message))
            {
                IDictionary<object, object> dataReply = (Dictionary<object, object>)(this.receive_message(inSocket));

                if (dataReply != null && dataReply.ContainsKey("pngData"))
                {
                    byte[] pngBytes = (byte[])(dataReply["pngData"]);
#if MAC || IOS
                    using (NSAutoreleasePool pool = new NSAutoreleasePool())
                    {
                        NSData dataTemp = NSData.FromArray(pngBytes);
                        if (dataTemp != null)
                        {
                            retImage = new Image(dataTemp);
                            dataTemp.Dispose();
                            dataTemp = null;
                        }
                    }
#else
                    retImage = Image.FromStream(new System.IO.MemoryStream(pngBytes));
#endif

                }
            }

            //如果是自己打开服务，需要自行关闭
            if (blnConnectSelf)
                this.StopService(ref inSocket);

            return retImage;
        }

        public string GetIconStatePlist()
        {
            try
            {
                object objPlist = this.GetIconState();
                if (objPlist != null)
                    mIconStatePlist = CoreFoundation.CreatePlistString(objPlist);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetIconStatePlist");
            }
            return IconStatePlist;
        }

        internal object GetIconState()
        {
            object dataReply = null;

            bool blnConnectSelf = false;
            int inSocket = this.mSocketSpringboardservices;

            if (inSocket <= 0)
            {
                if (this.StartService("com.apple.springboardservices", ref inSocket, false))
                    blnConnectSelf = true;
                else
                    return null;
            }

            Dictionary<object, object> message = new Dictionary<object, object>();
            string prodVersion = this.ProductVersion;

            message.Add("command", "getIconState");
            message.Add("formatVersion", "2");

            if (this.send_message_managed(inSocket, message))
                dataReply = this.receive_message(inSocket);

            //如果是自己打开服务，需要自行关闭
            if (blnConnectSelf)
                this.StopService(ref inSocket);

            return dataReply;
        }

        /// <summary>
        /// 设置当前设备图标位置
        /// </summary>
        /// <param name="objArys"></param>
        /// <returns></returns>
        /// <remarks></remarks>
        internal bool SetIconState(object objArys)
        {
            object dataReply = null;

            bool blnConnectSelf = false;
            int inSocket = 0;

            //Dim fName1 As String = String.Format("{0}\IconSave.plist", _
            //               Environment.GetFolderPath(Environment.SpecialFolder.Desktop))

            //CoreFoundation.WritePlist(objArys, fName1)

            //If Me.VersionNumber >= 700 Then
            //    Return Me.SetIconStateFor7X(objArys)
            //End If

            if (inSocket <= 0)
            {
                if (this.StartService("com.apple.springboardservices", ref inSocket, false))
                    blnConnectSelf = true;
                else
                    return false;
            }

            Dictionary<object, object> message = new Dictionary<object, object>();

            message.Add("command", "setIconState");
            message.Add("iconState", objArys);

            //Dim fName2 As String = String.Format("{0}\IconSave2.plist", _
            //                     Environment.GetFolderPath(Environment.SpecialFolder.Desktop))

            //CoreFoundation.WritePlist(CType(message, Object), fName2)

            if (this.send_message_managed(inSocket, message))
                dataReply = this.receive_message(inSocket);

            //如果是自己打开服务，需要自行关闭
            if (blnConnectSelf)
                this.StopService(ref inSocket);

            return true;
        }

        /// <summary>
        /// 获取当前界面方向
        /// </summary>
        /// <returns>1=纵向，4=横向</returns>
        /// <remarks></remarks>
        public int GetInterfaceOrientation()
        {
            int iRetVal = -1;

            bool blnConnectSelf = false;
            int inSocket = this.mSocketSpringboardservices;
            if (inSocket <= 0)
            {
                if (this.StartService("com.apple.springboardservices", ref inSocket, false))
                    blnConnectSelf = true;
                else
                    return iRetVal;
            }

            Dictionary<object, object> message = new Dictionary<object, object>();
            message.Add("command", "getInterfaceOrientation");

            if (this.send_message_managed(inSocket, message))
            {
                Dictionary<object, object> dataReply = (Dictionary<object, object>)(this.receive_message(inSocket));
                if (dataReply != null && dataReply.ContainsKey("interfaceOrientation"))
                    iRetVal = Convert.ToInt32(dataReply["interfaceOrientation"]);
            }

            //如果是自己打开服务，需要自行关闭			
            if (blnConnectSelf)
                this.StopService(ref inSocket);

            return iRetVal;
        }

        /// <summary>
        /// 获取设备墙纸
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public Image GetHomeScreenWallpaper()
        {
            Image retImage = null;

            bool blnConnectSelf = false;
            int inSocket = this.mSocketSpringboardservices;
            if (inSocket <= 0)
            {
                if (this.StartService("com.apple.springboardservices", ref inSocket, false))
                    blnConnectSelf = true;
                else
                    return retImage;
            }

            Dictionary<object, object> message = new Dictionary<object, object>();
            message.Add("command", "getHomeScreenWallpaperPNGData");

            if (this.send_message_managed(inSocket, message))
            {
                IDictionary<object, object> dataReply = (Dictionary<object, object>)(this.receive_message(inSocket));

                if (dataReply != null && dataReply.ContainsKey("pngData"))
                {
                    byte[] pngBytes = (byte[])(dataReply["pngData"]);
#if IOS || MAC
                    using (NSAutoreleasePool pool = new NSAutoreleasePool())
                    {
                        NSData dataTemp = NSData.FromArray(pngBytes);
                        if (dataTemp != null)
                        {
                            retImage = new Image(dataTemp);
                            dataTemp.Dispose();
                            dataTemp = null;
                        }
                    }
#else
                    retImage = System.Drawing.Image.FromStream(new System.IO.MemoryStream(pngBytes));
#endif
                }
            }

            //如果是自己打开服务，需要自行关闭
            if (blnConnectSelf)
                this.StopService(ref inSocket);

            return retImage;
        }

        /// <summary>
        /// iOS7图标管理的方式（同步的方式）
        /// </summary>
        private bool SetIconStateFor7X(object objArys)
        {
            bool blnResult = true;

            try
            {
                //生成plist文件
                string strSyncPlistFolderOnPc = Path.Combine(Folder.PublicStaging, System.IO.Path.GetFileName(this.Identifier));
                Folder.CheckFolder(strSyncPlistFolderOnPc);
                string strSyncPlistOnPc = Path.Combine(strSyncPlistFolderOnPc, "SpringboardIconState.plist");

                CoreFoundation.WritePlist(objArys, strSyncPlistOnPc);
                if (!File.Exists(strSyncPlistOnPc))
                {
                    blnResult = false;
                    goto DO_EXIT;
                }

                //发送准备同步的消息
                //Console.WriteLine("发送准备同步的消息")
                blnResult = this.ATHostSendSyncRequest(ATHostSyncType.Application);
                if (!blnResult)
                {
                    blnResult = false;
                    goto DO_EXIT;
                }

                //上传plist文件
                if (!this.UploadSpringboardIconStatePlist(strSyncPlistOnPc))
                {
                    blnResult = false;
                    goto DO_EXIT;
                }

                //发送上传所有文件完成的消息
                //Console.WriteLine("发送上传SpringboardIconState.plist文件完成的消息")
                blnResult = this.ATHostSendSyncFinished(false, ATHostSyncType.Application);
                if (!blnResult)
                {
                    blnResult = false;
                    goto DO_EXIT;
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                Common.LogException(ex.ToString(), "iPhoneDevice.SetIconStateFor7X");

                blnResult = false;
            }

        DO_EXIT:
            return blnResult;
        }

        /// <summary>
        ///  上传SpringboardIconState.plist
        /// </summary>
        private bool UploadSpringboardIconStatePlist(string strSyncPlistOnPc)
        {
            bool blnResult = true;

            try
            {
                string strSyncFolderOnPhone = "/PublicStaging/ApplicationSync";
                string strSyncPlistOnPhone = strSyncFolderOnPhone + "/SpringboardIconState.plist";

                this.CheckFolderByAFC(strSyncFolderOnPhone);

                blnResult = this.CopyToPhoneByAFC(strSyncPlistOnPc, strSyncPlistOnPhone, null);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                Common.LogException(ex.ToString(), "iPhoneDevice.UploadSpringboardIconStatePlist");

                blnResult = false;
            }

            return blnResult;
        }

        #endregion

        #region --- House_arrest ---

        //private void CreateAfcByFileSharing(string bundleId, ref int inSocket, ref IntPtr hHouseArrest)
        //{
        //    //如果之前有启动 house_arrest 服务，以及连接的话，先关掉
        //    //------------------------------------------------------------------------
        //    this.CloseAFCConnection(ref hHouseArrest, false);
        //    this.StopService(ref inSocket);

        //    //启动新的 house_arrest 服务
        //    //------------------------------------------------------------------------
        //    if (!this.StartService("com.apple.mobile.house_arrest", ref inSocket, false))
        //        return;

        //    bool blnReturn = false;
        //    try
        //    {
        //        Dictionary<object, object> message = new Dictionary<object, object>();

        //        //ios10.X以后企业签名包访问的机制发生改变。“VendDocuments”
        //        if (this.mVersionNumber >= 1000 && this.mDictInstalledApplications != null &&
        //            this.mDictInstalledApplications.ContainsKey(bundleId) &&
        //            this.mDictInstalledApplications[bundleId].CrackedInfo == CrakedInfo.Enterprise)
        //        {
        //            message.Add("Command", "VendDocuments");
        //        }
        //        else if (this.mVersionNumber < 830 || this.mDictInstalledApplications != null &&
        //            this.mDictInstalledApplications.ContainsKey(bundleId) &&
        //            (this.mDictInstalledApplications[bundleId].CrackedInfo == CrakedInfo.Enterprise ||
        //            this.mDictInstalledApplications[bundleId].CrackedInfo == CrakedInfo.Personal ||
        //            this.mDictInstalledApplications[bundleId].CrackedInfo == CrakedInfo.Experience ||
        //            this.mDictInstalledApplications[bundleId].CrackedInfo == CrakedInfo.Extreme))
        //        {
        //            message.Add("Command", "VendContainer");

        //        }
        //        else
        //        {
        //            message.Add("Command", "VendDocuments");
        //        }

        //        message.Add("Identifier", bundleId);

        //        if (this.send_message_managed(inSocket, message))
        //        {
        //            IDictionary<object, object> dataReply = null;

        //            do
        //            {
        //                dataReply = (Dictionary<object, object>)(this.receive_message(inSocket));

        //                if (dataReply != null)
        //                {
        //                    if (dataReply.ContainsKey("Status"))
        //                    {
        //                        string status = dataReply["Status"].ToString();

        //                        //判断执行的指令是否正确且完整的执行完毕
        //                        if (status.ToLower() == "complete")
        //                        {
        //                            blnReturn = true;
        //                            break;
        //                        }
        //                    }
        //                    else if (dataReply.ContainsKey("Error"))
        //                    {
        //                        //指令执行错误
        //                        blnReturn = false;
        //                    }

        //                }
        //            } while (dataReply != null);
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        Common.LogException(ex.ToString(), "iPhoneDevice.CreateAfcByFileSharing");

        //        blnReturn = false;
        //    }

        //    if (blnReturn)
        //        hHouseArrest = this.OpenAFCConnection(inSocket, false);
        //}

        private void CreateAfcByFileSharing(string bundleId, ref int inSocket, ref IntPtr hHouseArrest)
        {
            bool blnReturn = false;
            bool hasRetry = false;


            try
            {
                //付费或者免费个人开发者，或者为8.3-9.x的企业内部分发
                bool isDeveloperApp = this.CheckIsDeveloperApp(bundleId);
            DoRetry:

                //如果之前有启动 house_arrest 服务，以及连接的话，先关掉
                //------------------------------------------------------------------------
                this.CloseAFCConnection(ref hHouseArrest, false);
                this.StopService(ref inSocket);

                //启动新的 house_arrest 服务
                //------------------------------------------------------------------------
                if (!this.StartService("com.apple.mobile.house_arrest", ref inSocket, false))
                    return;

                Dictionary<object, object> message = new Dictionary<object, object>();
                message.Add("Command", isDeveloperApp ? "VendContainer" : "VendDocuments");
                message.Add("Identifier", bundleId);

                if (this.send_message_managed(inSocket, message))
                {
                    IDictionary<object, object> dataReply = null;
                    bool isWillGotoRetry = false;

                    do
                    {
                        dataReply = (Dictionary<object, object>)(this.receive_message(inSocket));

                        if (dataReply != null)
                        {
                            if (dataReply.ContainsKey("Status"))
                            {
                                string status = dataReply["Status"].ToString();

                                //判断执行的指令是否正确且完整的执行完毕
                                if (status.ToLower() == "complete")
                                {
                                    blnReturn = true;
                                    break;
                                }
                            }
                            else if (dataReply.ContainsKey("Error"))
                            {
                                // 如果获取不到，尝试另外一种方式获取
                                if (this.mVersionNumber >= 1000 && dataReply["Error"].ToString() == "InstallationLookupFailed")
                                {
                                    isDeveloperApp = !isDeveloperApp;
                                    isWillGotoRetry = true;
                                }

                                //指令执行错误
                                blnReturn = false;
                            }

                        }
                    } while (dataReply != null);

                    if (!hasRetry && isWillGotoRetry)
                    {
                        hasRetry = true;
                        goto DoRetry;
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iPhoneDevice.CreateAfcByFileSharing");

                blnReturn = false;
            }

            if (blnReturn)
                hHouseArrest = this.OpenAFCConnection(inSocket, false);
        }

        public void CloseAfcByFileSharing(string bundleId, HouseArrestType type)
        {
            if (!this.mDictHouseArrest.ContainsKey(type))
                return;

            AfcInfo info = this.mDictHouseArrest[type];
            if (info.bundleID == bundleId && info.hAfc != IntPtr.Zero)
            {
                this.CloseAFCConnection(ref info.hAfc, false);
                this.StopService(ref info.inSocket);

                if (this.mDictAFC.ContainsKey(info.bundleID))
                    this.mDictAFC.Remove(info.bundleID);
            }
        }

        public IntPtr GetAfcByFileSharing(string bundleId)
        {
            return GetAfcByFileSharing(bundleId, HouseArrestType.None, false);
        }

        public IntPtr GetAfcByFileSharing(string bundleId, HouseArrestType type)
        {
            return GetAfcByFileSharing(bundleId, type, false);
        }

        public IntPtr GetAfcByFileSharing(string bundleId, HouseArrestType type, bool blnRefresh)
        {
            AfcInfo info = null;

            //在进行iTunes备份，不响应AFC文件操作
            if (this.CheckIsSync())
                goto DoExit;

            lock (this.mLockerAFC)
            {
                //Console.WriteLine(bundleId + "\t" + type.ToString());

                if (this.mDictAFC.ContainsKey(bundleId))
                {
                    info = this.mDictAFC[bundleId];

                    if (info.Type != type)
                        goto DoExit;
                }

                if (this.mDictHouseArrest.ContainsKey(type))
                {
                    info = this.mDictHouseArrest[type];
                    if (info.bundleID != bundleId || info.hAfc == IntPtr.Zero || blnRefresh)
                    {
                        info.bundleID = bundleId;
                        this.CreateAfcByFileSharing(bundleId, ref info.inSocket, ref info.hAfc);
                    }
                }
                else
                {
                    info = new AfcInfo();
                    info.bundleID = bundleId;
                    info.Type = type;

                    this.mDictHouseArrest[type] = info;
                    this.CreateAfcByFileSharing(bundleId, ref info.inSocket, ref info.hAfc);
                }

                if (info != null && info.hAfc != IntPtr.Zero)
                    this.mDictAFC[bundleId] = info;
            }

        DoExit:
            return info.hAfc;
        }

        public bool CheckIsDeveloperApp(string bundleId)
        {
            bool isUIFileSharingEnabled = false;

            return CheckIsDeveloperApp(bundleId, ref isUIFileSharingEnabled);
        }

        public bool CheckIsDeveloperApp(string bundleId, ref bool isUIFileSharingEnabled)
        {
            bool blnResult = false;

            if (this.VersionNumber < 830)
            {
                blnResult = true;
                goto DoExit;
            }

            Dictionary<string, FileSharingPackageInfo> dict = InstalledApplications(ApplicationType.User, false);
            if (dict == null || !dict.ContainsKey(bundleId))
                goto DoExit;

            FileSharingPackageInfo info = this.mDictInstalledApplications[bundleId];
            if (info.CrackedInfo == CrakedInfo.Personal || info.CrackedInfo == CrakedInfo.Experience || info.CrackedInfo == CrakedInfo.Extreme)
            {
                //付费或者免费个人开发者
                blnResult = true;
            }
            else if (info.CrackedInfo == CrakedInfo.Enterprise)
            {
                //8.3-9.x的企业内部分发
                if (this.VersionNumber < 1000)
                {
                    blnResult = true;
                }
            }

            if (info != null)
            {
                isUIFileSharingEnabled = info.UIFileSharingEnabled;
                //Console.WriteLine(string.Format("{0}: \t{1}, \tUIFileSharingEnabled: {2}", bundleId, info.CrackedInfo.ToString(), info.UIFileSharingEnabled));
            }

        DoExit:
            return blnResult;
        }

        public string FormatFileSharingPath(string strPath)
        {
            return FormatFileSharingPath(strPath, string.Empty);
        }

        public string FormatFileSharingPath(string strPath, string bundleId)
        {
            return FormatFileSharingPath(strPath, bundleId, false);
        }

        public string FormatFileSharingPath(string strPath, string bundleId, bool blnIsDocument)
        {
            if (!string.IsNullOrEmpty(bundleId) && bundleId == "com.apple.crashreportcopymobile")
                return "/";

            string strDocPath = string.Empty;

            if (bundleId == null)
                bundleId = string.Empty;

            bool isDeveloperApp = this.CheckIsDeveloperApp(bundleId);

            if (strPath == "/" || !strPath.StartsWith("/"))
            {
                if (!strPath.StartsWith("/"))
                    bundleId = strPath;

                //付费或者免费个人开发者，或者为8.3-9.x的企业内部分发
                if (isDeveloperApp)
                {
                    strDocPath = "/";
                }
                else
                {
                    strDocPath = "/Documents";
                }
            }
            else
            {
                strDocPath = strPath;
            }

            if (blnIsDocument && !strDocPath.Contains("/Documents"))
            {
                if (strDocPath.EndsWith("/"))
                    strDocPath += "Documents";
                else
                    strDocPath += "/Documents";
            }

            return strDocPath;
        }

        #endregion

        #region --- com.apple.mobile.screenshotr ---

        private object mLockScreenshot = new object();

        public bool StartScreenshot()
        {
            if (this.mSocketScreenshot > 0)
                return true;

            bool blnResult = this.StartService("com.apple.mobile.screenshotr", ref this.mSocketScreenshot, false); //com.apple.mobile.mobile_image_mounter
            if (blnResult)
            {
                object dataReply = this.receive_message(this.mSocketScreenshot);
                SendMessageVersionExchange(this.mSocketScreenshot, 100);
            }

            return blnResult;
        }

        public bool CloseScreenshot()
        {
            if (this.mSocketScreenshot > 0)
            {
                this.SendMessageDisconnect(this.mSocketScreenshot, "___EmptyParameterString___");
                this.StopService(ref this.mSocketScreenshot);
            }

            this.mSocketScreenshot = 0;

            return true;
        }

        public Image GetScreenshotImage()
        {
            Image img = null;

            lock (mLockScreenshot)
            {
                if (this.StartScreenshot())
                {
                    Dictionary<object, object> dict = new Dictionary<object, object>();
                    dict.Add("MessageType", "ScreenShotRequest");

                    List<object> list = new List<object>();
                    list.Add("DLMessageProcessMessage");
                    list.Add(dict);

                    //Dim plist As String = System.Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory) & "\Shot1.plist"
                    //Dim result As Boolean = CoreFoundation.WritePlist(list, plist)
                    if (this.send_message_managed(this.mSocketScreenshot, list))
                    {
                        object objReply = this.receive_message(this.mSocketScreenshot);
                        try
                        {
                            if (objReply != null && objReply is Array)
                            {
                                Array aryReply = objReply as Array;
                                if (aryReply.Length < 2)
                                    goto DoExit;

                                object objData = aryReply.GetValue(1);
                                if (objData == null || !(objData is Dictionary<object, object>))
                                    goto DoExit;

                                Dictionary<object, object> dictData = objData as Dictionary<object, object>;
                                if (!dictData.ContainsKey("ScreenShotData"))
                                    goto DoExit;

                                object objScreenShotData = dictData["ScreenShotData"];
                                if (objScreenShotData == null || !(objScreenShotData is byte[]))
                                {
                                    Console.WriteLine("ScreenShotData is null, CloseScreenshot.");
                                    //this.CloseScreenshot();
                                    goto DoExit;
                                }

                                byte[] arrData = (byte[])objScreenShotData;
                                if (arrData == null || arrData.Length == 0)
                                    goto DoExit;

#if MAC || IOS
								using (NSAutoreleasePool pool = new NSAutoreleasePool())
								{
									NSData dataTemp = NSData.FromArray(arrData);
									if (dataTemp != null)
									{
										img = new Image(dataTemp);
										dataTemp.Dispose();
										dataTemp = null;
									}
								}
#else
                                MemoryStream ms = new MemoryStream(arrData);
                                img = Image.FromStream(ms);
#endif
                                arrData = null;

                                img = this.CorrectScreenshotImage(img);
                            }

                        }
                        catch (Exception ex)
                        {
                            Common.LogException(ex.ToString(), "iPhoneDevice.GetScreenshotImage");
                        }
                    }
                }
            }

        DoExit:
            return img;
        }

#if MAC || IOS
		private Image CorrectScreenshotImage(Image img)
		{
			if (img != null && this.VersionNumber >= 900)
			{
				iOrientation org = (iOrientation)this.GetInterfaceOrientation();
				switch (org)
				{
					case iOrientation.Horizontal_90:
						img = img.RotateFlip(RotateFlipType.Rotate90FlipXY);
						break;
					case iOrientation.Vertical_180:
						img = img.RotateFlip(RotateFlipType.Rotate180FlipNone);
						break;
					case iOrientation.Horizontal_270:
						img = img.RotateFlip(RotateFlipType.Rotate270FlipXY);
						break;
				}
			}
			return img;
		}
#else
        private Image CorrectScreenshotImage(Image img)
        {
            if (img != null && this.VersionNumber >= 900)
            {
                iOrientation org = (iOrientation)this.GetInterfaceOrientation();
                switch (org)
                {
                    case iOrientation.Horizontal_90:
                        img.RotateFlip(RotateFlipType.Rotate90FlipXY);
                        break;
                    case iOrientation.Vertical_180:
                        img.RotateFlip(RotateFlipType.Rotate180FlipNone);
                        break;
                    case iOrientation.Horizontal_270:
                        img.RotateFlip(RotateFlipType.Rotate270FlipXY);
                        break;
                }
            }
            return img;
        }
#endif

        internal bool StartMobileImageMounter()
        {
            if (this.mSocketImageMounter > 0)
                return true;

            return this.StartService("com.apple.mobile.mobile_image_mounter", ref this.mSocketImageMounter, false);
        }

        internal bool CloseImageMounter()
        {
            if (this.mSocketImageMounter > 0)
            {
                Dictionary<object, object> dictHangup = new Dictionary<object, object>();
                dictHangup.Add("Command", "Hangup");

                this.send_message_managed(this.mSocketImageMounter, dictHangup);
                this.StopService(ref this.mSocketImageMounter);
            }

            this.mSocketImageMounter = 0;

            return true;
        }

        //iOS7.1及以上的上传方式
        private bool UploadMobileImageMounter(string strImagePath, byte[] arrSign)
        {
            if (!File.Exists(strImagePath))
                return false;

            //1、通过socket发送文件大小
            bool blnResult = false;
            object dataReply = null;

            FileInfo fileInfo = new FileInfo(strImagePath);

            Dictionary<object, object> dict = new Dictionary<object, object>();
            dict.Add("Command", "ReceiveBytes");
            dict.Add("ImageSize", fileInfo.Length);
            dict.Add("ImageType", "Developer");

            if (this.mVersionNumber >= 800)
                dict.Add("ImageSignature", arrSign);

            if (this.send_message_managed(this.mSocketImageMounter, dict))
            {
                dataReply = this.receive_message(this.mSocketImageMounter);
                try
                {
                    Dictionary<object, object> dictReplay = (Dictionary<object, object>)(dataReply);
                    if (dictReplay.ContainsKey("Status") && dictReplay["Status"].ToString() == "ReceiveBytesAck")
                        blnResult = true;

                    Common.LogException(CoreFoundation.CreatePlistString(dictReplay));
                    //For Each pair As KeyValuePair(Of Object, Object) In dictReplay
                    //    Console.WriteLine(String.Format("{0}:{1}", pair.Key, pair.Value))
                    //Next
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "iPhoneDevice.UploadMobileImageMounterA");
                }
            }

            if (!blnResult)
                goto DO_EXIT;

            //2、通过socket发送文件内容
            blnResult = false;
            dataReply = null;

            long lngSend = 0;
            byte[] arrBuffer = new byte[32768];

            ServiceInfo info = this.GetService(mSocketImageMounter);

            using (FileStream fs = new FileStream(strImagePath, FileMode.Open, FileAccess.Read))
            {
                //Console.WriteLine("Upload: " & strLocalFile)

                while (lngSend < fs.Length)
                {
                    int len = (int)(fs.Length - lngSend < arrBuffer.Length ? fs.Length - lngSend : arrBuffer.Length);
                    int intRead = fs.Read(arrBuffer, 0, len);

                    if (intRead <= 0)
                        break;

                    //if (!this.mb2_Send_Raw(mSocketImageMounter, arrBuffer, intRead))
                    //    goto DO_EXIT;

                    IntPtr pData = Marshal.UnsafeAddrOfPinnedArrayElement(arrBuffer, 0);
                    int intSend = 0;

                    if (info == null || !info.IsSSL)
                        intSend = MobileDevice.send(mSocketImageMounter, pData, intRead, 0);
                    else
                        intSend = MobileDevice.AMDServiceConnectionSend(info.hService, pData, intRead);

                    if (intRead != intSend)
                        goto DO_EXIT;

                    lngSend += intRead;
                }
            }


            dataReply = this.receive_message(this.mSocketImageMounter);
            if (dataReply != null)
            {
                try
                {
                    Dictionary<object, object> dictReplay = (Dictionary<object, object>)(dataReply);
                    if (dictReplay.ContainsKey("Status") && dictReplay["Status"].ToString() == "Complete")
                        blnResult = true;
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "iPhoneDevice.UploadMobileImageMounterB");
                }
            }


        DO_EXIT:
            return blnResult;
        }

        public bool SendMobileImageMounter(string strImagePath, string strImageSignature)
        {
            if (!File.Exists(strImagePath) || !File.Exists(strImageSignature))
                return false;

            if (!this.StartMobileImageMounter())
                return false;

            string strImagePathOnPhone = "/PublicStaging/" + Path.GetFileName(strImagePath);
            string strRootImagePathOnPhone = "/var/mobile/Media" + strImagePathOnPhone;

            byte[] arrImageSignature = File.ReadAllBytes(strImageSignature);

            if (this.VersionNumber >= 710)
            {
                if (!this.UploadMobileImageMounter(strImagePath, arrImageSignature))
                {
                    this.CloseImageMounter();
                    return false;
                }
            }
            else
            {
                this.CheckFolderByAFC("/PublicStaging");
                if (!this.CopyToPhoneByAFC(strImagePath, strImagePathOnPhone))
                    return false;
            }

            bool blnResult = false;

            Dictionary<object, object> dict = new Dictionary<object, object>();
            dict.Add("Command", "MountImage");
            dict.Add("ImagePath", strRootImagePathOnPhone);
            dict.Add("ImageSignature", arrImageSignature);
            dict.Add("ImageType", "Developer");

            if (this.send_message_managed(this.mSocketImageMounter, dict))
            {
                object dataReply = this.receive_message(this.mSocketImageMounter);
                try
                {
                    Dictionary<object, object> dictReplay = (Dictionary<object, object>)(dataReply);
                    if (dictReplay.ContainsKey("Status") && dictReplay["Status"].ToString() == "Complete")
                        blnResult = true;
                    //For Each pair As KeyValuePair(Of Object, Object) In dictReplay
                    //    Console.WriteLine(String.Format("{0}:{1}", pair.Key, pair.Value))
                    //Next
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "iPhoneDevice.SendMobileImageMounter");
                }
            }

            this.CloseImageMounter();

            return blnResult;
        }

        #endregion

        #region --- com.apple.mobilesync ---

        internal void CheckTetheredSync(string strName)
        {
            try
            {
                object objValue = this.GetDeviceValue("com.apple.mobile.tethered_sync", strName);
                bool blnChangeValue = false;

                if (objValue is Dictionary<object, object>)
                {
                    Dictionary<object, object> dict = (Dictionary<object, object>)(objValue);
                    if (dict.ContainsKey("DisableTethered"))
                    {
                        if (dict["DisableTethered"] is bool)
                        {
                            if (System.Convert.ToBoolean(dict["DisableTethered"]))
                                blnChangeValue = true;
                        }
                        else if (dict["DisableTethered"] is int)
                        {
                            if (System.Convert.ToInt32(dict["DisableTethered"]) == 1)
                                blnChangeValue = true;
                        }
                    }
                }
                else if (objValue is long || objValue is int)
                {
                    blnChangeValue = true;
                }

                if (blnChangeValue)
                {
                    Dictionary<object, object> dict = new Dictionary<object, object>();
                    dict.Add("DisableTethered", false);

                    this.SetDeviceValue("com.apple.mobile.tethered_sync", strName, dict);
                    Utility.WaitSeconds(0.5);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iPhoneDevice.CheckTetheredSync");
            }
        }

        public bool StartMobileSync(int verminor)
        {
            if (this.mSocketMobileSync > 0)
                return true;

            bool blnResult = this.StartService("com.apple.mobilesync", ref this.mSocketMobileSync, false);
            if (blnResult)
            {
                object dataReply = this.receive_message(this.mSocketMobileSync);
                SendMessageVersionExchange(this.mSocketMobileSync, verminor);
            }

            return blnResult;
        }

        public bool CloseMobileSync()
        {
            if (this.mSocketMobileSync > 0)
            {
                this.SendMessageDisconnect(this.mSocketMobileSync, "All done, thanks for the memories");
                this.StopService(ref this.mSocketMobileSync);
            }

            this.mSocketMobileSync = 0;

            return true;
        }

        internal object SendMessageVersionExchange(int inSocket, int vMajor)
        {
            object dataReply = null;
            List<object> message = new List<object>();

            if (vMajor <= 0)
                vMajor = 100;

            message.Add("DLMessageVersionExchange");
            message.Add("DLVersionsOk");
            message.Add(vMajor);

            if (this.send_message_managed(inSocket, message))
                dataReply = this.receive_message(inSocket);

            return dataReply;
        }

#if!TOOLS
        internal object SendMessageSyncDataClassWithDevice(int inSocket, string domainName)
        {
            object dataReply = null;
            List<object> message = new List<object>();
            DateTime dtTime = DateTime.Now.AddMinutes(5);
            string strTimeZone = " +" + iPhoneHelper.TimeZone.Hours.ToString().PadLeft(2, '0') + iPhoneHelper.TimeZone.Minutes.ToString().PadLeft(2, '0');

            //If extension.Length = 0 Then
            //    extension = dtTime.ToString("MMM d yyyy HH:mm:ss", System.Globalization.DateTimeFormatInfo.InvariantInfo) & strTimeZone
            //End If

            message.Add("SDMessageSyncDataClassWithDevice");
            message.Add(domainName);
            message.Add("---");
            message.Add(dtTime.ToString("yyyy-MM-dd HH:mm:ss") + strTimeZone);
            message.Add(106); //'' Version Number
            message.Add("___EmptyParameterString___");

            if (this.send_message_managed(inSocket, message))
                dataReply = this.receive_message(inSocket);

            return dataReply;
        }
#endif

        internal object SendMessageGetChangesFromDevice(int inSocket, string domainName)
        {
            object dataReply = null;
            List<object> message = new List<object>();

            message.Add("SDMessageGetChangesFromDevice");
            message.Add(domainName);

            if (this.send_message_managed(inSocket, message))
                dataReply = this.receive_message(inSocket);

            return dataReply;
        }

        internal object SendMessageFinishSessionOnDevice(int inSocket, string domainName)
        {
            object dataReply = null;
            List<object> message = new List<object>();

            message.Add("SDMessageFinishSessionOnDevice");
            message.Add(domainName);

            if (this.send_message_managed(inSocket, message))
                dataReply = this.receive_message(inSocket);

            return dataReply;
        }

        internal object SendMessageDisconnect(int inSocket, string strMsg)
        {
            object dataReply = null;
            List<object> message = new List<object>();

            message.Add("DLMessageDisconnect");
            message.Add(strMsg);

            if (this.send_message_managed(inSocket, message))
            {
                //dataReply = Me.receive_message(inSocket)
            }

            return dataReply;
        }

        internal object SendMessageClearAllRecordsOnDevice(int inSocket, string domainName)
        {
            object dataReply = null;
            List<object> message = new List<object>();

            message.Add("SDMessageClearAllRecordsOnDevice");
            message.Add(domainName);
            message.Add("___EmptyParameterString___");

            if (this.send_message_managed(inSocket, message))
                dataReply = this.receive_message(inSocket);

            return dataReply;
        }

        internal object SendMessageGetAllRecordsFromDevice(int inSocket, string domainName)
        {
            object objReturn = null;
            List<object> message = new List<object>();

            message.Add("SDMessageGetAllRecordsFromDevice");
            message.Add(domainName);
            if (this.send_message_managed(inSocket, message))
            {
                Array aryReply = this.receive_message(inSocket) as Array;
                if (aryReply != null)
                    objReturn = aryReply.GetValue(2);
            }

            return objReturn;
        }

        internal List<object> SendMessageAcknowledgeChangesFromDevice(int inSocket, string domainName)
        {
            List<object> listReply = new List<object>();
            List<object> message = new List<object>();

            message.Add("SDMessageAcknowledgeChangesFromDevice");
            message.Add(domainName);

            while (true)
            {
                if (this.send_message_managed(inSocket, message))
                {
                    Array aryReply = this.receive_message(inSocket) as Array;

                    //Console.WriteLine(aryReply.GetValue(0).ToString());

                    //Dim strPath123 As String = System.Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory) & String.Format("\gxf{0}.plist", "_TB1234")
                    //Dim bln As Boolean = CoreFoundation.WritePlist(dataReply, strPath123)

                    if (aryReply == null || aryReply.Length == 2 && aryReply.GetValue(0).ToString() == "SDMessageDeviceReadyToReceiveChanges" && aryReply.GetValue(1).ToString() == domainName)
                    {
                        break;
                    }
                    else if (aryReply.GetValue(2) != null)
                    {
                        listReply.Add(aryReply.GetValue(2));
                    }
                }
                else
                {
                    break;
                }
            }

            return listReply;
        }

        internal object SendMessageProcessChanges(int inSocket, string domainName, List<object> listEntityNamesKey)
        {
            object dataReply = null;

            Dictionary<object, object> dict = new Dictionary<object, object>();
            dict.Add("SyncDeviceLinkEntityNamesKey", listEntityNamesKey);
            dict.Add("SyncDeviceLinkAllRecordsOfPulledEntityTypeSentKey", true);

            List<object> message = new List<object>();
            message.Add("SDMessageProcessChanges");
            message.Add(domainName);
            message.Add(new Dictionary<object, object>());
            message.Add(true);
            message.Add(dict);

            //Dim strPath123 As String = System.Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory) & String.Format("\_S_{0}.plist", Date.Now.ToString("HH_mm_ss"))
            //Dim bln As Boolean = CoreFoundation.WritePlist(message, strPath123)

            if (this.send_message_managed(inSocket, message))
                dataReply = this.receive_message(inSocket);

            return dataReply;
        }

        internal bool SendMessageProcessChanges(int inSocket, string domainName)
        {
            List<object> message = new List<object>();

            message.Add("SDMessageProcessChanges");
            message.Add(domainName);

            if (this.send_message_managed(inSocket, message))
            {
                //object dataReply = Me.receive_message(inSocket)
            }

            return true;
        }

        internal object SendMessagePing(int inSocket, string domainName)
        {
            object dataReply = null;
            List<object> message = new List<object>();

            message.Add("DLMessagePing");
            message.Add(domainName);

            if (this.send_message_managed(inSocket, message))
            {
                //dataReply = Me.receive_message(inSocket)
            }

            return dataReply;
        }

        #endregion

        #region --- com.apple.mobile.MCInstall ---

        private int mSocketMCInstall = 0;
        public bool StartMCInstall()
        {
            if (this.mSocketMCInstall > 0)
            {
                return true;
            }

            return this.StartService("com.apple.mobile.MCInstall", ref this.mSocketMCInstall, false); //com.apple.mobile.MCInstall
        }

        public bool CloseMCInstall()
        {
            if (this.mSocketMCInstall > 0)
                this.StopService(ref this.mSocketMCInstall);

            this.mSocketMCInstall = 0;

            return true;
        }

        public object GetConfigurationProfileList()
        {
            object objResult = null;

            try
            {
                if (!this.StartMCInstall())
                    goto DO_EXIT;

                Dictionary<object, object> dict = new Dictionary<object, object>();
                dict.Add("RequestType", "GetProfileList");

                if (this.send_message_managed(this.mSocketMCInstall, dict))
                {
                    Dictionary<object, object> dictResult = this.receive_message(this.mSocketMCInstall) as Dictionary<object, object>;
                    if (dictResult == null || !dictResult.ContainsKey("Status") || dictResult["Status"].ToString() != "Acknowledged")
                    {
                        Common.LogException(CoreFoundation.CreatePlistString(dictResult), "GetConfigurationProfileList");
                        this.CloseMCInstall();

                        goto DO_EXIT;
                    }

                    objResult = dictResult;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetConfigurationProfileList");
            }

        DO_EXIT:
            return objResult;
        }

        public bool InstallConfigurationProfile(string strConfigurationProfile)
        {
            if (!File.Exists(strConfigurationProfile))
                return false;

            byte[] arrData = File.ReadAllBytes(strConfigurationProfile);
            return this.InstallConfigurationProfile(arrData);
        }

        public bool InstallConfigurationProfile(byte[] arrData)
        {
            bool blnResult = false;

            try
            {
                if (!this.StartMCInstall() || arrData == null)
                    goto DO_EXIT;

                Dictionary<object, object> dict = new Dictionary<object, object>();
                dict.Add("RequestType", "InstallProfile");
                dict.Add("Payload", arrData);

                if (this.send_message_managed(this.mSocketMCInstall, dict))
                {
                    Dictionary<object, object> dictResult = this.receive_message(this.mSocketMCInstall) as Dictionary<object, object>;
                    if (dictResult == null || !dictResult.ContainsKey("Status") || dictResult["Status"].ToString() != "Acknowledged")
                    {
                        Common.LogException(CoreFoundation.CreatePlistString(dictResult), "InstallConfigurationProfile");
                        this.CloseMCInstall();

                        goto DO_EXIT;
                    }

                    blnResult = true;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "InstallConfigurationProfile");
            }

        DO_EXIT:
            return blnResult;
        }

        public bool RemoveConfigurationProfile(string strConfigurationProfile)
        {
            if (!File.Exists(strConfigurationProfile))
                return false;

            byte[] arrData = File.ReadAllBytes(strConfigurationProfile);
            return this.RemoveConfigurationProfile(arrData);
        }

        public bool RemoveConfigurationProfile(byte[] arrConfigurationProfile)
        {
            bool blnResult = false;

            try
            {
                if (!this.StartMCInstall() || arrConfigurationProfile == null)
                    goto DO_EXIT;

                Dictionary<object, object> dict = new Dictionary<object, object>();
                dict.Add("RequestType", "RemoveProfile");
                dict.Add("ProfileIdentifier", arrConfigurationProfile);

                if (this.send_message_managed(this.mSocketMCInstall, dict))
                {
                    Dictionary<object, object> dictResult = this.receive_message(this.mSocketMCInstall) as Dictionary<object, object>;
                    if (dictResult == null || !dictResult.ContainsKey("Status") || dictResult["Status"].ToString() != "Acknowledged")
                    {
                        Common.LogException(CoreFoundation.CreatePlistString(dictResult), "RemoveConfigurationProfile");
                        this.CloseMCInstall();

                        goto DO_EXIT;
                    }

                    blnResult = true;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "RemoveConfigurationProfile");
            }

        DO_EXIT:
            return blnResult;
        }

        #endregion

        #region --- com.apple.misagent ---

        private int mSocketMisAgent = 0;
        public bool StartMisAgent()
        {
            if (this.mSocketMisAgent > 0)
            {
                return true;
            }

            return this.StartService("com.apple.misagent", ref this.mSocketMisAgent, false); //com.apple.misagent
        }

        public bool CloseMisAgent()
        {
            if (this.mSocketMisAgent > 0)
                this.StopService(ref this.mSocketMisAgent);

            this.mSocketMisAgent = 0;

            return true;
        }

        private List<MISProvisionProfile> CopyProvisioningProfiles()
        {
            List<MISProvisionProfile> list = new List<MISProvisionProfile>();

            bool blnRetry = false;
            bool blnHeartbeat = false;

            try
            {
                bool blnConnectBySelf = false;
                bool blnStartSessionBySelf = false;
                kAMDError inError = kAMDError.kAMDSuccess;

            DO_CONNECT:
                if (!this.mIsConnectActive)
                {
                    inError = this.Connect();
                    if (inError == kAMDError.kAMDSuccess)
                    {
                        blnConnectBySelf = true;
                    }
                    else
                    {
                        Console.WriteLine("CopyProvisioningProfiles failed B");
                        goto DO_EXIT;
                    }
                }

                if (!this.mIsSessionActive)
                {
                    inError = this.StartSession(blnHeartbeat);
                    if (inError == kAMDError.kAMDSuccess)
                    {
                        blnStartSessionBySelf = true;
                    }
                    else if (!blnRetry)
                    {
                        this.Disconnect();
                        Console.WriteLine("CopyProvisioningProfiles failed RETRY" + inError);
                        blnRetry = true;
                        goto DO_CONNECT;
                    }
                    else
                    {
                        Console.WriteLine("CopyProvisioningProfiles failed A" + inError);
                        goto DO_DISCONNECT;
                    }
                }

                IntPtr arrayPtr = MobileDevice.AMDeviceCopyProvisioningProfiles(this.mDeviceRef);
                if (arrayPtr != IntPtr.Zero)
                {
                    int count = CoreFoundation.CFArrayGetCount(arrayPtr);
                    for (int index = 0; index <= count - 1; index++)
                    {
                        IntPtr hProfileDef = CoreFoundation.CFArrayGetValueAtIndex(arrayPtr, index);
                        if (hProfileDef != IntPtr.Zero)
                            list.Add(new MISProvisionProfile(hProfileDef));
                    }
                    CoreFoundation.CFRelease(arrayPtr);
                }


                if (blnStartSessionBySelf)
                    this.StopSession();

                DO_DISCONNECT:
                if (blnConnectBySelf)
                    this.Disconnect();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iPhoneDevice.CopyProvisioningProfiles");
            }

        DO_EXIT:
            return list;
        }

        public List<MISProvisionProfile> GetProvisioningProfileList()
        {
            List<MISProvisionProfile> list = new List<MISProvisionProfile>();

            try
            {
                if (!this.StartMisAgent())
                    goto DO_EXIT;

                Dictionary<object, object> dict = new Dictionary<object, object>();
                dict.Add("MessageType", "Copy");
                dict.Add("ProfileType", "Provisioning");

                if (this.send_message_managed(this.mSocketMisAgent, dict))
                {
                    Dictionary<object, object> dictResult = this.receive_message(this.mSocketMisAgent) as Dictionary<object, object>;
                    if (dictResult == null || !dictResult.ContainsKey("Status") || dictResult["Status"].ToString() != "0")
                    {
                        Common.LogException(CoreFoundation.CreatePlistString(dictResult), "GetProvisioningProfileList");
                        this.CloseMisAgent();

                        goto DO_EXIT;
                    }

                    foreach (byte[] arrData in dictResult["Payload"] as IList<object>)
                    {
                        MISProvisionProfile item = MISProvisionProfile.CreateProvisioningProfile(arrData);
                        if (item != null)
                            list.Add(item);
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetProvisioningProfileList");
            }

        DO_EXIT:
            return list;
        }

        public bool InstallProvisioningProfile(string strProvisioningProfile)
        {
            if (!File.Exists(strProvisioningProfile))
                return false;

            byte[] arrData = File.ReadAllBytes(strProvisioningProfile);
            return this.InstallProvisioningProfile(arrData);
        }

        public bool InstallProvisioningProfile(byte[] arrProvisioningProfile)
        {
            bool blnResult = false;

            try
            {
                if (!this.StartMisAgent() || arrProvisioningProfile == null)
                    goto DO_EXIT;

                Dictionary<object, object> dict = new Dictionary<object, object>();
                dict.Add("MessageType", "Install");
                dict.Add("Profile", arrProvisioningProfile);
                dict.Add("ProfileType", "Provisioning");

                if (this.send_message_managed(this.mSocketMisAgent, dict))
                {
                    Dictionary<object, object> dictResult = this.receive_message(this.mSocketMisAgent) as Dictionary<object, object>;
                    if (dictResult == null || !dictResult.ContainsKey("Status") || dictResult["Status"].ToString() != "0")
                    {
                        Common.LogException(CoreFoundation.CreatePlistString(dictResult), "InstallProvisioningProfile");
                        this.CloseMisAgent();

                        goto DO_EXIT;
                    }

                    blnResult = true;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "InstallProvisioningProfile");
            }

        DO_EXIT:
            return blnResult;
        }

        public bool RemoveProvisioningProfile(string profileUUID)
        {
            bool blnResult = false;

            try
            {
                if (!this.StartMisAgent() || string.IsNullOrEmpty(profileUUID))
                    goto DO_EXIT;

                Dictionary<object, object> dict = new Dictionary<object, object>();
                dict.Add("MessageType", "Remove");
                dict.Add("ProfileID", profileUUID);
                dict.Add("ProfileType", "Provisioning");

                if (this.send_message_managed(this.mSocketMisAgent, dict))
                {
                    Dictionary<object, object> dictResult = this.receive_message(this.mSocketMisAgent) as Dictionary<object, object>;
                    if (dictResult == null || !dictResult.ContainsKey("Status") || dictResult["Status"].ToString() != "0")
                    {
                        Common.LogException(CoreFoundation.CreatePlistString(dictResult), "RemoveProvisioningProfile");
                        this.CloseMisAgent();

                        goto DO_EXIT;
                    }

                    blnResult = true;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "RemoveProvisioningProfile");
            }

        DO_EXIT:
            return blnResult;
        }

        #endregion

        #region --- com.apple.crashreportcopymobile ---

        public IntPtr HandleCrashReportCopyMobile
        {
            get
            {
                if (this.mHandleCrashReportCopyMobile == IntPtr.Zero)
                {
                    this.StartCrashReportCopyMobile();
                }
                return this.mHandleCrashReportCopyMobile;
            }
        }

        private int mSocketCrashReportCopyMobile = 0;
        private IntPtr mHandleCrashReportCopyMobile = IntPtr.Zero;

        public bool StartCrashReportCopyMobile()
        {
            if (this.mSocketCrashReportCopyMobile > 0)
                return true;

            if (this.StartService("com.apple.crashreportcopymobile", ref this.mSocketCrashReportCopyMobile, false)) //com.apple.mobile.MCInstall
                this.mHandleCrashReportCopyMobile = this.OpenAFCConnection(this.mSocketCrashReportCopyMobile, true);

            return true;
        }

        public bool CloseCrashReportCopyMoible()
        {
            if (this.mSocketCrashReportCopyMobile > 0)
            {
                this.CloseAFCConnection(ref this.mHandleCrashReportCopyMobile, true);
                this.StopService(ref this.mSocketCrashReportCopyMobile);
            }

            this.mSocketCrashReportCopyMobile = 0;

            return true;
        }

        #endregion

        #region --- com.apple.syslog_relay ---

        public int mSocketSyslogRelay = 0;
        public bool StartSyslogRelay()
        {
            if (this.mSocketSyslogRelay > 0)
            {
                return true;
            }

            return this.StartService("com.apple.syslog_relay", ref this.mSocketSyslogRelay, false);
        }

        public bool CloseSyslogRelay()
        {
            if (this.mSocketSyslogRelay > 0)
                this.StopService(ref this.mSocketSyslogRelay);

            this.mSocketSyslogRelay = 0;

            return true;
        }

        public string ReadSyslogRelay()
        {
            string strLog = string.Empty;

            try
            {

                if (!this.StartSyslogRelay())
                    goto DoExit;

                byte[] arrData = new byte[5120];
                IntPtr recvPtr = Marshal.AllocCoTaskMem(arrData.Length);

                ServiceInfo info = this.GetService(this.mSocketSyslogRelay);
                int count = 0;

                if (this.mConnectMode == ConnectMode.USB && (info == null || !info.IsSSL))
                    count = MobileDevice.recv(this.mSocketSyslogRelay, recvPtr, arrData.Length, 0);
                else
                    count = MobileDevice.AMDServiceConnectionReceive(info.hService, recvPtr, arrData.Length);

                if (count > 0)
                {
                    Marshal.Copy(recvPtr, arrData, 0, count);
                    strLog = Encoding.UTF8.GetString(arrData);
                }

                Marshal.FreeCoTaskMem(recvPtr);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ReadSyslogRelay");
            }

        DoExit:
            return strLog;
        }

        #endregion

        #region --- com.apple.mobile.diagnostics_relay ---

        private int mSocketDiagnosticsRelay = 0;
        private bool StartDiagnosticsRelay()
        {
            if (this.mSocketDiagnosticsRelay > 0)
                return true;

            //com.apple.iosdiagnostics.relay
            bool blnResult = this.StartService("com.apple.mobile.diagnostics_relay", ref this.mSocketDiagnosticsRelay, false);
            //Dim dataReply As Object = Me.receive_message(Me.mSocketExtra)

            return blnResult;
        }

        private bool CloseDiagnosticsRelay()
        {
            if (this.mSocketDiagnosticsRelay > 0)
                this.StopService(ref this.mSocketDiagnosticsRelay);

            this.mSocketDiagnosticsRelay = 0;

            return true;
        }

        public bool Restart()
        {
            bool blnResult = false;

            try
            {
                if (this.StartDiagnosticsRelay())
                {
                    Dictionary<object, object> dict = new Dictionary<object, object>();
                    dict.Add("Request", "Restart");

                    if (this.send_message_managed(this.mSocketDiagnosticsRelay, dict))
                    {
                        Dictionary<object, object> dictReply = (Dictionary<object, object>)(this.receive_message(this.mSocketDiagnosticsRelay));
                        //Console.WriteLine(CoreFoundation.CreatePlistString(dictReply))
                        if (dictReply != null && dictReply.ContainsKey("Status"))
                            blnResult = (dictReply["Status"].ToString() == "Success");
                    }

                    this.CloseDiagnosticsRelay();
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iPhoneDevice.Restart");
            }

            return blnResult;
        }

        public bool Shutdown()
        {
            bool blnResult = false;

            try
            {
                if (this.StartDiagnosticsRelay())
                {
                    Dictionary<object, object> dict = new Dictionary<object, object>();
                    dict.Add("Request", "Shutdown");
                    //dict.Add("Request", "Goodbye")
                    //dict.Add("Request", "Sleep")

                    if (this.send_message_managed(this.mSocketDiagnosticsRelay, dict))
                    {
                        Dictionary<object, object> dictReply = (Dictionary<object, object>)(this.receive_message(this.mSocketDiagnosticsRelay));
                        //Console.WriteLine(CoreFoundation.CreatePlistString(dictReply))
                        if (dictReply != null && dictReply.ContainsKey("Status"))
                            blnResult = (dictReply["Status"].ToString() == "Success");
                    }

                    this.CloseDiagnosticsRelay();
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iPhoneDevice.Shutdown");
            }

            return blnResult;
        }

        public object GetIORegistryInfo()
        {
            object objInfo = null;

            try
            {
                if (this.StartDiagnosticsRelay())
                {
                    Dictionary<object, object> dict = new Dictionary<object, object>();
                    dict.Add("Request", "IORegistry");
                    dict.Add("EntryClass", "IOPMPowerSource");
                    //dict.Add("CurrentPlane", "CurrentPlane")
                    //dict.Add("EntryName", "EntryName")

                    if (this.send_message_managed(this.mSocketDiagnosticsRelay, dict))
                    {
                        Dictionary<object, object> dictReply = (Dictionary<object, object>)(this.receive_message(this.mSocketDiagnosticsRelay));
                        //Console.Write(CoreFoundation.CreatePlistString(dictReply));
                        if (dictReply != null && dictReply.ContainsKey("Status") && dictReply["Status"].ToString() == "Success" && dictReply.ContainsKey("Diagnostics"))
                        {
                            Dictionary<object, object> dictDiagnostics = dictReply["Diagnostics"] as Dictionary<object, object>;
                            if (dictDiagnostics.ContainsKey("IORegistry"))
                            {
                                objInfo = dictDiagnostics["IORegistry"];
                                //Console.Write(CoreFoundation.CreatePlistString(objInfo));

                                //Dictionary<object, object> dictInfo = objInfo as Dictionary<object, object>;
                                //Dictionary<object, object> dictBattery = dictInfo["battery-data"] as Dictionary<object, object>;
                                //byte[] arr0003 = (byte[])dictBattery["0003-default"];
                                //Dictionary<object, object> dict0003 = CoreFoundation.ManagedPropertyListFromXMLData(arr0003) as Dictionary<object, object>;
                                //{ 
                                //}

                            }
                        }
                    }

                    this.CloseDiagnosticsRelay();
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iPhoneDevice.GetBatteryInfo");
            }

            return objInfo;
        }


        #endregion

        #region --- com.apple.mobile.file_relay ---

        private int mSocketFileRelay = 0;

        private bool StartFileRelay()
        {
            if (this.mSocketFileRelay > 0)
                return true;

            return this.StartService("com.apple.mobile.file_relay", ref this.mSocketFileRelay, false);
        }

        private bool CloseFileRelay()
        {
            if (this.mSocketFileRelay > 0)
                this.StopService(ref this.mSocketFileRelay);

            this.mSocketFileRelay = 0;

            return true;
        }

#if !TOOLS
        public bool GetFileRelay(FileRelaySourceType type, string downZipFile)
        {
            bool blnResult = false;

            try
            {
                if (!this.StartFileRelay())
                    goto DO_EXIT;

                List<object> list = new List<object>();
                list.Add(type.ToString());

                Dictionary<object, object> dictMsg = new Dictionary<object, object>();
                dictMsg.Add("Sources", list);

                if (this.send_message_managed(this.mSocketFileRelay, dictMsg))
                {
                    Dictionary<object, object> dataReply = (Dictionary<object, object>)(this.receive_message(this.mSocketFileRelay));
                    if (dataReply == null || dataReply.ContainsKey("Error"))
                        goto DO_EXIT;

                    if (!dataReply.ContainsKey("Status") || dataReply["Status"].ToString() != "Acknowledged")
                        goto DO_EXIT;

                    using (FileStream fs = new FileStream(downZipFile, FileMode.Create))
                    {
                        while (true)
                        {
                            byte[] arrData = this.mb2_Receive_Raw(this.mSocketFileRelay, 4096);
                            if (arrData != null && arrData.Length > 0)
                            {
                                fs.Write(arrData, 0, arrData.Length);
                            }
                            else
                            {
                                break;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iPhoneDevice.GetFileRelay");
            }

        DO_EXIT:
            this.CloseFileRelay();

            return blnResult;
        }
#endif

        #endregion

        #region --- 获取同步推的identify ---

        public string GetTuiAppSKU(bool blnPad)
        {
            string strId = Common.GetTuiAppSKU(blnPad);
            Dictionary<string, FileSharingPackageInfo> dict = this.InstalledApplications(ApplicationType.Any, false);
            if (dict != null && dict.Count > 0)
            {
                foreach (string Item in dict.Keys)
                {
                    if (Item.StartsWith(strId))
                    {
                        strId = Item;
                        break;
                    }
                }
            }
            return strId;
        }

        public string GetTuiAppSKU(string strProductType)
        {
            bool blnPad = false;
            if (strProductType.ToLower().StartsWith("ipad"))
                blnPad = true;

            string strId = GetTuiAppSKU(blnPad);
            return strId;
        }

        #endregion

        #region --- iTunes自启动 ---

        public bool SetiTunesPrefs(bool blnOpen)
        {
            bool blnReturn = false;

            try
            {
                string iTunesPrefsOnPC = Folder.GetTempFilePath();
                string iTunesPrefsPlistOnPC = Folder.GetTempFilePath();

                string iTunesPrefsOnPhone = "/iTunes_Control/iTunes/iTunesPrefs";
                string iTunesPrefsPlistOnPhone = "/iTunes_Control/iTunes/iTunesPrefs.plist";

                if (this.DownFromPhoneByAFC(iTunesPrefsOnPhone, iTunesPrefsOnPC) && this.DownFromPhoneByAFC(iTunesPrefsPlistOnPhone, iTunesPrefsPlistOnPC))
                {
                    Dictionary<object, object> dict = CoreFoundation.ManagedPropertyListFromXMLData(iTunesPrefsPlistOnPC) as Dictionary<object, object>;
                    byte[] arrData = File.ReadAllBytes(iTunesPrefsOnPC);

                    if (arrData == null || arrData.Length < 175)
                        goto DO_EXIT;

                    arrData[9] = (byte)(blnOpen ? 1 : 0);
                    arrData[174] = (byte)(blnOpen ? 0 : 1);

                    if (dict.ContainsKey("iPodPrefs"))
                        dict["iPodPrefs"] = arrData;

                    CoreFoundation.WritePlist(dict, iTunesPrefsPlistOnPC);
                    File.WriteAllBytes(iTunesPrefsOnPC, arrData);

                    if (this.CopyToPhoneByAFC(iTunesPrefsOnPC, "/iTunes_Control/iTunes/iTunesPrefs") && this.CopyToPhoneByAFC(iTunesPrefsPlistOnPC, "/iTunes_Control/iTunes/iTunesPrefs.plist"))
                        blnReturn = true;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "SetiTunesPrefs");
            }

        DO_EXIT:
            return blnReturn;
        }

        public bool GetiTunesPrefs()
        {
            bool blnReturn = false;

            try
            {
                string iTunesPrefsOnPC = Folder.GetTempFilePath();
                string iTunesPrefsOnPhone = "/iTunes_Control/iTunes/iTunesPrefs";

                if (this.DownFromPhoneByAFC(iTunesPrefsOnPhone, iTunesPrefsOnPC))
                {
                    byte[] arrData = File.ReadAllBytes(iTunesPrefsOnPC);

                    if (arrData.Length > 9 && arrData[9] == 1)
                        blnReturn = true;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetiTunesPrefs");
            }

            return blnReturn;
        }

        #endregion

        #region --- 同步推导照片操作 ---

        public DateTime GetDeviceTime()
        {
            DateTime dtReturn = DateTime.Now;
            string strFilePath = "/iTunes_Control/iTunes/Tongbu/" + Guid.NewGuid().ToString("N");
            iPhoneFileInfo info = null;
            try
            {
                this.CreateDirectoryByAFC(strFilePath);
                this.IsDirectory(strFilePath, ref info);
                if (info != null)
                    dtReturn = info.st_mtime;

                this.DeleteDirectoryByAFC(strFilePath);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "Device_GetDeviceTime");
            }

            return dtReturn;
        }

        public TuiPhotoStatus GetTuiPhotoStatus(IntPtr tuiAfc)
        {
            TuiPhotoStatus status = TuiPhotoStatus.None;
            try
            {
                string strSPlist = "/Documents/s.plist";
                string strTmp = Folder.GetTempFilePath();
                //如果文件不存在就说明同步推没打开。
                iPhoneFileInfo info = this.GetFileInfo(tuiAfc, strSPlist);
                if (info != null && info.st_birthtime != null && info.st_birthtime.AddMinutes(10) < this.GetDeviceTime())
                    return status;

                Dictionary<object, object> dict = this.GetDevicePlist(tuiAfc, strSPlist);
                if (dict == null || dict.Count <= 0 || !dict.ContainsKey("cmd"))
                {
                    return status;
                }
                int intValue = (int)dict["cmd"];
                switch (intValue)
                {
                    case -1:
                        status = TuiPhotoStatus.Error;
                        break;
                    case 1:
                        status = TuiPhotoStatus.Ready;
                        break;
                    case 2:
                        status = TuiPhotoStatus.Importing;
                        break;
                    case 3:
                        status = TuiPhotoStatus.Completed;
                        break;
                }
                //Dictionary<object, object> dictData = (Dictionary<object, object>)dict["data"];
                //int intIndex = (int)dictData["index"];
                //string strErrorMsg = (string)dictData["msg"];
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "Device_GetTuiPhotoStatus");
            }
            return status;
        }

        public bool SetTuiPhotoStatus(IntPtr tuiAfc, TuiPhotoStatus strtus, int intCount, int intIndex)
        {
            bool blnReturn = true;
            try
            {
                string strSPlist = "/Documents/c.plist";
                Dictionary<object, object> dict = new Dictionary<object, object>();
                dict.Add("cmd", (int)strtus);
                dict.Add("time", DateTime.Now.ToFileTimeUtc());
                Dictionary<object, object> dictData = new Dictionary<object, object>();
                dictData.Add("count", intCount);
                dictData.Add("index", intIndex);
                dict.Add("data", dictData);
                blnReturn = this.SetDevicePlist(tuiAfc, strSPlist, dict);
            }
            catch (Exception ex)
            {
                blnReturn = false;
                Common.LogException(ex.ToString(), "Device_SetTuiPhotoStatus");
            }
            return blnReturn;
        }

        public bool SetDevicePlist(IntPtr tuiAfc, string strPlistPathOnPhone, Dictionary<object, object> dict)
        {
            bool blnReturn = true;
            try
            {
                string strSPlistTmp = strPlistPathOnPhone + "Tmp";
                string strPlistInPC = Folder.GetTempFilePath();
                CoreFoundation.CreatePlist(dict, strPlistInPC);
                if (this.Exists(tuiAfc, strPlistPathOnPhone))
                {
                    this.DeleteFile(tuiAfc, strPlistPathOnPhone);
                }
                //用修改名字的方式同步推才可以识别到文件
                if (this.Exists(tuiAfc, strSPlistTmp))
                {
                    this.DeleteFile(tuiAfc, strSPlistTmp);
                }
                blnReturn = this.CopyToPhone(tuiAfc, strPlistInPC, strSPlistTmp);
                blnReturn = this.Rename(tuiAfc, strSPlistTmp, strPlistPathOnPhone);
                Common.LogException("DEBUG_SetDevicePlist", CoreFoundation.CreatePlistString(dict));
            }
            catch (Exception ex)
            {
                blnReturn = false;
                Common.LogException(ex.ToString(), "Device_SetDevicePlist");
            }
            return blnReturn;
        }

        public Dictionary<object, object> GetDevicePlist(IntPtr tuiAfc, string strPlistPathOnPhone)
        {
            Dictionary<object, object> dict = null;
            try
            {
                string strTmp = Folder.GetTempFilePath();
                //如果文件不存在就说明同步推没打开。
                Common.Log("DEBUG_GetDevicePlist:" + strTmp);
                if (!this.Exists(tuiAfc, strPlistPathOnPhone) || !this.DownFromPhone(tuiAfc, strPlistPathOnPhone, strTmp))
                {
                    return dict;
                }
                dict = CoreFoundation.ReadPlist_managed(strTmp) as Dictionary<object, object>;
                Common.LogException("DEBUG_GetDevicePlist", CoreFoundation.CreatePlistString(dict));
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "Device_GetDevicePlist");
            }
            return dict;
        }

        #endregion

        #region --- 取消ios升级提示 ---

#if !TOOLS

        public void CanceliOSUpgrade()
        {
            try
            {
                string strUrl = DeviceIni.GetiOSUpgradePlugin(this.VersionNumber.ToString().Substring(0, 2)); ;
                string strPath = string.Empty;

                if (!string.IsNullOrEmpty(strUrl))
                {
                    strPath = Path.Combine(Folder.Plugins, Path.GetFileName(strUrl));
                }

                if (!string.IsNullOrEmpty(strPath) && !File.Exists(strPath))
                {
                    Common.DownloadImage(strUrl, 20000, strPath);
                }

                if (File.Exists(strPath))
                {
                    byte[] arrData = File.ReadAllBytes(strPath);
                    this.InstallConfigurationProfile(arrData);
                }
                else
                {
                    this.InstallConfigurationProfile(global::iTong.Device.Properties.Resources.CanceliOSUpgrade12);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iPhoneDevice_CanceliOSUpgrade");
            }
        }
#endif

        #endregion
    }
}
