#!/bin/bash

# Remote Support Bundle ID 冲突清理脚本
# 用于解决macOS 10.13.6和11.7.10上的"应用已运行"问题

echo "开始清理Remote Support应用状态..."

# 1. 强制终止所有相关进程
echo "1. 终止所有Remote Support进程..."
sudo pkill -f "Remote Support" 2>/dev/null || true
killall "Remote Support" 2>/dev/null || true
killall "Remote Support Service" 2>/dev/null || true

# 等待进程完全退出
sleep 2

# 2. 卸载所有launchctl服务
echo "2. 卸载launchctl服务..."
launchctl unload ~/Library/LaunchAgents/com.sandstudio.remotesupport.windows.plist 2>/dev/null || true
sudo launchctl unload /Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist 2>/dev/null || true
launchctl unload ~/Library/LaunchAgents/com.sandstudio.remotesupport.video.plist 2>/dev/null || true
launchctl unload ~/Library/LaunchAgents/com.sandstudio.remotesupport.safemode.plist 2>/dev/null || true
launchctl unload ~/Library/LaunchAgents/com.sandstudio.remotesupport.proxy.plist 2>/dev/null || true

# 等待服务卸载完成
sleep 1

# 3. 清理可能残留的plist文件（如果需要完全重新安装）
if [ "$1" = "--full-cleanup" ]; then
    echo "3. 执行完全清理..."
    rm -f ~/Library/LaunchAgents/com.sandstudio.remotesupport.*.plist 2>/dev/null || true
    sudo rm -f /Library/LaunchDaemons/com.sandstudio.remotesupport.*.plist 2>/dev/null || true
fi

# 4. 清理系统缓存
echo "4. 清理系统缓存..."
sudo rm -rf /Library/Caches/AirDroidRemoteSupport 2>/dev/null || true

# 5. 重新加载服务（如果应用仍然存在）
if [ -d "/Applications/Remote Support.app" ]; then
    echo "5. 重新加载服务..."
    
    # 重新加载Service进程
    if [ -f "/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist" ]; then
        sudo launchctl load /Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist
        sleep 1
    fi
    
    # 重新加载Windows进程
    if [ -f "/Library/LaunchAgents/com.sandstudio.remotesupport.windows.plist" ]; then
        launchctl load /Library/LaunchAgents/com.sandstudio.remotesupport.windows.plist
        sleep 1
    fi
fi

echo "清理完成！"
echo ""
echo "使用方法："
echo "  ./cleanup_bundle_conflicts.sh           # 标准清理"
echo "  ./cleanup_bundle_conflicts.sh --full-cleanup  # 完全清理（删除plist文件）"
echo ""
echo "如果问题仍然存在，请重启系统后再试。"
