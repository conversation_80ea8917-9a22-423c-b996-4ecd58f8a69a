<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>LSMinimumSystemVersion</key>
	<string>10.9</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>NSHumanReadableCopyright</key>
	<string>Sand Studio, Copyright © 2024 airdroid.com All Rights Reserved.</string>
	<key>NSPrincipalClass</key>
	<string>NSApplication</string>
	<key>NSMainNibFile</key>
	<string>MainMenu</string>
	<key>LangName</key>
	<string>zh-CN</string>
	<key>CFBundleIconFile</key>
	<string>rsh.icns</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSMicrophoneUsageDescription</key>
	<string>Is access to the microphone allowed?</string>
	<key>NSCameraUsageDescription</key>
	<string>Is access to the camera allowed?</string>
	<key>LSApplicationCategoryType</key>
	<string>public.app-category.utilities</string>
	<key>CFBundleDisplayName</key>
	<string>AirDroid Remote Support Host</string>
	<key>CFBundleExecutable</key>
	<string>Remote Support Service</string>
	<key>MonoBundleExecutable</key>
	<string>Remote Support Service.exe</string>
	<key>CFBundleIdentifier</key>
	<string>com.sandstudio.remotesupport.service</string>
	<key>CFBundleName</key>
	<string>AirDroid Remote Support</string>
	<key>CFBundleShortVersionString</key>
	<string>*******</string>
	<key>CFBundleVersion</key>
	<string>build 1</string>
	<key>LSBackgroundOnly</key>
	<true/>
	<key>LSMultipleInstancesProhibited</key>
	<false/>
	<key>LSUIElement</key>
	<string>1</string>
</dict>
</plist>
