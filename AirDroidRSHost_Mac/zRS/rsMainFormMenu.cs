﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.IO;
using System.Threading;
using System.Security.Cryptography;
using System.Drawing;
using System.Collections.Specialized;
using System.Xml;
using System.Diagnostics;
using System.Management;
using System.Globalization;
using System.Runtime.InteropServices;
using System.Windows.Forms;

using iTong.CoreModule;
using iTong.CoreFoundation;
using iTong.Device;
using iTong.Android;

using CoreFoundation;
using ObjCRuntime;
using CoreGraphics;
using AppKit;
using Foundation;
using Size = CoreGraphics.CGSize;
using Point = CoreGraphics.CGPoint;
using Rectangle = CoreGraphics.CGRect;
using Font = AppKit.NSFont;
using Color = AppKit.NSColor;
using Icon = AppKit.NSImage;
using Image = AppKit.NSImage;

using skContextMenuStrip = iTong.CoreModule.skMenu;
using ToolStripMenuItem = iTong.CoreModule.skMenuItem;
using ToolStripItem = iTong.CoreModule.skMenuItem;
using PictureBox = iTong.CoreModule.skPictureBox;
using Label = iTong.CoreModule.skLabel;
using NotifyIcon = iTong.CoreModule.skStatusItem;
using ContentAlignment = iTong.CoreModule.ContentAlignment;


namespace iTong.CoreModule
{
    public partial class rsMainForm
    {
        private long mTimePass = 0;

        private void tsmiExit_Click(object sender, EventArgs e)
        {
            try
            {
                skMsgInfoNew msg;
#if BIZ
                msg = RsCommon.GetDefaultMsgInfo(this.Language.GetString("Biz_exit"), "AirDroid Biz Daemon", this.Language);
#else
                msg = RsCommon.GetDefaultMsgInfo(string.Format(this.Language.GetString("rs_exit_running_background")), this.Language.GetString("pc_rs_intro_title"), this.Language);
#endif

                msg.FirstButton.skText = this.Language.GetString("Common_Confirm");
                msg.TopMost = true;
                msg.MessageInfo.skWordWrapping = true;
                msg.ButtonPadding = new Padding(10, 2, 10, 0);

                if (MsgBoxMgr.Show(null, msg) != DialogResult.OK)
                {
                    return;
                }

                foreach (skControlForm form in Application.OpenForms)
                {
                    if (form == null)
                        continue;

                    form.Hide();
                }
            }
            catch (Exception ex) { }

            this.ShowInTaskbar = false;

            this.NotifyIcon.Visible = false;

#if !MAC
            InputSender.RemoveClipboardFormatListener(this.Handle);
#endif

            this.mActionHelper.SaveDB(true);

            OnExitApp();
        }

        private void tsmiFeedback_Click(object sender, EventArgs e)
        {
            if (PageHelper.HasInitCef)
            {
                Form form = MyForm.GetMainForm(typeof(frmFeedback).Name, false);
                if (form == null)
                {
                    if (this.frmFeedback == null)
                    {
                        this.frmFeedback = new frmFeedback();
                        //this.frmFeedback.Owner = this;
                        this.frmFeedback.FormClosing += frmFeedback_FormClosed;
                        this.frmFeedback.ShowDialog();
                    }
                    else
                    {
                        this.frmFeedback.Activate();
                    }
                }
            }
        }

        private void tsmiRefreshPwd_Click(object sender, EventArgs e)
        {
            if (!Common.NetworkIsAvailable())
                return;

            this.mActionHelper.Add(tbActionModeKeyForRS.MainPwdRefresh);
            RSEvent arg = new RSEvent();
            arg.EventType = RSEventType.ShowPwdRefresh;
            SocketMgr.SendMsgFromClient(arg);
        }

        private void tsmiUpdataPwd_Click(object sender, EventArgs e)
        {
            skMsgInfoNew msg = RsCommon.GetDefaultMsgInfo(this.Language.GetString("rs_custom_connection_password"), this.Language.GetString("pc_rs_intro_title"), this.Language);
            msg.MessageInfo.Padding = new Padding(0, 0, 0, 10);
            msg.ButtonPadding = new Padding(10, 2, 10, 0);

            msg.InputBoxInfo.IsPasswordMode = true;
            msg.InputBoxInfo.Visible = true;
            msg.InputBoxInfo.MaxLength = 20;
            msg.InputBoxInfo.MinLength = 8;
            msg.InputBoxInfo.PlaceHolder = this.Language.GetString("enter_8_to_20_characters");
            msg.InputBoxInfo.Padding = new Padding(2, 8, 2, 10);

            //#if RS
            // 多选框
            msg.CheckBoxInfo.skIcon = MyResource.GetImage("rs_chk.png");
            msg.CheckBoxInfo.skIconSize = new Size(14, 14);
            msg.CheckBoxInfo.skIconCheck = MyResource.GetImage("rs_chk_check.png");
            msg.CheckBoxInfo.skTextFont = MyFont.CreateFont("微软雅黑", 9.75f);
            msg.CheckBoxInfo.skTextColor = skColor.FromArgb(102, 107, 117);
            msg.CheckBoxInfo.skText = this.Language.GetString("set_fixed_password");
            msg.CheckBoxInfo.Padding = new Padding(0, 0, 0, 24);

            msg = RsCommon.SetClearMsgInfo(msg, MyResource.GetImage("btn_clear_4.png"));
            msg = RsCommon.SetPasswordMsgInfo(msg, MyResource.GetImage("btn_main_hide_4.png"), MyResource.GetImage("btn_main_show_4.png"));
            //#endif

            msg.EventCallBack += () => {
                try
                {
                    if (!Common.NetworkIsAvailable())
                    {
                        RsCommon.ShowSplashBox(this, this.Language.GetString("Common_check_network"));
                        return;
                    }

                    if (msg.InputBoxInfo.skText.Length < msg.InputBoxInfo.MinLength)
                    {
                        RsCommon.SetErrorMsgInfo(msg, this.Language.GetString("rs_enter_numbers_characters"), updateText: false);
                        msg.ErrorInfo.Padding = new Padding(0, 0, 0, 0);
                        MsgBoxMgr.UpdateText(msg);

                        return;
                    }

                    if (RsAPI.UpdatePwd(RsController.Instance.ShareCode, RsAPI.LocalSetting.Pwd, msg.InputBoxInfo.skText))
                    {
                        RsController.Instance.Password = msg.InputBoxInfo.skText;
                        CheckDeviceDeployInfo();

                        if (msg.CheckBoxInfo.skChecked)
                            RsAPI.LocalSetting.PwdType = rsPwdType.UpdateByMan;
                        else
                            RsAPI.LocalSetting.PwdType = rsPwdType.UpdateAfterDisconnect;

                        RsAPI.SaveLocalSetting(RsAPI.LocalSetting);

                        this.SendSettingChanged();

                        SocketMgr.SendMsgFromClient(RSEvent.Create(RSEventType.LocalSettingUpdate));

                        MsgBoxMgr.Remove(msg);
                        RsCommon.ShowSplashBox(this, this.Language.GetString("data_update"));
                    }
                }
                catch (Exception)
                {

                }
                finally
                {
                    MsgBoxMgr.StopBtnLoading(msg);
                }
            };

            MsgBoxMgr.Show(this, msg);
        }

        private bool CheckPwdFormat(string pwd)
        {
            bool res = false;

            Regex regex = new Regex("[0-9]");
            Match match = regex.Match(pwd);
            if (!match.Success)
                return res;

            regex = new Regex("[a-z]|[A-Z]");
            match = regex.Match(pwd);
            if (!match.Success)
                return res;

            res = true;
            return res;
        }

        private void tsmiClearLog_Click(object sender, EventArgs e)
        {
            Folder.ClearFolder(Folder.LogFolder);
            //Folder.ClearFolder(Folder.ExceptionFolder);
        }

        private void tsmiOpenLogFolder_Click(object sender, EventArgs e)
        {
            Common.OpenExplorer(Folder.LogFolder);
        }

        private void tsmiCreateLog_Click(object sender, EventArgs e)
        {
            ThreadMgr.Start(new ThreadStart(() => {
                RsCommon.CreateLogZip();
            }));
        }

        private void tsmiCheckUpdate_Click(object sender, EventArgs e)
        {
            //if (!Common.NetworkIsAvailable())
            //{
            //    if (this.Language == null)
            //        this.Language = LanguageInterface.Instance();

            //    skMessageBox.Show(this, this.Language.GetString("Main.Message.NetworkDisconnectCanNotUpgrade"), this.Language.GetString("pc_rs_intro_title"), MessageBoxButtons.OK);//网络不通，无法完成升级，请检查您的网络
            //    return;
            //}

            MyUpdate.CheckUpdate(true, false, this.Language.CurrentLanguage.LangName);
        }

        /// <summary>卸载app</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void tsmiUnloadApp_Click(object sender, EventArgs e)
        {
            string title = this.Language.GetString("app_uninstall_rs_title");
            string content = this.Language.GetString("uninstall_confirmation_dialog");
            string checkBoxContent = this.Language.GetString("app_data_retention_option");

            skMsgInfoNew skMsgInfo = RsCommon.GetDefaultMsgInfo(content, title, this.Language);
            skMsgInfo.ButtonPadding = new Padding(10, 2, 10, 0);

            // 多选框
            skMsgInfo.CheckBoxInfo.skIcon = MyResource.GetImage("rs_chk.png");
            skMsgInfo.CheckBoxInfo.skIconSize = new Size(14, 14);
            skMsgInfo.CheckBoxInfo.skIconCheck = MyResource.GetImage("rs_chk_check.png");
            skMsgInfo.CheckBoxInfo.skTextFont = MyFont.CreateFont("微软雅黑", 9.75f);
            skMsgInfo.CheckBoxInfo.skTextColor = skColor.FromArgb(102, 107, 117);
            skMsgInfo.CheckBoxInfo.skText = checkBoxContent;
            skMsgInfo.CheckBoxInfo.Padding = new Padding(0, 0, 0, 24);

            skMsgInfo = RsCommon.SetClearMsgInfo(skMsgInfo, MyResource.GetImage("btn_clear_4.png"));

            skMsgInfo.MessageInfo.Padding = new Padding(0, 0, 0, 6);

            if (MsgBoxMgr.Show(this, skMsgInfo) != DialogResult.OK)
                return;

            SocketMgr.SendMsgFromClient(RSEvent.Create(RSEventType.UnloadApp, skMsgInfo.CheckBoxInfo.skChecked.ToString()));
        }

        private void tmrStart_Tick(object sender, System.Timers.ElapsedEventArgs e)
        {
            try
            {
                this.mTimePass += (long)this.tmrStart.Interval / 1000;

                long Interval = 0;

                if (MyLog.IsTestMode || MyLog.ShowLog)
                {
                    string strPath = Path.Combine(Folder.ApplicationDataFolder, "Update.ini");
                    Interval = IniHelper.GetValue<long>("Setting", "Interval", 0, strPath);
                }

                if (Interval <= 0)
                    Interval = 3600 * 24;//一天

                if (this.mTimePass % Interval == 5)
                {
                    if (Common.NetworkIsAvailable())
                        MyUpdate.CheckUpdate(true, true, this.Language.CurrentLanguage.LangName);
                }

                //第5秒、每5分钟抛一次统计
                if (this.mTimePass == 5 || (this.mTimePass % 300 == 0))
                    this.mActionHelper.StartPostData();
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "tmrStart_Tick");
            }
        }  
    }
}
