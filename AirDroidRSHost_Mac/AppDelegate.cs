using System;
using System.Collections.Generic;
using System.Linq;
using System.ComponentModel;
using System.IO;

using AppKit;
using Foundation;
using ObjCRuntime;
using CoreGraphics;


using iTong.Android;
using iTong.CoreFoundation;
using iTong.CoreModule;

namespace AirDroidRemoteSupportHost
{
	//public static class AEKeyword
	//{
	//	public static readonly uint DirectObject = Create("----");
	//	public static readonly uint ErrorNumber = Create("errn");
	//	public static readonly uint ErrorString = Create("errs");
	//	public static readonly uint ProcessSerialNumber = Create("psn ");
	//	public static readonly uint PreDispatch = Create("phac");
	//	public static readonly uint SelectProc = Create("selh");
	//	public static readonly uint AERecorderCount = Create("recr");
	//	public static readonly uint AEVersion = Create("vers");

	//	private static uint Create(string key)
	//	{
	//		return (((uint)key[0]) << 24 |
	//				((uint)key[1]) << 16 |
	//				((uint)key[2]) << 8 |
	//				((uint)key[3]) << 0);
	//	}
	//}

	public partial class AppDelegate : NSApplicationDelegate
	{

        protected skWindow MainWindow { get; set; }

		public static AppDelegate Instance
		{
			get
			{
				return NSApplication.SharedApplication.Delegate as AppDelegate;
			}
		}

		public void ExitApp()
		{
			NSApplication.SharedApplication.Terminate(this);
		}

		public override void DidFinishLaunching (NSNotification notification)
		{
            //通过WebView的策略控制来实现URL跳转打开

            string[] arrPara = ParaMgr.Arguments;
            if (arrPara == null || arrPara.Length == 0)
                return;

            System.Windows.Forms.ExtendControl.IsWindowAnchor = true;

            switch (arrPara[0])
            {
                case ParaMgr.CommandRunWidnows:
                    {
                        rsMainForm view = new rsMainForm();
                        view.Show();

                        //保存MainWindow
                        this.MainWindow = (skWindow)view.Window;

                        break;
                    }

                case ParaMgr.CommandRunSafeMode:
                    {
                        frmSafeMode.Init();
                        break;
                    }
            }
        }

        /// <summary>
        /// 协议：weixin://, tongbu:// 避免WebKit弹出窗体(需要设置info.plist中的URL types的URL Schemes
        /// </summary>
        /// <param name="descriptor">Descriptor.</param>
        /// <param name="replyEvent">Reply event.</param>
        //[Export("handleGetURLEvent:withReplyEvent:")]
        //private void HandleGetURLEvent(NSAppleEventDescriptor descriptor, NSAppleEventDescriptor replyEvent)
        //{
        //	try
        //	{
        //		string urlStr = descriptor.ParamDescriptorForKeyword(AEKeyword.DirectObject).StringValue;
        //		Console.WriteLine("HandleGetURLEvent={0}", urlStr);

        //		// 处理微信weixin://的协议
        //		ChatWebPage.Navigating(urlStr);
        //	}
        //	catch (Exception ex)
        //	{
        //		Console.WriteLine(ex.ToString());
        //	}
        //}

        /// <summary>
        /// 处理airdroid://协议的事件，需要info.plist配合
        /// </summary>
        /// <param name="obj">Object.</param>
        //[Export("handleUrlEvent:")]
        //private void handleUrlEvent(NSObject obj)
        //{

        //	if (obj != null)
        //		Console.WriteLine("handleUrlEvent: " + obj.ToString());
        //}

        //实现单击窗体的X按钮时，关闭窗体
        //public override bool ApplicationShouldTerminateAfterLastWindowClosed(NSApplication sender)
        //{
        //    ServiceMgr.LaunchctlUnload(ServiceMgr.ServiceNameForRS_Windows);
        //    return false;
        //}

        
        public override void WillTerminate(NSNotification notification)
        {
            ServiceMgr.ExitAppForRS();
        }

        // 实现Dock栏退出时触发
        //public override NSApplicationTerminateReply ApplicationShouldTerminate(NSApplication sender)
        //{
        //          ThreadMgr.AbortAll();
        //	TimerMgr.DisposeAll();

        //	return NSApplicationTerminateReply.Now;
        //}

        //      NSMenu dockMenu;

        //      public override NSMenu ApplicationDockMenu(NSApplication sender)
        //      {
        //          // 如果dockMenu为空，创建一个新的菜单
        //          if (dockMenu == null)
        //          {
        //              dockMenu = new NSMenu();

        //              // 创建菜单项
        //              var menuItem = new NSMenuItem("Say Hello", SayHello);
        //              dockMenu.AddItem(menuItem);
        //          }

        //          return dockMenu;
        //      }

        //      void SayHello(object sender, EventArgs e)
        //      {
        //          // 在这里处理菜单项点击事件
        //          var alert = new NSAlert
        //          {
        //              MessageText = "Hello from Dock Menu!",
        //              AlertStyle = NSAlertStyle.Informational
        //          };
        //          alert.RunModal();
        //      }


        public override bool ApplicationShouldHandleReopen(NSApplication sender, bool hasVisibleWindows)
        {
            if (this.MainWindow != null && !this.MainWindow.IsVisible)
            {
                //NSApplication.SharedApplication.doe
                this.MainWindow.OrderFront(null);               
            }

            return false;
        }
    }
}
