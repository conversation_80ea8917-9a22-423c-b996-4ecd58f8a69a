<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{1835994D-840A-4336-8784-E24C95AF719B}</ProjectGuid>
    <ProjectTypeGuids>{A3F8F2AB-B479-4A4A-A458-A89E7DC349F1};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>Exe</OutputType>
    <RootNamespace>RemoteSupportHost</RootNamespace>
    <MonoMacResourcePrefix>Resources</MonoMacResourcePrefix>
    <AssemblyName>Remote Support</AssemblyName>
    <ReleaseVersion>1.0.0</ReleaseVersion>
    <TargetFrameworkVersion>v4.6</TargetFrameworkVersion>
    <SynchReleaseVersion>false</SynchReleaseVersion>
    <UseXamMacFullFramework>true</UseXamMacFullFramework>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\bin\Debug</OutputPath>
    <DefineConstants>__MACOS__;__UNIFIED__;DEBUG;MAC;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <ConsolePause>false</ConsolePause>
    <Profiling>true</Profiling>
    <UseRefCounting>true</UseRefCounting>
    <UseSGen>true</UseSGen>
    <IncludeMonoRuntime>false</IncludeMonoRuntime>
    <CreatePackage>false</CreatePackage>
    <CodeSigningKey>Developer ID Application: Xiamen Tongbu Networks Co., Ltd. (J4745728M6)</CodeSigningKey>
    <EnableCodeSigning>false</EnableCodeSigning>
    <EnablePackageSigning>false</EnablePackageSigning>
    <PackageSigningKey>Developer ID Installer</PackageSigningKey>
    <I18n>cjk,mideast,other,rare,west</I18n>
    <XamMacArch>x86_64,arm64</XamMacArch>
    <PlatformTarget>anycpu</PlatformTarget>
    <AOTMode>None</AOTMode>
    <LinkMode>None</LinkMode>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType></DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\bin\Release</OutputPath>
    <DefineConstants>__MACOS__;__UNIFIED__;MAC;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <ConsolePause>false</ConsolePause>
    <Profiling>false</Profiling>
    <UseRefCounting>true</UseRefCounting>
    <UseSGen>true</UseSGen>
    <IncludeMonoRuntime>true</IncludeMonoRuntime>
    <CreatePackage>true</CreatePackage>
    <CodeSigningKey>Developer ID Application</CodeSigningKey>
    <EnableCodeSigning>false</EnableCodeSigning>
    <EnablePackageSigning>false</EnablePackageSigning>
    <PackageSigningKey>Developer ID Installer</PackageSigningKey>
    <AOTMode>None</AOTMode>
    <LinkMode>None</LinkMode>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Main.cs" />
    <Compile Include="AppDelegate.cs" />
    <Compile Include="AppDelegate.designer.cs">
      <DependentUpon>AppDelegate.cs</DependentUpon>
    </Compile>    
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\Mac\Xamarin.Mac.CSharp.targets" />
  <ItemGroup>
    <Reference Include="System.Core" />
    <Reference Include="System" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data" />
    <Reference Include="Xamarin.Mac" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Info.plist" />
    <BundleResource Include="Resources\rsh.icns" />
    <BundleResource Include="..\AppUnion\ResourcesUrl\weburl_RemoteSupport">
      <Link>Resources\weburl_RemoteSupport</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\audio_incoming_call">
      <Link>Resources\audio_incoming_call</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\audio_message">
      <Link>Resources\audio_message</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\audio_transfer_end">
      <Link>Resources\audio_transfer_end</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\audio_transfer_start">
      <Link>Resources\audio_transfer_start</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\chat_btn_4_send_file.png">
      <Link>Resources\chat_btn_4_send_file.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\chat_btn_4_voice_cancel.png">
      <Link>Resources\chat_btn_4_voice_cancel.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\chat_btn_4_voice_ok.png">
      <Link>Resources\chat_btn_4_voice_ok.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\chat_btn_4_voice.png">
      <Link>Resources\chat_btn_4_voice.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\chat_btn_4_voip.png">
      <Link>Resources\chat_btn_4_voip.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\chat_gif_voice.gif">
      <Link>Resources\chat_gif_voice.gif</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\chat_voip_connecting.png">
      <Link>Resources\chat_voip_connecting.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_3gp_32.png">
      <Link>Resources\icon_file_3gp_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_aac_32.png">
      <Link>Resources\icon_file_aac_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_amr_32.png">
      <Link>Resources\icon_file_amr_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_apk_32.png">
      <Link>Resources\icon_file_apk_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_avi_32.png">
      <Link>Resources\icon_file_avi_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_dng_32.png">
      <Link>Resources\icon_file_dng_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_doc_32.png">
      <Link>Resources\icon_file_doc_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_docx_32.png">
      <Link>Resources\icon_file_docx_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_exe_32.png">
      <Link>Resources\icon_file_exe_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_flac_32.png">
      <Link>Resources\icon_file_flac_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_folder_32.png">
      <Link>Resources\icon_file_folder_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_gif_32.png">
      <Link>Resources\icon_file_gif_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_gz_32.png">
      <Link>Resources\icon_file_gz_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_jpeg_32.png">
      <Link>Resources\icon_file_jpeg_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_jpg_32.png">
      <Link>Resources\icon_file_jpg_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_m4a_32.png">
      <Link>Resources\icon_file_m4a_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_mkv_32.png">
      <Link>Resources\icon_file_mkv_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_mov_32.png">
      <Link>Resources\icon_file_mov_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_mp3_32.png">
      <Link>Resources\icon_file_mp3_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_mp4_32.png">
      <Link>Resources\icon_file_mp4_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_music_32.png">
      <Link>Resources\icon_file_music_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_opus_32.png">
      <Link>Resources\icon_file_opus_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_other_32.png">
      <Link>Resources\icon_file_other_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_pdf_32.png">
      <Link>Resources\icon_file_pdf_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_photo_32.png">
      <Link>Resources\icon_file_photo_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_pic_32.png">
      <Link>Resources\icon_file_pic_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_png_32.png">
      <Link>Resources\icon_file_png_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_ppt_32.png">
      <Link>Resources\icon_file_ppt_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_pptx_32.png">
      <Link>Resources\icon_file_pptx_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_rar_32.png">
      <Link>Resources\icon_file_rar_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_text_32.png">
      <Link>Resources\icon_file_text_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_txt_32.png">
      <Link>Resources\icon_file_txt_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_video_32.png">
      <Link>Resources\icon_file_video_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_wav_32.png">
      <Link>Resources\icon_file_wav_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_wma_32.png">
      <Link>Resources\icon_file_wma_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_xls_32.png">
      <Link>Resources\icon_file_xls_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_xlsx_32.png">
      <Link>Resources\icon_file_xlsx_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\icon_file_zip_32.png">
      <Link>Resources\icon_file_zip_32.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\web_page.zip">
      <Link>Resources\web_page.zip</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\chat_btn_4_closure.png">
      <Link>Resources\chat_btn_4_closure.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\chat_btn_4_minimize.png">
      <Link>Resources\chat_btn_4_minimize.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\chat_btn_4_phone.png">
      <Link>Resources\chat_btn_4_phone.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourceWeb\chat_btn_4_send_msg.png">
      <Link>Resources\chat_btn_4_send_msg.png</Link>
    </BundleResource>
    <BundleResource Include="..\DllImport\Codec\ffmpeg">
      <Link>Resources\Codec\ffmpeg</Link>
    </BundleResource>
    <BundleResource Include="Resources\airdroid_48.png" />
    <BundleResource Include="Resources\airdroid_48%402x.png" />
    <BundleResource Include="Resources\airdroid.ico" />
    <BundleResource Include="Resources\airdroid%402x.ico" />
    <BundleResource Include="Resources\btn_clear_4.png" />
    <BundleResource Include="Resources\btn_clear_4%402x.png" />
    <BundleResource Include="Resources\btn_grey_4.png" />
    <BundleResource Include="Resources\btn_grey_4%402x.png" />
    <BundleResource Include="Resources\btn_line_blue_4%402x.png" />
    <BundleResource Include="Resources\btn_line_blue.png" />
    <BundleResource Include="Resources\btn_main_copy_4.png" />
    <BundleResource Include="Resources\btn_main_copy_4%402x.png" />
    <BundleResource Include="Resources\btn_main_hide_4.png" />
    <BundleResource Include="Resources\btn_main_hide_4%402x.png" />
    <BundleResource Include="Resources\btn_main_illustrate_4.png" />
    <BundleResource Include="Resources\btn_main_illustrate_4%402x.png" />
    <BundleResource Include="Resources\btn_main_refresh_1.png" />
    <BundleResource Include="Resources\btn_main_refresh_1%402x.png" />
    <BundleResource Include="Resources\btn_main_setup_4.png" />
    <BundleResource Include="Resources\btn_main_setup_4%402x.png" />
    <BundleResource Include="Resources\btn_main_show_4.png" />
    <BundleResource Include="Resources\btn_main_show_4%402x.png" />
    <BundleResource Include="Resources\btn_main_switch_4.png" />
    <BundleResource Include="Resources\btn_main_switch_4%402x.png" />
    <BundleResource Include="Resources\btn_solid_red_40.png" />
    <BundleResource Include="Resources\btn_solid_red_40%402x.png" />
    <BundleResource Include="Resources\btn_update_1.png" />
    <BundleResource Include="Resources\btn_update_1%402x.png" />
    <BundleResource Include="Resources\btn_update_4.png" />
    <BundleResource Include="Resources\btn_update_4%402x.png" />
    <BundleResource Include="Resources\frm_bg_shadow_6.png" />
    <BundleResource Include="Resources\frm_bg_shadow_6%402x.png" />
    <BundleResource Include="Resources\frm_bg_shadow_8.png" />
    <BundleResource Include="Resources\frm_bg_shadow_8%402x.png" />
    <BundleResource Include="Resources\frm_bg_shadow.png" />
    <BundleResource Include="Resources\frm_bg_shadow%402x.png" />
    <BundleResource Include="Resources\ic_chromemaximize.png" />
    <BundleResource Include="Resources\ic_chromemaximize%402x.png" />
    <BundleResource Include="Resources\ic_chromeminimize.png" />
    <BundleResource Include="Resources\ic_chromeminimize%402x.png" />
    <BundleResource Include="Resources\ic_close.png" />
    <BundleResource Include="Resources\ic_close%402x.png" />
    <BundleResource Include="Resources\loading2.gif" />
    <BundleResource Include="Resources\loading2%402x.gif" />
    <BundleResource Include="Resources\pic_main_bg.gif" />
    <BundleResource Include="Resources\pic_main_bg%402x.gif" />
    <BundleResource Include="Resources\pic_main_border.png" />
    <BundleResource Include="Resources\pic_main_border%402x.png" />
    <BundleResource Include="Resources\pic_main_green.png" />
    <BundleResource Include="Resources\pic_main_green%402x.png" />
    <BundleResource Include="Resources\pic_main_red.png" />
    <BundleResource Include="Resources\pic_main_red%402x.png" />
    <BundleResource Include="Resources\pic_main_unattended_bg.png" />
    <BundleResource Include="Resources\pic_main_unattended_bg%402x.png" />
    <BundleResource Include="Resources\rs_chk_check.png" />
    <BundleResource Include="Resources\rs_chk_check%402x.png" />
    <BundleResource Include="Resources\rs_chk.png" />
    <BundleResource Include="Resources\rs_chk%402x.png" />
    <BundleResource Include="Resources\rs_pic_disconnect_Icon.png" />
    <BundleResource Include="Resources\rs_pic_disconnect_Icon%402x.png" />
    <BundleResource Include="Resources\setting_about_check.png" />
    <BundleResource Include="Resources\setting_about_check%402x.png" />
    <BundleResource Include="Resources\setting_about_nor.png" />
    <BundleResource Include="Resources\setting_about_nor%402x.png" />
    <BundleResource Include="Resources\setting_leftnav_bg.png" />
    <BundleResource Include="Resources\setting_leftnav_bg%402x.png" />
    <BundleResource Include="Resources\setting_setup_check.png" />
    <BundleResource Include="Resources\setting_setup_check%402x.png" />
    <BundleResource Include="Resources\setting_setup_nor%402x.png" />
    <BundleResource Include="Resources\translate.ico" />
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_arrow_down.png">
      <Link>Resources\rs_arrow_down.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_arrow_down%402x.png">
      <Link>Resources\rs_arrow_down%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_arrow_left.png">
      <Link>Resources\rs_arrow_left.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_arrow_left%402x.png">
      <Link>Resources\rs_arrow_left%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_arrow_right.png">
      <Link>Resources\rs_arrow_right.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_arrow_right%402x.png">
      <Link>Resources\rs_arrow_right%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_arrow_up.png">
      <Link>Resources\rs_arrow_up.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_arrow_up%402x.png">
      <Link>Resources\rs_arrow_up%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_btn_blue_4.png">
      <Link>Resources\rs_btn_blue_4.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_btn_blue_4%402x.png">
      <Link>Resources\rs_btn_blue_4%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_btn_green_4.png">
      <Link>Resources\rs_btn_green_4.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_btn_green_4%402x.png">
      <Link>Resources\rs_btn_green_4%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_btn_grey_4.png">
      <Link>Resources\rs_btn_grey_4.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_btn_grey_4%402x.png">
      <Link>Resources\rs_btn_grey_4%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_btn_red_4.png">
      <Link>Resources\rs_btn_red_4.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_btn_red_4%402x.png">
      <Link>Resources\rs_btn_red_4%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_btn_white_4.png">
      <Link>Resources\rs_btn_white_4.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_btn_white_4%402x.png">
      <Link>Resources\rs_btn_white_4%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_calling_4.png">
      <Link>Resources\rs_calling_4.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_calling_4%402x.png">
      <Link>Resources\rs_calling_4%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_calling.png">
      <Link>Resources\rs_calling.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_calling%402x.png">
      <Link>Resources\rs_calling%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_chk_disabled.png">
      <Link>Resources\rs_chk_disabled.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_chk_disabled%402x.png">
      <Link>Resources\rs_chk_disabled%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_chk_hover.png">
      <Link>Resources\rs_chk_hover.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_chk_hover%402x.png">
      <Link>Resources\rs_chk_hover%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_close_4.png">
      <Link>Resources\rs_close_4.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_close_4%402x.png">
      <Link>Resources\rs_close_4%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_frm_bg_shadow.png">
      <Link>Resources\rs_frm_bg_shadow.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_frm_bg_shadow%402x.png">
      <Link>Resources\rs_frm_bg_shadow%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_frm_corner.png">
      <Link>Resources\rs_frm_corner.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_frm_corner%402x.png">
      <Link>Resources\rs_frm_corner%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_frm_shadow.png">
      <Link>Resources\rs_frm_shadow.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_frm_shadow%402x.png">
      <Link>Resources\rs_frm_shadow%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_msg_4.png">
      <Link>Resources\rs_msg_4.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_msg_4%402x.png">
      <Link>Resources\rs_msg_4%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_msg_new_4.png">
      <Link>Resources\rs_msg_new_4.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_msg_new_4%402x.png">
      <Link>Resources\rs_msg_new_4%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_reject_4.png">
      <Link>Resources\rs_reject_4.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_reject_4%402x.png">
      <Link>Resources\rs_reject_4%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_tip.png">
      <Link>Resources\rs_tip.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_tip%402x.png">
      <Link>Resources\rs_tip%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_user_head.png">
      <Link>Resources\rs_user_head.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_user_head%402x.png">
      <Link>Resources\rs_user_head%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_voip_connect.png">
      <Link>Resources\rs_voip_connect.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_voip_connect%402x.png">
      <Link>Resources\rs_voip_connect%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_voip_reject.png">
      <Link>Resources\rs_voip_reject.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_voip_reject%402x.png">
      <Link>Resources\rs_voip_reject%402x.png</Link>
    </BundleResource>
    <BundleResource Include="Resources\pic_permission_high_version.png" />
    <BundleResource Include="Resources\pic_permission_low_version.png" />
    <BundleResource Include="Resources\pic_tips.png" />
    <BundleResource Include="Resources\pic_tips%402x.png" />
    <BundleResource Include="Resources\pic_bg_tips.png" />
    <BundleResource Include="Resources\pic_bg_tips%402x.png" />
    <BundleResource Include="Resources\rs_binding_Attended.png" />
    <BundleResource Include="Resources\rs_binding_Attended%402x.png" />
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_logo_black.png">
      <Link>Resources\rs_logo_black.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_logo_black%402x.png">
      <Link>Resources\rs_logo_black%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_logo_white.png">
      <Link>Resources\rs_logo_white.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\rs_logo_white%402x.png">
      <Link>Resources\rs_logo_white%402x.png</Link>
    </BundleResource>
    <BundleResource Include="Resources\pic_permission_high_version%402x.png" />
    <BundleResource Include="Resources\pic_permission_low_version%402x.png" />
    <BundleResource Include="..\AppUnion\ResourcesRS\setting_security_check.png">
      <Link>Resources\setting_security_check.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\setting_security_check%402x.png">
      <Link>Resources\setting_security_check%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\setting_security_nor.png">
      <Link>Resources\setting_security_nor.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\setting_security_nor%402x.png">
      <Link>Resources\setting_security_nor%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\pic_main_attended_bg.png">
      <Link>Resources\pic_main_attended_bg.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\pic_main_attended_bg%402x.png">
      <Link>Resources\pic_main_attended_bg%402x.png</Link>
    </BundleResource>
    <BundleResource Include="Lang\it-IT.lang">
      <Link>Resources\Lang\it-IT.lang</Link>
    </BundleResource>
    <BundleResource Include="Lang\zh-CN.lang">
      <Link>Resources\Lang\zh-CN.lang</Link>
    </BundleResource>
    <BundleResource Include="Lang\ja-JP.lang">
      <Link>Resources\Lang\ja-JP.lang</Link>
    </BundleResource>
    <BundleResource Include="Lang\es-ES.lang">
      <Link>Resources\Lang\es-ES.lang</Link>
    </BundleResource>
    <BundleResource Include="Lang\pt-PT.lang">
      <Link>Resources\Lang\pt-PT.lang</Link>
    </BundleResource>
    <BundleResource Include="Lang\zh-TW.lang">
      <Link>Resources\Lang\zh-TW.lang</Link>
    </BundleResource>
    <BundleResource Include="Lang\fr-FR.lang">
      <Link>Resources\Lang\fr-FR.lang</Link>
    </BundleResource>
    <BundleResource Include="Lang\de-DE.lang">
      <Link>Resources\Lang\de-DE.lang</Link>
    </BundleResource>
    <BundleResource Include="Lang\pt-BR.lang">
      <Link>Resources\Lang\pt-BR.lang</Link>
    </BundleResource>
    <BundleResource Include="Lang\ru-RU.lang">
      <Link>Resources\Lang\ru-RU.lang</Link>
    </BundleResource>
    <BundleResource Include="Lang\en-US.lang">
      <Link>Resources\Lang\en-US.lang</Link>
    </BundleResource>
    <BundleResource Include="Resources\btn_tip_background.png" />
    <BundleResource Include="Resources\btn_tip_background%402x.png" />
    <BundleResource Include="Resources\pic_screen_icon.png" />
    <BundleResource Include="Resources\pic_screen_icon%402x.png" />
    <BundleResource Include="Resources\pic_accessibility_icon.png" />
    <BundleResource Include="Resources\pic_accessibility_icon%402x.png" />
    <BundleResource Include="Resources\pic_full_icon.png" />
    <BundleResource Include="Resources\pic_full_icon%402x.png" />
    <BundleResource Include="Resources\pic_microphone_icon.png" />
    <BundleResource Include="Resources\pic_microphone_icon%402x.png" />
    <BundleResource Include="Resources\btn_allow_icon.png" />
    <BundleResource Include="Resources\btn_allow_icon%402x.png" />
    <BundleResource Include="Resources\btn_sleepTip_icon.png" />
    <BundleResource Include="Resources\btn_sleepTip_icon%402x.png" />
    <BundleResource Include="Resources\cms_updatePwdMode_selected_icon.png" />
    <BundleResource Include="Resources\cms_updatePwdMode_selected_icon%402x.png" />
    <BundleResource Include="Resources\btn_arrow_icon.png" />
    <BundleResource Include="Resources\btn_arrow_icon%402x.png" />
    <BundleResource Include="Resources\btn_arrow_down_icon.png" />
    <BundleResource Include="Resources\btn_arrow_down_icon%402x.png" />
    <BundleResource Include="Resources\btn_arrow_up_icon.png" />
    <BundleResource Include="Resources\btn_arrow_up_icon%402x.png" />
    <BundleResource Include="..\AppUnion\ResourcesRS\frmConnect_logo48%402x.png">
      <Link>Resources\frmConnect_logo48%402x.png</Link>
    </BundleResource>
    <BundleResource Include="..\AppUnion\ResourcesRS\frmConnect_logo48.png">
      <Link>Resources\frmConnect_logo48.png</Link>
    </BundleResource>
  </ItemGroup>
  <ItemGroup>
    <InterfaceDefinition Include="MainMenu.xib" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="English.lproj\InfoPlist.strings" />
    <Content Include="zh_CN.lproj\InfoPlist.strings" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Resources\" />
    <Folder Include="Resources\Codec\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Android\Android_Mac.csproj">
      <Project>{A2CAD0B7-AC6D-4E3B-9E3E-EBD69C4871C1}</Project>
      <Name>Android_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreMisc\CoreMisc_Mac.csproj">
      <Project>{3D7568EF-2BED-4B71-BBFA-D2F35BE5BF00}</Project>
      <Name>CoreMisc_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreTag\CoreTag_Mac.csproj">
      <Project>{338F4272-5C9F-47B6-B100-5634EF0D1DDE}</Project>
      <Name>CoreTag_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUpdate\CoreUpdate_Mac.csproj">
      <Project>{B71186D3-DD3B-405B-9C17-217CE7157E7C}</Project>
      <Name>CoreUpdate_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtilCS\CoreUtil_Mac.csproj">
      <Project>{913145D8-1271-42F3-BB1A-5D7694D6CA3C}</Project>
      <Name>CoreUtil_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\iPhoneCS\iPhone_Mac.csproj">
      <Project>{FB6055F3-5096-4D14-A2FE-78571A23C920}</Project>
      <Name>iPhone_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\ProtoBuf\ProtoBuf_Mac.csproj">
      <Project>{C8C2D342-F711-47C4-95CB-0AF620DA719B}</Project>
      <Name>ProtoBuf_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\SharpZLib\SharpZLib_Mac.csproj">
      <Project>{CBCC1A89-433D-4102-97F5-A68A500920EF}</Project>
      <Name>SharpZLib_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\System.Web\System.Web_Mac.csproj">
      <Project>{C5E45B23-1A73-418C-B1CA-B35D970B8D05}</Project>
      <Name>System.Web_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\zlib\zlib_Mac.csproj">
      <Project>{0E70E2EA-72A9-4406-8954-95D663386663}</Project>
      <Name>zlib_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreKid_Mac\CoreKid_Mac.csproj">
      <Project>{AD5EBBD5-1A3E-4532-A1B2-F8C10003DFA1}</Project>
      <Name>CoreKid_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\AppUnion\AppUnion_Mac.csproj">
      <Project>{0E0C80D7-8C78-4F0F-B8FC-320F5C97F3F6}</Project>
      <Name>AppUnion_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="AirDroidRSHost_UI.csproj">
      <Project>{0E0C80D7-8C78-4F0F-B8FC-320F5C97F3F9}</Project>
      <Name>AirDroidRSHost_UI</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="CLang.sh" />
  </ItemGroup>
</Project>