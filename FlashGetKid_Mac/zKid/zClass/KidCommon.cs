﻿using AppKit;
using iTong.Android;
using iTong.CoreFoundation;
using iTong.CoreModule;
using iTong.Device;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Threading;
using System.Windows.Forms;

namespace iTong.CoreModule
{
    public partial class kidMainForm
    {
        #region 变量

        /// <summary>配对码</summary>
        private string mPairingCode = string.Empty;

        private string mEmail = string.Empty;

        /// <summary>每次轮询等待时间</summary>
        private int mWait = 0;

        /// <summary>轮询次数</summary>
        private int mTimes = 1;

        /// <summary>配对码 返回参数</summary>
        private KidProfileInfo mKidProfileInfo = null;

        /// <summary>当前Panel</summary>
        private skPanel mCurrentPanel;

        /// <summary>前一个Panel</summary>
        private skPanel mPreviousPanel;

        private bool mCheckiTunesResult = false;

        private List<kidPopup> openedSubForms = new List<kidPopup>();

        /// <summary>进度条计时器</summary>
        private skTimer mProcessTimer = null;

        private List<iPhoneDevice> mConnectedDevicesLst = null;

        private Thread mThreadMonitor;

        #endregion 变量

        #region iTunes 相关（检查/安装）

        private void CheckiTunes()
        {
            this.mCheckiTunesResult = true;
        }

        private delegate void InstalliTunesDriveHandler();
        /// <summary>检查是否有安装iTunes</summary>
        private void InstalliTunesDriveHandle()
        {
            this.TimerStop();

            if (this.InvokeRequired)
            {
                this.Invoke(new InstalliTunesDriveHandler(InstalliTunesDriveHandle));
                return;
            }

            if (this.pnlDriveInstalled.Visible)
            {
                this.pnlDriveInstalled.Visible = false;
            }

            ThreadMgr.Start(() =>
            {
                this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.DriveInstall);
            });

            //using (kidDriveInstall frmDriveInstall = new kidDriveInstall(this.mActionHelper))
            //{
            //    if (frmDriveInstall.ShowDialog(this) == DialogResult.OK)
            //    {
            //        this.pnlDriveInstalled.Location = new CoreGraphics.CGPoint((this.Size.Width - this.pnlDriveInstalled.Width) / 2, 71);

            //        this.skBorderRadius = new skBorderRadius(2);

            //        this.pnlDriveInstalled.Visible = true;

            //        this.mTimer = TimerMgr.Create(2, InstallSuccessHandle, "InstallSuccessTimer", 1);
            //    }
            //}
        }

        private delegate void InstallSuccessHandler();
        /// <summary>iTunes安装完成</summary>
        private void InstallSuccessHandle()
        {
            this.TimerStop();

            if (this.InvokeRequired)
            {
                this.BeginInvoke(new InstallSuccessHandler(InstallSuccessHandle));
                return;
            }

            this.pnlDriveInstalled.Visible = false;

            iTunesHelper.CheckInit();

            this.StartListenHandle();
        }

        private Thread mThreadListen;
        private void StartListenHandle()
        {
            this.CheckiTunes();

            if (ThreadMgr.CheckThreadIsAlive(this.mThreadListen))
                return;

            this.mThreadListen = ThreadMgr.Start(new ThreadStart(() =>
            {
                this.StartListen();
                //if (iTunesHelper.LoadAppleDll() && iTunesHelper.StartAppleMobileDevice())
                //{
                //    this.StartListen();
                //}
            }));
        }

        #endregion iTunes 相关（检查/安装）

        #region 配对码 校验
        /// <summary>配对码</summary>
        private void CheckPairingCode()
        {
            using (kidPopup frmPopup = new kidPopup("PairingCode", this.mActionHelper))
            {
                this.openedSubForms.Add(frmPopup);

                if (frmPopup.ShowDialog(this) == DialogResult.Yes)
                {
                    this.mKidProfileInfo = frmPopup.KidProfileInfo;

                    this.mPairingCode = frmPopup.PairingCode;

                    this.mEmail = frmPopup.Email;

                    this.ConfirmAgain(frmPopup.Email); //匹配码正确 再次确认
                }
                else
                {
                    this.SelectFirstPage();
                }

                if (this.openedSubForms.Contains(frmPopup))
                    openedSubForms.Remove(frmPopup);
            }
        }

        /// <summary>再次确认</summary>
        private void ConfirmAgain(string email)
        {
            ThreadMgr.Start(() =>
            {
                this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.PairingCodeConfirm);
            });

            using (kidPopup frmConfirm = new kidPopup("Confirm", this.mActionHelper, this.mDevice.DeviceName, email, mPairingCode))
            {
                this.openedSubForms.Add(frmConfirm);

                if (frmConfirm.ShowDialog(this) == DialogResult.OK)
                {
                    this.mSelectDeviceMode = SelectDeviceMode.Enable;

                    this.DeviceConnectHandle();
                }
                else
                {
                    this.SelectFirstPage();
                }

                if (this.openedSubForms.Contains(frmConfirm))
                    openedSubForms.Remove(frmConfirm);
            }
        }

        #endregion 配对码 校验

        #region 初始化（Panel/设备列表）

        /// <summary>初始化 Panel</summary>
        /// <param name="panel"></param>
        /// <param name="isConnectFailed"></param>
        private void InitPanel(skPanel panel, bool isConnectFailed = false)
        {
            try
            {
                this.TimerStop();
                panel.Size = new CoreGraphics.CGSize(940, 540);
                panel.Location = new CoreGraphics.CGPoint(0, 0);

                if(this.mCurrentPanel!=null)
                    this.mCurrentPanel.Visible = false;

                panel.Visible = true;
                panel.BringToFront();

                if (panel == this.pnlDeviceNotDetected)
                    this.mPreviousPanel = this.mCurrentPanel;

                this.mCurrentPanel = panel;

                //this.cmsDevices.Visible = false;

                switch (panel.Name)
                {
                    case "pnlFirstGuide":
                        this.mSelectDeviceMode = SelectDeviceMode.Normal;

                        this.AbortMonitorThread();

                        this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.Main);
                        break;

                    case "pnlSelectSituation":
                        this.mSelectDeviceMode = SelectDeviceMode.Normal;

                        this.AbortMonitorThread();
                        if (this.mDevMgr != null && this.mDevMgr.ConnectedDevices.Count > 0)
                        {
                            if (this.mDevice != null && !this.mDevice.DeviceID.Equals(this.mDevMgr.ConnectedDevices[0].DeviceID))
                                this.mDevice = this.mDevMgr.ConnectedDevices[0];
                        }
                        break;

                    case "pnlCloseFindMyiPhoneTip":
                        this.SetCloseFindMyiPhoneRecheckLabelStyle();

                        this.btnTurnOffFindMyiPhone.skText = this.Language.GetString("Parental.Connect.turnofffindmyiphone.button1"); //Yes,turned off;

                        this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.CloseFindMyiPhone);
                        break;

                    case "pnlActivatingSupervisionMode":
                        this.mSelectDeviceMode = SelectDeviceMode.Activating;

                        this.progressActivatingSupervisionMode.skValue = 0;

                        this.lblActivatingSupervisionModeDeviceNameContent.skText = this.mDevice.DeviceName;
                        //this.lblActivatingSupervisionModeDeviceModelContent.skText = SummaryInfo.FormatProduct(this.mDicIOSModels[this.mDevice.ProductType].ToString());
                        this.lblActivatingSupervisionModeDeviceModelContent.skText = this.mDevice.ProductType;

                        this.ActivatingSupervisionModeUIHandle();

                        this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.ActivatingSupervisionMode);
                        break;

                    case "pnlDeactivatingSupervisionMode":
                        this.mSelectDeviceMode = SelectDeviceMode.Deactivating;

                        this.progressDeactivatingSupervisionMode.skValue = 0;

                        this.lblDeactivatingSupervisionModeDeviceNameContent.skText = this.mDevice.DeviceName;
                        //this.lblDeactivatingSupervisionModeDeviceModelContent.skText = SummaryInfo.FormatProduct(this.mDicIOSModels[this.mDevice.ProductType].ToString());
                        this.lblDeactivatingSupervisionModeDeviceModelContent.skText = this.mDevice.ProductType;

                        this.DeactivatingSupervisionModeUIHandle();

                        this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.DeactivatingSupervisionMode);
                        break;

                    case "pnlUsbConnectTip":
                        this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.UsbConnectTip);

                        this.AbortMonitorThread();
                        break;

                    case "pnlConnecting":
                        this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.UsbConnecting);
                        break;

                    case "pnlConnectingGuide":
                        this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.TrustComputer);
                        break;

                    case "pnlEnableConnectedSuccess":
                        this.mSelectDeviceMode = SelectDeviceMode.Enable;

                        //this.ShowConnectedDeviceMenuHandle(this.btnEnableConnectedSuccessDevice);

                        this.EnableConnectedSuccessSubUIHandle();

                        this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.EnableConnectedSuccess);
                        break;

                    case "pnlEnableSupervisionModeSuccess":
                        this.btnEnableSupervisionModeFinish.Visible = false;

                        this.mSelectDeviceMode = SelectDeviceMode.EnableSuccess;

                        this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.EnableSupervisionModeSuccess);
                        break;

                    case "pnlDisableConnectedSuccess":
                        this.mSelectDeviceMode = SelectDeviceMode.Disable;

                        //this.ShowConnectedDeviceMenuHandle(this.btnDisableConnectedSuccessDevice);

                        this.DisableConnectedSuccessUIHandle();

                        this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.DisableConnectedSuccess);
                        break;

                    case "pnlDisableSupervisionModeSuccess":
                        this.mSelectDeviceMode = SelectDeviceMode.DisableSuccess;

                        this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.DisableSupervisionModeSuccess);
                        break;

                    case "pnlConnectionFailed":
                        if (isConnectFailed)
                        {
                            this.lblConnectionFailedTitle.skText = this.Language.GetString("Common.ConnectFailed"); //Connection failed
                            this.lblConnectionFailedNote.skText = this.Language.GetString("Parental.Connect.Usb.fail.note"); //Note: \r\nPlease ensure your device remains connected during the process
                            this.btnConnectionFailedTip.skText = this.Language.GetString("Parental.Connect.Usb.buttion.nodevice"); //Device not detected?
                            this.btnConnectionFailedTip.Visible = true;
                        }
                        else
                        {
                            this.lblConnectionFailedTitle.skText = string.Format(this.Language.GetString("APC.RunTaskFail.Title"), this.mDevice == null ? "" : this.mDevice.DeviceName); //执行失败
                            this.lblConnectionFailedNote.skText = this.Language.GetString("APC.RunTaskFail.Description"); //注意:\r\n由于未知原因执行失败，请直接重试或重新连接设备后重新执行操作。
                            this.btnConnectionFailedTip.Visible = false; //执行失败不显示Device not detected?按钮
                        }

                        this.ConnectionFailedUIHandle();

                        this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.ConnectionFailed);
                        break;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "MainForm.InitPanel");
            }
        }

        private void AbortMonitorThread()
        {
            try
            {
                ThreadMgr.Abort(this.mThreadMonitor);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "KidCommon.AbortMonitorThread");
            }
        }

        private void ShowConnectedDeviceMenuHandle(skButton button)
        {
            button.skText = this.mDevice == null ? "" : this.mDevice.DeviceName;

            if (this.mDevMgr == null || this.mDevMgr != null && this.mDevMgr.ConnectedDevices.Count <= 1)
            {
                button.Visible = false;
            }
            else
            {
                List<string> lstDeviceId = new List<string>();
                this.mConnectedDevicesLst = new List<iPhoneDevice>();

                foreach (iPhoneDevice device in this.mDevMgr.ConnectedDevices)
                {
                    if (lstDeviceId.Contains(device.DeviceID) || !device.IsConnected)
                        continue;

                    lstDeviceId.Add(device.DeviceID);
                    this.mConnectedDevicesLst.Add(device);
                }

                if (lstDeviceId.Count <= 1)
                {
                    button.Visible = false;
                }
                else
                {
                    this.InitDeviceMenu(button);

                    button.Visible = true;
                }
            }
        }

        /// <summary>设置关闭我的手机Recheck Label样式</summary>
        /// <param name="isRecheck"></param>
        private void SetCloseFindMyiPhoneRecheckLabelStyle(bool isRecheck = false)
        {
            if (isRecheck)
            {
                this.lblCloseFindMyiPhoneRecheck.skTextColor = skColor.FromArgb(225, 62, 62);
                this.lblCloseFindMyiPhoneRecheck.skText = this.Language.GetString("Parental.Connect.turnofffindmyiphone.error1"); //Find My IPhone is still enabled on your device.Please turn off and try again.
            }
            else
            {
                this.lblCloseFindMyiPhoneRecheck.skTextColor = skColor.FromArgb(102, 107, 117);
                this.lblCloseFindMyiPhoneRecheck.skText = this.Language.GetString("enabling.supervision.mode.reopen.iphone"); //开启监督模式后您可以重新打开“查找我的iphone”
            }
        }

        /// <summary>初始化 设备列表</summary>
        /// <param name="btnCurrentDevice"></param>
        /// <param name="isShow"></param>
        private void InitDeviceMenu(skButton btnCurrentDevice, bool isShow = false)
        {
            this.cmsDevices.ClearItems();

            if (this.mConnectedDevicesLst == null || this.mConnectedDevicesLst.Count <= 1)
                return;

            foreach (iPhoneDevice device in this.mConnectedDevicesLst)
            {
                string deviceName = device.DeviceName;

                NSMenuItem nSMenuItem = new NSMenuItem(deviceName);

                if (deviceName == btnCurrentDevice.skText)
                    nSMenuItem.Image = MyResource.GetImage("ic_selected.png");

                this.cmsDevices.AddItem(nSMenuItem);
            }

            #region 测试
            //this.cmsDevices.Items.Add("**********************************************************************");
            //this.cmsDevices.Items.Add("1");
            //this.cmsDevices.Items.Add("2");
            //this.cmsDevices.Items.Add("3");
            //this.cmsDevices.Items.Add("4");
            //this.cmsDevices.Items.Add("5");
            //this.cmsDevices.Items.Add("6");
            //this.cmsDevices.Items.Add("7");
            //this.cmsDevices.Items.Add("8");
            //this.cmsDevices.Items.Add("9");
            //this.cmsDevices.Items.Add("10");
            #endregion 测试

            int itemsCount = this.cmsDevices.Items.Count;

            int y = btnCurrentDevice.Height;

            if (itemsCount > 4)
            {
                btnCurrentDevice.skIconMore = MyResource.GetImage("ic_arrow_up.png");
                //y = -(this.cmsDevices.skItemHeight * itemsCount + 5);
            }
            else
            {
                btnCurrentDevice.skIconMore = MyResource.GetImage("ic_arrow_down.png");
            }

            if (isShow)
            {
                this.cmsDevices.Show(btnCurrentDevice, new CoreGraphics.CGPoint(3, y));
            }
        }

        #endregion 初始化（Panel/设备列表）

        #region 设备连接相关

        private void DeviceConnectHandle()
        {
            if (this.mSelectDeviceMode == SelectDeviceMode.Normal)
            {
                this.DeviceConnectingHandle();
            }
            else if (this.mSelectDeviceMode == SelectDeviceMode.EnableSuccess || this.mSelectDeviceMode == SelectDeviceMode.DisableSuccess)
            {
                return;
            }
            else
            {
                this.InitPanel(this.pnlConnecting);

                this.TimerStop();
                this.mTimer = TimerMgr.Create(1, DeviceConnectingHandle, "DeviceConnectingTimer", 1);
            }

            if (this.mPreviousDevice != null && this.mDevice != null && !this.mPreviousDevice.DeviceID.Equals(this.mDevice.DeviceID) && this.openedSubForms != null && this.openedSubForms.Count > 1)
            {
                foreach (kidPopup subForm in this.openedSubForms)
                {
                    if (!subForm.Text.Equals("Confirm"))
                        continue;

                    foreach (Control ctr in subForm.Controls)
                    {
                        if (!ctr.Name.Equals("pnlConfirm"))
                            continue;

                        foreach (Control subCtr in ctr.Controls)
                        {
                            if (!subCtr.Name.Equals("lblConfirmTitle"))
                                continue;

                            skLabel lblConfirmTitle = subCtr as skLabel;
                            lblConfirmTitle.skText = string.Format(this.Language.GetString("Parental.Connect.code.title2"), this.mDevice.DeviceName);

                            break;
                        }

                        break;
                    }

                    break;
                }
            }
        }

        private delegate void DeviceConnectingHandler();
        private void DeviceConnectingHandle()
        {
            this.TimerStop();

            if (this.InvokeRequired)
            {
                this.Invoke(new DeviceConnectingHandler(DeviceConnectingHandle));
                return;
            }
            try
            {
                if (this.HaveNoDevice()) //没有连接设备
                {
                    this.CloseAllChildForms();

                    if (this.IsDeviceTrustError())
                    {
                        this.InitPanel(this.pnlConnectingGuide);
                        return;
                    }

                    this.SelectConnectGuide();

                }
                else
                {
                    if (this.mSelectDeviceMode == SelectDeviceMode.Normal)
                    {
                        this.InitPanel(this.pnlSelectSituation);
                    }
                    else
                    {
                        if (this.mDevice != null && (!this.mDevice.DeviceID.Equals(this.mDevMgr.ConnectedDevices[0].DeviceID) || !this.mDevice.IsConnected))
                        {
                            //有设备，但不是已进入开启/关闭监督模式的设备或设备未连接
                            this.InitPanel(this.pnlUsbConnectTip);
                        }

                        if (this.mDevice.IsConnected)
                        {
                            switch (this.mSelectDeviceMode)
                            {
                                case SelectDeviceMode.Enable:
                                case SelectDeviceMode.Activating:
                                    this.ConnectedSuccessTitleHandle(this.lblEnableConnectedSuccessTitle, this.mDevice.DeviceName);  //Device {0} has successfully connected.

                                    this.InitPanel(this.pnlEnableConnectedSuccess);
                                    break;

                                case SelectDeviceMode.Disable:
                                case SelectDeviceMode.Deactivating:
                                    this.ConnectedSuccessTitleHandle(this.lblDisableConnectedSuccessTitle, this.mDevice.DeviceName);  //Device {0} has successfully connected.

                                    this.InitPanel(this.pnlDisableConnectedSuccess);
                                    break;

                                //case SelectDeviceMode.Activating:
                                //    this.ActivateSuperviseStart();
                                //    break;

                                //case SelectDeviceMode.Deactivating:
                                //    this.DeactivateSuperviseStart();
                                //    break;

                                default:
                                    break;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "MainForm.DeviceConnectingHandle");
            }
        }

        /// <summary>关闭子窗体</summary>
        private void CloseAllChildForms()
        {
            if (this.openedSubForms == null || this.openedSubForms != null && this.openedSubForms.Count < 1)
                return;

            foreach (kidPopup openForm in openedSubForms)
            {
                openForm.Close();
            }
        }

        /// <summary>是否设备未信任</summary>
        /// <returns></returns>
        private bool IsDeviceTrustError()
        {
            bool result = false;

            if (this.mDevMgr != null && this.mDevMgr.ConnectedErrorDevices.Count > 0)
            {
                iPhoneDevice connectErrorDevice = this.mDevMgr.ConnectedErrorDevices[0];
                kAMDError kAMDError = connectErrorDevice.ConnectError;
                if (this.mDevice.DeviceID == connectErrorDevice.DeviceID && kAMDError == kAMDError.kAMDNotTrustError || kAMDError == kAMDError.kAMDTrustComputerError || kAMDError == kAMDError.kAMDPasswordProtectedError)
                    result = true;
            }
            return result;
        }

        /// <summary>选择首页</summary>
        private void SelectFirstPage()
        {
            if (this.HaveNoDevice())
            {
                if (this.IsDeviceTrustError())
                    this.InitPanel(this.pnlConnectingGuide);
                else
                    this.InitPanel(this.pnlFirstGuide);
            }
            else
            {
                this.InitPanel(this.pnlSelectSituation);
            }
        }

        /// <summary>是否没有设备</summary>
        /// <returns></returns>
        private bool HaveNoDevice()
        {
            bool result = false;

            if (this.mDevice == null || this.mDevMgr == null || this.mDevMgr != null && this.mDevMgr.ConnectedDevices.Count == 0 || this.mDevice != null && !this.mDevice.IsConnected)
                result = true;

            return result;
        }

        /// <summary>选择设备连接引导页</summary>
        private void SelectConnectGuide()
        {
            if (this.mSelectDeviceMode == SelectDeviceMode.Normal)
                this.InitPanel(this.pnlFirstGuide);
            else
                this.InitPanel(this.pnlUsbConnectTip);
        }

        #endregion 设备连接相关

        #region 开启监督模式

        /// <summary>开启监督模式</summary>
        private void EnableDeviceSupervisionMode()
        {
            this.CheckiTunes(); //检查驱动是否已安装

            if (!this.mCheckiTunesResult)
            {
                this.InstalliTunesDriveHandle();
                return;
            }

            ThreadMgr.Start(() =>
            {
                this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.EnableSupervisionMode);
            });

            this.CheckPairingCode();
        }

        /// <summary>开始 开启监督模式</summary>
        private void ActivateSuperviseStart()
        {
            this.CheckiTunes(); //检查驱动是否已安装

            if (!this.mCheckiTunesResult)
            {
                this.InstalliTunesDriveHandle();
                return;
            }

            this.InitPanel(this.pnlActivatingSupervisionMode);

            ThreadMgr.Start(() =>
            {
                this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.StartEnableSupervisionMode);
            });

            this.TimerStop();
            this.mTimer = TimerMgr.Create(1, ActivateSuperviseHandle, "ActivateSuperviseTimer", 3);
        }

        private delegate void ActivateSuperviseHandler();
        private void ActivateSuperviseHandle()
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new ActivateSuperviseHandler(ActivateSuperviseHandle));
                return;
            }

            this.progressActivatingSupervisionMode.skValue += 25;

            if (this.progressActivatingSupervisionMode.skValue == 75)
            {
                this.TimerStop();

                this.AbortMonitorThread();

                this.mThreadMonitor = ThreadMgr.Start(() =>
                {
                    this.Monitor(true);
                });
            }
        }

        #endregion 开启监督模式

        #region 关闭监督模式

        /// <summary>关闭监督模式</summary>
        private void DisableDeviceSupervisionMode()
        {
            this.CheckiTunes(); //检查驱动是否已安装

            if (!this.mCheckiTunesResult)
            {
                this.InstalliTunesDriveHandle();
                return;
            }

            this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.DisableSupervisionMode);

            this.mSelectDeviceMode = SelectDeviceMode.Disable;

            this.DeviceConnectHandle();
        }

        private void DeactivateSuperviseStart()
        {
            this.CheckiTunes(); //检查驱动是否已安装

            if (!this.mCheckiTunesResult)
            {
                this.InstalliTunesDriveHandle();
                return;
            }

            this.InitPanel(this.pnlDeactivatingSupervisionMode);

            ThreadMgr.Start(() =>
            {
                this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.StartDisableSupervisionMode);
            });

            ThreadMgr.Start(this.ExecuteMdmCheckout);
        }

        /// <summary>解除前，执行mdm checkout指令</summary>
        /// <returns></returns>
        private void ExecuteMdmCheckout()
        {
            ServerArgs<MdmCheckoutInfo> args = FGKidAPI.ExecuteMdmCheckout(this.mDevice.UniqueDeviceID);

            if (args != null && args.Code == 1 && args.Data != null)
            {
                MdmCheckoutInfo mdmCheckoutInfo = args.Data;

                mWait = mdmCheckoutInfo.wait;
                mTimes = mdmCheckoutInfo.times;

                this.TimerStop();
                this.mTimer = TimerMgr.Create(mWait, this.GetCheckoutResultHandle, "GetCheckoutResultTimer", mTimes);

                int totalWait = this.mWait;
                if (mTimes > 0)
                    totalWait = mWait * this.mTimes;

                if (totalWait > 0)
                {
                    this.mProcessTimer = TimerMgr.Create(1,
                        new ThreadStart(() =>
                        {
                            this.DeactivateSuperviseProcessHandle(Math.Floor((double)90 / totalWait));
                        }));
                }
                else
                {
                    this.mProcessTimer = TimerMgr.Create(1,
                        new ThreadStart(() =>
                        {
                            this.DeactivateSuperviseProcessHandle(10);
                        }));
                }
            }
            else
            {
                this.DeactivateSuperviseFailedHandle();
            }
        }

        private delegate void DeactivateSuperviseProcessHandler(double avgProgress);
        private void DeactivateSuperviseProcessHandle(double avgProgress)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new DeactivateSuperviseProcessHandler(DeactivateSuperviseProcessHandle), avgProgress);
                return;
            }

            this.progressDeactivatingSupervisionMode.skValue += avgProgress;
        }

        private delegate void GetCheckoutResultHandler();
        private void GetCheckoutResultHandle()
        {
            //if (this.InvokeRequired)
            //{
            //    this.Invoke(new GetCheckoutResultHandler(GetCheckoutResultHandle));
            //    return;
            //}

            ServerArgs<GetCheckoutResultInfo> args = FGKidAPI.GetCheckoutResult(this.mDevice.UniqueDeviceID);

            if (args != null && args.Code == 1 && args.Data != null && args.Data.checkout)
            {
                this.ProcessTimerStop();

                this.TimerStop();

                this.AbortMonitorThread();

                this.mThreadMonitor = ThreadMgr.Start(() =>
                {
                    this.Monitor(false);
                });
            }
            else
            {
                if (this.mTimer.CurrentLoop >= this.mTimer.MaxLoop)
                {
                    this.DeactivateSuperviseFailedHandle();
                }
            }
        }

        private delegate void DeactivateSuperviseFailedHandler();
        private void DeactivateSuperviseFailedHandle()
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new DeactivateSuperviseFailedHandler(DeactivateSuperviseFailedHandle));
                return;
            }

            this.ProcessTimerStop();

            this.TimerStop();

            this.InitPanel(this.pnlConnectionFailed);

            return;
        }

        #endregion 关闭监督模式

        /// <summary>重启应用程序</summary>
        private void RestartApplication()
        {
            Common.ProcessStart(Path.Combine(Folder.AppFolder, "Helper.exe"), "/s", Application.ExecutablePath);

            // 关闭当前进程
            Environment.Exit(0);
        }

        /// <summary>计时器停止</summary>
        private void TimerStop()
        {
            if (this.mTimer != null)
                this.mTimer.Dispose();
        }

        private void ProcessTimerStop()
        {
            if (this.mProcessTimer != null)
                this.mProcessTimer.Dispose();
        }

        private Dictionary<object, object> mDicIOSModels = null;
        /// <summary>获取ios模型</summary>
        private void GetIOSModels()
        {
            try
            {
                List<object> list = FGKidAPI.GetConfigByUrl(MyUrl.GetBatch, "get_ios_models");
                if (list != null && list.Count > 0)
                {
                    Dictionary<object, object> dicData = list[0] as Dictionary<object, object>;

                    mDicIOSModels = dicData["data"] as Dictionary<object, object>;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "MainForm.GetIOSModels");
            }
        }

        /// <summary>连接成功 页  title处理（设备名称过程 用...）</summary>
        /// <param name="label"></param>
        /// <param name="deviceName"></param>
        private void ConnectedSuccessTitleHandle(skLabel label, string deviceName)
        {
            bool isExtraLong = false;

            label.skText = string.Format(this.Language.GetString("Parental.Connect.Usb.success.title"), this.DeviceNameTruncation(deviceName, ref isExtraLong));

            if (isExtraLong)
                label.skToolTip = deviceName;
        }

        /// <summary>设备名称截断</summary>
        /// <param name="label"></param>
        /// <param name="sourceDeviceName"></param>
        /// <returns></returns>
        private string DeviceNameTruncation(string deviceName, ref bool isExtraLong)
        {
            isExtraLong = false;

            int titleLength = this.Language.GetString("Parental.Connect.Usb.success.title").Substring(3).Length;

            int usableLength = 53 - titleLength;

            int length = deviceName.Length;

            if (length > usableLength)
            {
                deviceName = string.Concat(deviceName.Substring(0, usableLength), "...");
                isExtraLong = true;
            }

            return deviceName;
        }

        public enum SelectDeviceMode
        /// <summary>设备模式</summary>
        {
            /// <summary>正常</summary>
            Normal,
            /// <summary>开启监督</summary>
            Enable,
            /// <summary>关闭监督</summary>
            Disable,
            /// <summary>正在开启监督</summary>
            Activating,
            /// <summary>正在关闭监督</summary>
            Deactivating,
            /// <summary>开启成功</summary>
            EnableSuccess,
            /// <summary>关闭成功</summary>
            DisableSuccess,
        }

        #region 上传日志到jira

        private skMsgInfoNew GetDefaultMsgInfo(string msgContent, string msgTitle, LanguageInterface language)
        {
            skMsgInfoNew skMsgInfo = new skMsgInfoNew();

            skMsgInfo.Buttons = MessageBoxButtons.OKCancel;

            skMsgInfo.FirstButton.skText = language.GetString("Common.Confirm");
            skMsgInfo.SecondButton.skText = language.GetString("Common.Cancel");

            skMsgInfo.FirstButton.Size = new CoreGraphics.CGSize(90, 28);
            skMsgInfo.FirstButton.skTextFont = MyFont.CreateFont("微软雅黑", 9.75f);
            skMsgInfo.FirstButton.skBackgroundImage = MyResource.GetImage("btn_4_sure");

            skMsgInfo.SecondButton.Size = new CoreGraphics.CGSize(90, 28);
            skMsgInfo.SecondButton.skTextFont = MyFont.CreateFont("微软雅黑", 9.75f);
            skMsgInfo.SecondButton.skTextColor = skColor.FromArgb(90, 94, 101);
            skMsgInfo.SecondButton.skBackgroundImage = MyResource.GetImage("btn_4_cancel");

            skMsgInfo.MessageInfo.skText = msgContent;
            skMsgInfo.MessageInfo.skTextFont = MyFont.CreateFont("微软雅黑", 9.75f);
            skMsgInfo.MessageInfo.skTextAlign = ContentAlignment.TopLeft;
            skMsgInfo.MessageInfo.skTextColor = skColor.FromArgb(102, 107, 117);
            skMsgInfo.MessageInfo.Padding = new Padding(0, 0, 0, 18);

            skMsgInfo.skTitleBackgroundColor = skColor.FromArgb(245, 246, 248);
            skMsgInfo.skTitleColor = skColor.FromArgb(45, 47, 51);
            skMsgInfo.skTitleFont = MyFont.CreateFont("微软雅黑", 9f);
            skMsgInfo.skTitleBarHeight = 30;
            skMsgInfo.skTitle = msgTitle;

            skMsgInfo.InputBoxInfo.Size = new CoreGraphics.CGSize(372, 38);
            skMsgInfo.InputBoxInfo.Padding = new Padding(2, 8, 2, 24);
            skMsgInfo.InputBoxInfo.IsPasswordMode = true;
            skMsgInfo.InputBoxInfo.PlaceHolder = language.GetString("Account_Password_Placeholder");
            skMsgInfo.InputBoxInfo.skBorderType = skBorderType.Round;
            skMsgInfo.InputBoxInfo.skBorderStrokeColor = skColor.FromArgb(234, 234, 234);
            skMsgInfo.InputBoxInfo.skBorderStrokeColorFocus = skColor.FromArgb(0, 98, 246);

            skMsgInfo.FormPadding = new Padding(24, 24, 24, 24);
            skMsgInfo.FormMinSize = new CoreGraphics.CGSize(420, 0);
            skMsgInfo.FormTranslateImage = MyResource.GetImage("frm_bg_shadow.png");
            skMsgInfo.FormIcon = MyResource.GetIcon("logo_all.ico");

            return skMsgInfo;
        }

        private void DoCreateLogZip(string strJiraId)
        {
            ThreadMgr.Start(new ThreadStart(() =>
            {
                string filePath = CreateLogZip();
                try
                {
                    if (filePath != null && File.Exists(filePath))
                    {
                        string strUrl = string.Format("http://**************:8090/rest/api/2/issue/{0}/attachments", strJiraId);

                        System.Net.WebClient client = new System.Net.WebClient();
                        client.Credentials = System.Net.CredentialCache.DefaultCredentials;
                        client.Headers.Add("Content-Type", "application/zip");
                        client.Headers.Add("Content-Type", "multipart/form-data");
                        client.Headers.Add("X-Atlassian-Token", "nocheck");
                        client.Headers.Add("Authorization", "Basic Ym90OnhtdGIuMjAyMA==");
                        client.UploadFile(new Uri(strUrl), "POST", filePath);
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "LogHelper_DoCreateLogZip");
                }
                finally
                {

                    if (filePath != null && File.Exists(filePath))
                    {
                        File.Delete(filePath);
                    }
                }
            }));
        }

        private static string CreateLogZip(bool openFolder = true)
        {
            List<string> listCopy = new List<string>();

            try
            {
                listCopy.AddRange(GetFileInRecenty(Folder.LogFolder));
                listCopy.AddRange(GetFileInRecenty(Folder.ExceptionFolder));

                string zipFolder = Folder.GetTempFolder();
                Folder.CheckFolder(zipFolder);

                foreach (string strFile in listCopy)
                {
                    try
                    {
                        File.Copy(strFile, Path.Combine(zipFolder, Path.GetFileName(strFile)));
                    }
                    catch
                    { }
                }
                string folder = Path.Combine(Folder.CacheFolder, "Devices", "Cache");
                Folder.CheckFolder(folder);

                string zipFile = Path.Combine(folder, (string.Format("Log_{0}.zip", DateTime.Now.ToString("yyyyMMdd_HHmmss"))));
                Utility.PackFiles(zipFile, zipFolder);
                if (openFolder)
                    Common.OpenExplorer(zipFile);
                return zipFile;
            }
            catch
            { }
            return null;
        }

        private static List<string> GetFileInRecenty(string dir)
        {
            List<string> listFile = new List<string>();

            if (Directory.Exists(dir))
            {
                string[] arrFile = Directory.GetFiles(dir);
                foreach (string strFile in arrFile)
                {
                    FileInfo info = new FileInfo(strFile);
                    if (DateTime.Now.Subtract(info.CreationTime).TotalDays < 3 || DateTime.Now.Subtract(info.LastWriteTime).TotalDays < 3)
                        listFile.Add(strFile);
                }
            }

            return listFile;
        }

        #endregion 上传日志到jira
    }
}
