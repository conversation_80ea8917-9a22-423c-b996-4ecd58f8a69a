﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO;
using System.Threading;
using System.Security.Cryptography;
using System.Drawing;
using System.Collections.Specialized;
using System.Xml;
using System.Diagnostics;
using System.Management;
using System.Globalization;
using System.Runtime.InteropServices;
using System.Windows.Forms;

using iTong.CoreModule;
using iTong.CoreFoundation;
using iTong.Device;
using iTong.Android;

using CoreFoundation;
using ObjCRuntime;
using CoreGraphics;
using AppKit;
using Foundation;
using Size = CoreGraphics.CGSize;
using Point = CoreGraphics.CGPoint;
using Rectangle = CoreGraphics.CGRect;
using Font = AppKit.NSFont;
using Color = AppKit.NSColor;
using Icon = AppKit.NSImage;
using Image = AppKit.NSImage;

using skContextMenuStrip = iTong.CoreModule.skMenu;
using ToolStripMenuItem = iTong.CoreModule.skMenuItem;
using ToolStripItem = iTong.CoreModule.skMenuItem;
using PictureBox = iTong.CoreModule.skPictureBox;
using Label = iTong.CoreModule.skLabel;
using NotifyIcon = iTong.CoreModule.skStatusItem;
using ContentAlignment = iTong.CoreModule.ContentAlignment;

namespace iTong.CoreModule
{
    public partial class kidMainForm
    {
        private static List<kidMainForm> mListMainForm = new List<kidMainForm>();

        private static Color mDefaultPanelskBorderStrokeColor = skColor.FromArgb(228, 230, 234);

        private Color mWhiteColor = Color.White;

        private static skBorderRadius mRightAngle = new skBorderRadius(0);

        private static skBorderType mPanelBorderType = skBorderType.Top;

        private string mCurrentLangDisplayName = string.Empty;

        private SelectDeviceMode mSelectDeviceMode = SelectDeviceMode.Normal;

        private skTimer mTimer = null;

        private tdActionHelper<tdActionItemForFGKid> mActionHelper = tdActionHelper<tdActionItemForFGKid>.Instance();

        public override int skTitleBarHeight => 0;

        public override int skStatusBarHeight => 40;

        public override skSplit skTransparentImageSplit => new skSplit(15);

        private frmBase frmSetting;//frmSetting
        private frmBase frmBinding;//frmBinding
        private frmBase frmPerssion;

        private skButton btnTitle;

        public bool IsConnected { get; set; }

        private NSImage mNormalImage;

        private NSImage mAlternateImage;

        private skMenuDelegate mDelegate;

        public kidMainForm()
        {
            InitializeComponent();

            this.InitFrame();

            mCurrentLangDisplayName = LanguageInterface.Instance().CurrentLanguage.LangDisplayName;

            //PageHelper.InitCef();

            FGKidAPI.Init();

            ThreadMgr.Start(this.GetIOSModels);
        }

        public override void OnShown(object sender, EventArgs e)
        {
            base.OnShown(sender, e);

            CreateCustomButton();
            ChargeHelperForCast.SendLastRunInfo();

            //发送日活数据
            TimerMgr.Create(3600, ChargeHelperForCast.SendLastRunInfo, callbackImediateliy: true);

            this.InitMenu();
        }

        public override void OnWindowWillClose(object sender, EventArgs e)
        {
            base.OnWindowWillClose(sender, e);
        }

        protected void InitFrame()
        {
            //TimerMgr.Create(30, new ThreadStart(() => {
            //    ServiceMgr.WakeUpDisplay();
            //}));

            Size sizeForm = new Size(940, 580);
            this.Size = sizeForm;

            this.skShowStatusBar = false;
            this.skShowButtonMax = false;
            this.skShowButtonMin = false;
            this.skShowButtonClose = false;

            this.ShowInTaskbar = false;

            this.skTitle = "FlashGet Kids Connector";
            this.skTitleFont = MyFont.CreateFont(9.75f);
            this.skTitleColor = skColor.FromArgb(45, 47, 51);
            this.skTitleBackgroundColor = Color.White;
            this.skBackgroundColor = Color.White;
            this.skUseHandCursor = false;

            if (this.btnTitle == null)
            {
                this.btnTitle = new skButton();
                this.AddSubview(this.btnTitle);
            }

            this.btnTitle.Size = new Size(this.Width, 40);
            this.btnTitle.Location = new Point(0, this.Height - this.btnTitle.Height);
            this.btnTitle.Anchor = AnchorStyles.Top | AnchorStyles.Right | AnchorStyles.Left;
            this.btnTitle.skTextAlign = NSTextAlignment.Center;
            this.btnTitle.skTextFont = MyFont.CreateFont(13);
            this.btnTitle.skTextColor = this.skTitleColor;
            this.btnTitle.skText = this.skTitle;
            this.btnTitle.skBackgroundColor = this.skTitleBackgroundColor;
            this.btnTitle.skSuperviewReceiveMessage = true;
            this.btnTitle.skUseHandCursor = false;
            this.btnTitle.skIcon = MyResource.GetImage("logo_48.png");
            this.btnTitle.skShowIcon = true;
            this.btnTitle.skIconSize = new Size(24,27);
        }

        /// <summary>初始化控件位置</summary>
        /// 要在赋值完文本之后再初始化位置，不然按照控件高度计算位置会不准确
        private void InitLocation()
        {
            foreach (var control in this.Controls)
            {
                if (control is skPanel pnl)
                    pnl.Location = new Point(0, this.Height - pnl.Top);
            }
        }

        public void InitMenu()
        {
            try
            {
                MyTest.Callback += OnTestCallback;
                MyTest.InitTestMenu(NSApplication.SharedApplication.MainMenu.ItemAt(0).Submenu);

                //NSMenu mainMenu = NSApplication.SharedApplication.MainMenu.ItemAt(0).Submenu;
                //NSMenuItem[] menuItems = mainMenu.Items;

                //LanguageInterface lang = LanguageInterface.Instance();

                //string appName = Common.GetApplicationName();
                //appName = "";
                //menuItems[0].Title = string.Format("{0} {1}", lang.GetStringTrim("Main.Menu.About"), appName);
                //menuItems[1].Title = lang.GetStringTrim("WeChat.Menu.CheckUpdate");// "检查更新";
                //menuItems[2].Title = lang.GetStringTrim("Main.Menu.SelectLang");
                //menuItems[3].Title = lang.GetStringTrim("Main.Menu.CreateError");
                //menuItems[4].Title = lang.GetStringTrim("Wechat.Message.Preference.Setting");


                //menuItems[6].Title = string.Format("{0} {1}", lang.GetStringTrim("WeChat.Menu.HideSelf"), appName);// "隐藏 " + Common.GetApplicationName();
                //menuItems[7].Title = lang.GetStringTrim("WeChat.Menu.HideOther");// "隐藏 其他";
                //menuItems[8].Title = lang.GetStringTrim("WeChat.Menu.ShowAll");// "显示全部";
                //menuItems[10].Title = string.Format("{0} {1}", lang.GetStringTrim("WeChat.Menu.Exit"), appName);

                ////mainMenu.RemoveItemAt(2);

                //menuItems[1].Activated += OnMenuItem_CheckUpdate;
                //menuItems[3].Activated += OnMenuItem_PrintLog;

                //skMenu menuSelectLang = new skMenu();
                //menuSelectLang.Opening += OnMenuItem_Opening;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "AppDelegate.InitMenu");
            }
        }

        protected override void InitControls()
        {
            try
            {
                base.InitControls();

                this.StartListenHandle();

                this.InitStyle();

                this.FormBorderStyle = FormBorderStyle.None;
                this.skBorderRadius = new skBorderRadius(0);
                this.skBorderWidth = 0;

                //this.Icon = MyResource.GetIcon("airdroid.ico");
                this.skIcon = MyResource.GetImage("logo_48.png");
                this.ShowIcon = true;
                this.skIconSize = Size.Empty;
                this.mIconPadding = new Padding(20, -2, 0, 0);
                this.skBackgroundColor = Color.White;

                mDelegate = new skMenuDelegate();
                mDelegate.Closed += this.OnMenuClosed;
                this.cmsBottom.Delegate = mDelegate;


                this.tsmiExit.Title = this.Language.GetString("common.exit");
                
                string strModify = string.Empty;
                if (MyLog.IsTestMode)
                {
                    strModify = string.Format(" ({0})", new FileInfo(Application.ExecutablePath).LastWriteTime.ToString("yyyy-MM-dd HH:mm"));
                    this.NotifyIcon.Title = "FlashGet Kids Connector - In Test mode";  //Airdroid Parental Connect;
                }
                else
                    this.NotifyIcon.Title = "FlashGet Kids Connector";  //Airdroid Parental Connect;

                //this.tmrStart.Start();

                this.InitLocation(); //有些位置要依靠控件高度去计算位置，所以在文本赋值之后再初始化控件位置

                //this.btnBinding.Padding = new Padding(10, 0, 10, 0);
                //this.btnBinding.skBadgeNumber = 3;
                //this.btnBinding.skShowIconMore = true;
                //this.btnBinding.skIconMoreAlign = skImageAlignment.Right;
                //this.btnBinding.skIconMore = MyResource.GetImage("ic_arrow.png");
                //this.btnBinding.skBadgeBackgroundColor = NSColor.Red;
                //this.btnBinding.skBadgeColor = NSColor.White;
                //this.btnBinding.skText = "通过生成 API 密钥，你可以为该密钥配置、认证和使用一个或多个 Apple 服务。密钥不会过期，但你无法在密钥生成后对其进行修改来访问更多服务。";
                //this.btnBinding.Size = new CGSize(this.btnBinding.Width, this.btnBinding.Height + 30);
                //this.btnBinding.skMultiLine = true;
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "MainForm.InitControls");
            }
        }

        private void InitStyle()
        {
            this.InitLauncher(this.Language.CurrentLanguage.LangName);

            this.Size = new Size(940, 580);
            //this.btnLanguageSetting.skAutoSize = true;
            //this.btnLanguageSetting.MinimumSize = new Size(75, 28);
            this.btnLanguageSetting.skBorderRadius = new skBorderRadius(12);

            this.btnLanguageSetting.skText = this.LanguageDisplayNameHandle(this.mCurrentLangDisplayName);
            this.btnLanguageSetting.Location = new Point(940 - 20 - this.btnLanguageSetting.Width, (40-this.btnLanguageSetting.Height)/2);

            this.btnFeedBack.Location = new Point(this.btnLanguageSetting.Location.X - 2 - this.btnFeedBack.Width, 9);

            this.pnlSelectSituation.skBorderType = mPanelBorderType;
            this.pnlFirstGuide.skBorderType = mPanelBorderType;
            this.pnlConnectingGuide.skBorderType = mPanelBorderType;

            this.pnlSelectSituation.skBorderRadius = mRightAngle;
            this.pnlFirstGuide.skBorderRadius = mRightAngle;
            this.pnlConnectingGuide.skBorderRadius = mRightAngle;

            this.pnlSelectSituation.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
            this.pnlFirstGuide.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
            this.pnlConnectingGuide.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;

            this.pnlSelectSituationEnable.skBorderRadius = new skBorderRadius(16);
            this.pnlSelectSituationDisable.skBorderRadius = new skBorderRadius(16);
            this.pnlSelectSituationDisableSub.skBorderRadius = new skBorderRadius(10);
            this.pnlSelectSituationEnableSub.skBorderRadius = new skBorderRadius(10);

            //this.lblFirstGuideTitle.skText = string.Concat("    ", this.Language.GetString("APC.FirstPage.Title")); //开始之前，请将设备通过 USB 与电脑进行连接
            //this.lblFirstGuideContent.skText = this.Language.GetString("APC.First.Description"); //AirDroid Parental Connector 能帮助您开启和关闭设备的监督模式，让您能更好的管控孩子的设备。
            //this.lblFirstGuideNote.skText = this.Language.GetString("Parental.Connect.Usb.noteforwin"); //Notice:1. Make sure you have unlocked the device screen.2.Please check the USB connection cable.
            //this.btnFirstGuideNoDevice.skText = this.Language.GetString("Parental.Connect.Usb.buttion.nodevice"); //Device not detected?
            //this.btnFirstGuideLearnMore.skText = string.Concat(this.Language.GetString("rs.ar.not.support.tip.link"), " >"); //Learn More

            //this.lblSelectSituationTitle.skText = this.Language.GetString("Parental.Connect.home.select"); //Please Select a Situation to Continue
            //this.lblSelectSituationEnableDescribe.skText = this.Language.GetString("Parental.Connect.home.open"); //Enable iOS device supervision mode
            //this.lblSelectSituationDisableDescribe.skText = this.Language.GetString("Parental.Connect.home.close"); //Disable iOS device supervision mode

            //this.lblEnableSupervisionModeContent.skText = this.Language.GetString("APC.EnableSupervision.Description"); //开启后即可体验完整模式的 AirDroid Parental Control 功能，帮助您更好地管控儿童设备。
            //this.lblDisableSupervisionModeContent.skText = this.Language.GetString("APC.DisableSupervision.Description"); //当您不再需要 AirDroid Parental Control 帮助您管理儿童设备时再关闭监督模式。

            this.lblSelectSituationEnableDescribe.Padding = new Padding(0, 0, 0, 5);
            this.lblSelectSituationDisableDescribe.Padding = new Padding(0, 0, 0, 5);

            if (this.HaveNoDevice())
                this.ShowFirstGuideHandle();
            else
                this.ShowSelectModeHandle();

            this.SelectFirstPage(); //选择首页

            if (this.mCurrentPanel == this.pnlConnectingGuide)
            {
                this.ShowSelectModeHandle();

                this.ShowFirstGuideHandle();
            }
            else
            {
                if (this.mCurrentPanel == this.pnlFirstGuide)
                    this.ShowSelectModeHandle();
                else
                    this.ShowFirstGuideHandle();
            }

            #region 边框类型

            this.pnlCloseFindMyiPhoneTip.skBorderType = mPanelBorderType;

            this.pnlUsbConnectTip.skBorderType = mPanelBorderType;
            this.pnlConnecting.skBorderType = mPanelBorderType;
            this.pnlConnectionFailed.skBorderType = mPanelBorderType;
            this.pnlDeviceNotDetected.skBorderType = mPanelBorderType;

            this.pnlEnableConnectedSuccess.skBorderType = mPanelBorderType;
            this.pnlActivatingSupervisionMode.skBorderType = mPanelBorderType;
            this.pnlEnableSupervisionModeSuccess.skBorderType = mPanelBorderType;

            this.pnlDisableConnectedSuccess.skBorderType = mPanelBorderType;
            this.pnlDeactivatingSupervisionMode.skBorderType = mPanelBorderType;
            this.pnlDisableSupervisionModeSuccess.skBorderType = mPanelBorderType;

            #endregion 边框类型

            #region 边框圆角

            this.pnlCloseFindMyiPhoneTip.skBorderRadius = mRightAngle;

            this.pnlUsbConnectTip.skBorderRadius = mRightAngle;
            this.pnlConnecting.skBorderRadius = mRightAngle;
            this.pnlConnectionFailed.skBorderRadius = mRightAngle;
            this.pnlDeviceNotDetected.skBorderRadius = mRightAngle;

            this.pnlEnableConnectedSuccess.skBorderRadius = mRightAngle;
            this.pnlActivatingSupervisionMode.skBorderRadius = mRightAngle;
            this.pnlEnableSupervisionModeSuccess.skBorderRadius = mRightAngle;

            this.pnlDisableConnectedSuccess.skBorderRadius = mRightAngle;
            this.pnlDeactivatingSupervisionMode.skBorderRadius = mRightAngle;
            this.pnlDisableSupervisionModeSuccess.skBorderRadius = mRightAngle;

            #endregion 边框圆角

            #region 边框颜色

            this.pnlCloseFindMyiPhoneTip.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;

            this.pnlUsbConnectTip.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
            this.pnlConnecting.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
            this.pnlConnectionFailed.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
            this.pnlDeviceNotDetected.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;

            this.pnlEnableConnectedSuccess.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
            this.pnlActivatingSupervisionMode.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
            this.pnlEnableSupervisionModeSuccess.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;

            this.pnlDisableConnectedSuccess.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
            this.pnlDeactivatingSupervisionMode.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
            this.pnlDisableSupervisionModeSuccess.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;

            #endregion 边框颜色

            //this.lblUsbConnectTipTitle.skText = this.Language.GetString("Parental.Connect.Usb.title"); //请将设备通过USB与电脑进行连接
            //this.lblUsbConnectTipDescribe.skText = this.Language.GetString("Parental.Connect.Usb.text1"); //Enable supervision mode to access the full functionality of AirDroid Parents, including device disabling and app restrictions, etc.
            //this.lblUsbConnectTipNotice.skText = this.Language.GetString("Parental.Connect.Usb.note"); //注意:\r\n1.确保你已经解锁了设备屏幕。\r\n2.请检查USB连接线
            //this.btnUsbConnectQuestion.skText = this.Language.GetString("Parental.Connect.Usb.buttion.nodevice"); //Device not detected?

            this.lblDeviceNotDetectedTip1.skBorderRadius = new skBorderRadius(12);
            this.lblDeviceNotDetectedTip2.skBorderRadius = new skBorderRadius(12);
            this.lblDeviceNotDetectedTip3.skBorderRadius = new skBorderRadius(12);

            //this.lblDeviceNotDetectedTitle.skText = this.Language.GetString("APC.NotDevice.Description"); //No device detected. Please:         this.Language.GetString("Parental.Connect.Usb.buttion.nodevice"); //Device not detected?
            ////this.lblDeviceNotDetectedDescribe.skText = this.Language.GetString("Parental.Connect.nodevice.tilte"); //Please check with following steps and try again.
            //this.lblDeviceNotDetectedMethod1.skText = this.Language.GetString("Parental.Connect.nodevice.text1"); //Unlock your device
            //this.lblDeviceNotDetectedMethod2.skText = this.Language.GetString("Parental.Connect.nodevice.text2"); //Select [Trust] from the screen of the device
            //this.lblDeviceNotDetectedMethod3.skText = this.Language.GetString("Parental.Connect.nodevice.text3"); //Please change another USB cable or USB port of computer

            //this.lblConnectingTitle.skText = this.Language.GetString("Parental.Connect.connectdevice.title"); //Connecting to the device... 
            //this.lblConnectingDescribe.skText = this.Language.GetString("Parental.Connect.connectdevice.text"); //Please keep the USB cable connection

            this.lblConnectingGuide1.skBorderRadius = new skBorderRadius(12);
            this.lblConnectingGuide2.skBorderRadius = new skBorderRadius(12);

            //this.lblConnectingGuideTitle.skText = this.Language.GetString("Parental.Connect.trusteddevice.title"); //Please select [Trust]on your device and complete the following steps to enter your device password.
            //this.lblConnectingGuideStep1.skText = this.Language.GetString("Parental.Connect.nodevice.text2");// Select [Trust] from the screen of the device
            //this.lblConnectingGuideStep2.skText = this.Language.GetString("Parental.Connect.nodevice.text1"); //Unlock your device

            //this.lblConnectionFailedTitle.skText = this.Language.GetString("Common.ConnectFailed"); //Connection failed
            //this.lblConnectionFailedNote.skText = this.Language.GetString("Parental.Connect.Usb.fail.note"); //Note: \r\nPlease ensure your device remains connected during the process
            //this.btnConnectionFailedTip.skText = this.Language.GetString("Parental.Connect.Usb.buttion.nodevice"); //Device not detected?

            this.btnConnectionFailedTryAgain.skBorderRadius = new skBorderRadius(18);
            //this.btnConnectionFailedTryAgain.skText = this.Language.GetString("Common.TryAgain.Button"); //Try again

            //this.lblEnableConnectedSuccessTitle.skText = string.Format(this.Language.GetString("Parental.Connect.Usb.success.title"), this.mDevice == null ? string.Empty : this.mDevice.DeviceName); //Device Emma’s iphone has successfully connected.
            //this.lblEnableConnectedSuccessDescribe.skText = this.Language.GetString("Parental.Connect.Usb.text1"); //Enable supervision mode to use the full functionality of AirDroid Parents, which supports device disabling and app restrictions, and more.
            //this.lblEnableConnectedSuccessNotice.skText = this.Language.GetString("Parental.Connect.Usb.note"); //Notice:1. Make sure you have unlocked the device screen.2.Please check the USB connection cable.

            //this.btnStartEnableSupervisionMode.skText = this.Language.GetString("Common.Start"); //Start
            this.btnStartEnableSupervisionMode.skBorderRadius = new skBorderRadius(18);

            //this.lblActivatingSupervisionModeTitle.skText = this.Language.GetString("Parental.Connect.supervisionmode.open"); //Activating supervision mode
            //this.lblActivatingSupervisionModeDeviceName.skText = this.Language.GetString("Parental.Connect.supervisionmode.devicename"); //Device name:
            //this.lblActivatingSupervisionModeDeviceModel.skText = this.Language.GetString("Parental.Connect.supervisionmode.devicemodel"); //Device model:
            //this.lblActivatingSupervisionModeTip.skText = this.Language.GetString("Parental.Connect.supervisionmode.note"); //The entire process will take about 1 minute, please be patient

            //this.lblStartSuccessfullyTitle.skText = this.Language.GetString("Parental.Connect.supervisionmode.text1"); //Congratulations, you have done everything
            //this.lblStartSuccessfullyDescribe.skText = this.Language.GetString("turn.off.app.Limits.setting"); //如果您之前正在使用iOS系统的【屏幕使用时间】，为了避免冲突请关闭【App限额】的限制

            //this.btnEnableSupervisionModeFinish.skText = this.Language.GetString("Button.Done"); //Finish
            this.btnEnableSupervisionModeFinish.skBorderRadius = new skBorderRadius(18);

            //this.lblDisableConnectedSuccessTitle.skText = this.Language.GetString("Parental.Connect.Usb.success.title"); // Device Emma’s iphone has successfully connected
            //this.lblDisableConnectedSuccessDescribe.skText = this.Language.GetString("Parental.Connect.Usb.text2"); //Removing AirDroid parental control mode and device monitoring
            //this.lblDisableConnectedSuccessNotice.skText = this.Language.GetString("Parental.Connect.Usb.note"); //Notice:1. Make sure you have unlocked the device screen.2.Please check the USB connection cable.

            //this.btnStartDisableSupervisionMode.skText = this.Language.GetString("Common.Start"); //Start
            this.btnStartDisableSupervisionMode.skBorderRadius = new skBorderRadius(18);

            //this.lblDeactivatingSupervisionModeTitle.skText = this.Language.GetString("Parental.Connect.supervisionmode.close"); //Deactivating supervision mode
            //this.lblDeactivatingSupervisionModeDeviceName.skText = this.Language.GetString("Parental.Connect.supervisionmode.devicename"); //Device name:
            //this.lblDeactivatingSupervisionModeDeviceModel.skText = this.Language.GetString("Parental.Connect.supervisionmode.devicemodel"); //Device model:
            //this.lblDeactivatingSupervisionModeTip.skText = this.Language.GetString("Parental.Connect.supervisionmode.note"); //The entire process will take about 1 minute, please be patient

            //this.lblCloseSuccessfullyDescribe.skText = this.Language.GetString("Parental.Connect.supervisionmode.text2"); //Congratulations! You have successfully disabled supervision mode.

            //this.btnDisableSupervisionModeFinish.skText = this.Language.GetString("Button.Done"); //Finish

            //this.lblDriveInstalled.skText = this.Language.GetString("Parental.InstallDrive.successful"); //驱动已安装完成

            ////关闭查找我的手机页面
            //this.lblCloseFindMyiPhoneTipTitle.skText = this.Language.GetString("Parental.Connect.turnofffindmyiphone.title");//Please Follow These Steps to Disable "Find My iPhone"
            //this.lblCloseFindMyiPhoneTipContent1.skText = this.Language.GetString("Parental.Connect.turnofffindmyiphone.step1"); //Go to "Setting > [Your Name] > Find My"
            //this.lblCloseFindMyiPhoneTipContent2.skText = this.Language.GetString("Parental.Connect.turnofffindmyiphone.step2"); //Select "Find My iPhone",and turn it off
            //this.lblCloseFindMyiPhoneTipContent3.skText = this.Language.GetString("Parental.Connect.turnofffindmyiphone.step3"); //Enter your Apple ID password and tap "Turn Off"
            //this.SetCloseFindMyiPhoneRecheckLabelStyle();
            //this.btnTurnOffFindMyiPhone.skText = this.Language.GetString("Parental.Connect.turnofffindmyiphone.button1"); //Yes,turned off

            this.btnCloseFindMyiPhoneTip.MaximumSize = new Size(218, 0);
            this.btnCloseFindMyiPhoneTip.skAutoSize = true;

            //this.btnCloseFindMyiPhoneTip.skText = this.Language.GetString("Parental.Connect.turnofffindmyiphone.tips");//无法关闭“查找我的设备”
            this.btnCloseFindMyiPhoneTip.Location = new Point(this.picCloseFindMyiPhoneTip3.Location.X + this.picCloseFindMyiPhoneTip3.Width / 2 - this.btnCloseFindMyiPhoneTip.Width / 2, this.lblCloseFindMyiPhoneTipContent3.Location.Y + this.lblCloseFindMyiPhoneTipContent3.Height);

            this.btnDisableSupervisionModeFinish.skBorderRadius = new skBorderRadius(18);

            this.btnLanguageSetting.skBorderRadius = new skBorderRadius(10);

            this.lblInTestMode.Anchor = AnchorStyles.Top | AnchorStyles.Right | AnchorStyles.Left;
            this.lblInTestMode.Size = new Size(100, this.lblInTestMode.Height);
            //this.lblInTestMode.Visible = false;

            this.lblVersion.Anchor = AnchorStyles.Bottom | AnchorStyles.Right | AnchorStyles.Left;
            this.lblVersion.Location = new Point(10, this.lblInTestMode.Bottom + 10);
            this.lblVersion.Size = new Size(this.Width - 20, this.lblVersion.Height);
            this.lblVersion.Visible = true;

            MyDpi.LayoutSubviewFromWin(this.pnlUsbConnectTip);
            MyDpi.LayoutSubviewFromWin(this.pnlDeviceNotDetected, this.DeviceNotDetectedUIHandle);
            MyDpi.LayoutSubviewFromWin(this.pnlConnecting);
            MyDpi.LayoutSubviewFromWin(this.pnlConnectingGuide);
            MyDpi.LayoutSubviewFromWin(this.pnlConnectionFailed, this.btnConnectionFailedBack);
            MyDpi.LayoutSubviewFromWin(this.pnlEnableConnectedSuccess, this.btnConnectedSuccessfullyBack);
            MyDpi.LayoutSubviewFromWin(this.pnlEnableConnectedSuccess, this.btnEnableConnectedSuccessDevice);
            MyDpi.LayoutSubviewFromWin(this.pnlEnableConnectedSuccess, this.picConnectedSuccessfullyImage);
            MyDpi.LayoutSubviewFromWin(this.pnlActivatingSupervisionMode);
            //MapWindowLayoutToMac.MapPanelLayout(this.pnlEnableSupervisionModeSuccess);
            MyDpi.LayoutSubviewFromWin(this.pnlDisableConnectedSuccess,this.btnCloseSuperviseBack);
            MyDpi.LayoutSubviewFromWin(this.pnlDisableConnectedSuccess, this.btnDisableConnectedSuccessDevice);
            MyDpi.LayoutSubviewFromWin(this.pnlDisableConnectedSuccess, this.picCloseSuperviseImage);
            MyDpi.LayoutSubviewFromWin(this.pnlDeactivatingSupervisionMode);
            MyDpi.LayoutSubviewFromWin(this.pnlDisableSupervisionModeSuccess);
            MyDpi.LayoutSubviewFromWin(this.pnlCloseFindMyiPhoneTip);
            MyDpi.LayoutSubviewFromWin(this,btnLanguageSetting);
            MyDpi.LayoutSubviewFromWin(this,btnFeedBack);
            btnLanguageSetting.BringToFront();
        }


        private void InitLauncher(string cultureInfo)
        {
            this.Language.LoadLang(cultureInfo);
            this.lblFirstGuideTitle.skText = string.Concat("    ", this.Language.GetString("APC.FirstPage.Title")); //开始之前，请将设备通过 USB 与电脑进行连接
            this.lblFirstGuideContent.skText = this.Language.GetString("APC.First.Description"); //AirDroid Parental Connector 能帮助您开启和关闭设备的监督模式，让您能更好的管控孩子的设备。
            this.lblFirstGuideNote.skText = this.Language.GetString("Parental.Connect.Usb.noteforwin"); //Notice:1. Make sure you have unlocked the device screen.2.Please check the USB connection cable.
            this.btnFirstGuideNoDevice.skText = this.Language.GetString("Parental.Connect.Usb.buttion.nodevice"); //Device not detected?
            this.btnFirstGuideLearnMore.skText = string.Concat(this.Language.GetString("rs.ar.not.support.tip.link"), " >"); //Learn More

            this.lblSelectSituationTitle.skText = this.Language.GetString("Parental.Connect.home.select"); //Please Select a Situation to Continue 启用/禁用完整模式
            this.lblSelectSituationEnableDescribe.skText = this.Language.GetString("Parental.Connect.home.open"); //Enable iOS device supervision mode
            this.lblSelectSituationDisableDescribe.skText = this.Language.GetString("Parental.Connect.home.close"); //Disable iOS device supervision mode


            this.lblEnableSupervisionModeContent.skText = this.Language.GetString("APC.EnableSupervision.Description"); //开启后即可体验完整模式的 AirDroid Parental Control 功能，帮助您更好地管控儿童设备。
            this.lblDisableSupervisionModeContent.skText = this.Language.GetString("APC.DisableSupervision.Description"); //当您不再需要 AirDroid Parental Control 帮助您管理儿童设备时再关闭监督模式。

            this.lblUsbConnectTipTitle.skText = this.Language.GetString("Parental.Connect.Usb.title"); //请将设备通过USB与电脑进行连接
            this.lblUsbConnectTipDescribe.skText = this.Language.GetString("Parental.Connect.Usb.text1"); //Enable supervision mode to access the full functionality of AirDroid Parents, including device disabling and app restrictions, etc.
            this.lblUsbConnectTipNotice.skText = this.Language.GetString("Parental.Connect.Usb.note"); //注意:\r\n1.确保你已经解锁了设备屏幕。\r\n2.请检查USB连接线
            this.btnUsbConnectQuestion.skText = this.Language.GetString("Parental.Connect.Usb.buttion.nodevice"); //Device not detected?

            this.lblDeviceNotDetectedTitle.skText = this.Language.GetString("APC.NotDevice.Description"); //No device detected. Please:         this.Language.GetString("Parental.Connect.Usb.buttion.nodevice"); //Device not detected?
            //this.lblDeviceNotDetectedDescribe.skText = this.Language.GetString("Parental.Connect.nodevice.tilte"); //Please check with following steps and try again.
            this.lblDeviceNotDetectedMethod1.skText = this.Language.GetString("Parental.Connect.nodevice.text1"); //Unlock your device
            this.lblDeviceNotDetectedMethod2.skText = this.Language.GetString("Parental.Connect.nodevice.text2"); //Select [Trust] from the screen of the device
            this.lblDeviceNotDetectedMethod3.skText = this.Language.GetString("Parental.Connect.nodevice.text3"); //Please change another USB cable or USB port of computer

            this.lblConnectingTitle.skText = this.Language.GetString("Parental.Connect.connectdevice.title"); //Connecting to the device... 
            this.lblConnectingDescribe.skText = this.Language.GetString("Parental.Connect.connectdevice.text"); //Please keep the USB cable connection

            this.lblConnectingGuideTitle.skText = this.Language.GetString("Parental.Connect.trusteddevice.title"); //Please select [Trust]on your device and complete the following steps to enter your device password.
            this.lblConnectingGuideStep1.skText = this.Language.GetString("Parental.Connect.nodevice.text2");// Select [Trust] from the screen of the device
            this.lblConnectingGuideStep2.skText = this.Language.GetString("Parental.Connect.nodevice.text1"); //Unlock your device

            this.lblConnectionFailedTitle.skText = this.Language.GetString("Common.ConnectFailed"); //Connection failed
            this.lblConnectionFailedNote.skText = this.Language.GetString("Parental.Connect.Usb.fail.note"); //Note: \r\nPlease ensure your device remains connected during the process
            this.btnConnectionFailedTip.skText = this.Language.GetString("Parental.Connect.Usb.buttion.nodevice"); //Device not detected?

            this.btnConnectionFailedTryAgain.skText = this.Language.GetString("Common.TryAgain.Button"); //Try again

            this.lblEnableConnectedSuccessTitle.skText = string.Format(this.Language.GetString("Parental.Connect.Usb.success.title"), this.mDevice == null ? string.Empty : this.mDevice.DeviceName); //Device Emma’s iphone has successfully connected.
            this.lblEnableConnectedSuccessDescribe.skText = this.Language.GetString("Parental.Connect.Usb.text1"); //Enable supervision mode to use the full functionality of AirDroid Parents, which supports device disabling and app restrictions, and more.
            this.lblEnableConnectedSuccessNotice.skText = this.Language.GetString("Parental.Connect.Usb.note"); //Notice:1. Make sure you have unlocked the device screen.2.Please check the USB connection cable.

            this.btnStartEnableSupervisionMode.skText = this.Language.GetString("Common.Start"); //Start

            this.lblActivatingSupervisionModeTitle.skText = this.Language.GetString("Parental.Connect.supervisionmode.open"); //Activating supervision mode
            this.lblActivatingSupervisionModeDeviceName.skText = this.Language.GetString("Parental.Connect.supervisionmode.devicename"); //Device name:
            this.lblActivatingSupervisionModeDeviceModel.skText = this.Language.GetString("Parental.Connect.supervisionmode.devicemodel"); //Device model:
            this.lblActivatingSupervisionModeTip.skText = this.Language.GetString("Parental.Connect.supervisionmode.note"); //The entire process will take about 1 minute, please be patient

            this.lblStartSuccessfullyTitle.skText = this.Language.GetString("Parental.Connect.supervisionmode.text1"); //Congratulations, you have done everything
            this.lblStartSuccessfullyDescribe.skText = this.Language.GetString("turn.off.app.Limits.setting"); //如果您之前正在使用iOS系统的【屏幕使用时间】，为了避免冲突请关闭【App限额】的限制

            this.btnEnableSupervisionModeFinish.skText = this.Language.GetString("Button.Done"); //Finish

            this.lblDisableConnectedSuccessTitle.skText = this.Language.GetString("Parental.Connect.Usb.success.title"); // Device Emma’s iphone has successfully connected
            this.lblDisableConnectedSuccessDescribe.skText = this.Language.GetString("Parental.Connect.Usb.text2"); //Removing AirDroid parental control mode and device monitoring
            this.lblDisableConnectedSuccessNotice.skText = this.Language.GetString("Parental.Connect.Usb.note"); //Notice:1. Make sure you have unlocked the device screen.2.Please check the USB connection cable.

            this.btnStartDisableSupervisionMode.skText = this.Language.GetString("Common.Start"); //Start

            this.lblDeactivatingSupervisionModeTitle.skText = this.Language.GetString("Parental.Connect.supervisionmode.close"); //Deactivating supervision mode
            this.lblDeactivatingSupervisionModeDeviceName.skText = this.Language.GetString("Parental.Connect.supervisionmode.devicename"); //Device name:
            this.lblDeactivatingSupervisionModeDeviceModel.skText = this.Language.GetString("Parental.Connect.supervisionmode.devicemodel"); //Device model:
            this.lblDeactivatingSupervisionModeTip.skText = this.Language.GetString("Parental.Connect.supervisionmode.note"); //The entire process will take about 1 minute, please be patient

            this.lblCloseSuccessfullyDescribe.skText = this.Language.GetString("Parental.Connect.supervisionmode.text2"); //Congratulations! You have successfully disabled supervision mode.

            this.btnDisableSupervisionModeFinish.skText = this.Language.GetString("Button.Done"); //Finish

            this.lblDriveInstalled.skText = this.Language.GetString("Parental.InstallDrive.successful"); //驱动已安装完成

            //关闭查找我的手机页面
            this.lblCloseFindMyiPhoneTipTitle.skText = this.Language.GetString("Parental.Connect.turnofffindmyiphone.title");//Please Follow These Steps to Disable "Find My iPhone"
            this.lblCloseFindMyiPhoneTipContent1.skText = this.Language.GetString("Parental.Connect.turnofffindmyiphone.step1"); //Go to "Setting > [Your Name] > Find My"
            this.lblCloseFindMyiPhoneTipContent2.skText = this.Language.GetString("Parental.Connect.turnofffindmyiphone.step2"); //Select "Find My iPhone",and turn it off
            this.lblCloseFindMyiPhoneTipContent3.skText = this.Language.GetString("Parental.Connect.turnofffindmyiphone.step3"); //Enter your Apple ID password and tap "Turn Off"
            this.SetCloseFindMyiPhoneRecheckLabelStyle();
            this.btnTurnOffFindMyiPhone.skText = this.Language.GetString("Parental.Connect.turnofffindmyiphone.button1"); //Yes,turned off
            this.btnCloseFindMyiPhoneTip.skText = this.Language.GetString("Parental.Connect.turnofffindmyiphone.tips");//无法关闭“查找我的设备”

            ShowFirstGuideHandle();
            ShowSelectModeHandle();

            this.ActivatingSupervisionModeUIHandle();
            this.ConnectionFailedUIHandle();
            this.EnableConnectedSuccessSubUIHandle();
            this.EnableSupervisionModeSuccessUIHandle();
            this.DisableConnectedSuccessUIHandle();
            this.DeactivatingSupervisionModeUIHandle();
            this.DeviceNotDetectedUIHandle();
        }


        public void CreateCustomButton()
        {
            // 创建关闭按钮
            skButton closeButton = new skButton();
            closeButton.skText = "";
            closeButton.Size = new Size(12, 12);
            closeButton.Location = new Point(12, this.Height - closeButton.Height-14);
            closeButton.skBackgroundImage = MyResource.GetImage("btn_nav_close_4.png");
            closeButton.skBackgroundImageState = skImageState.TwoState;
            closeButton.skBackgroundColor = skColor.Transparent;
            //closeButton.skBorderWidth = 1;
            //closeButton.skBorderType = skBorderType.Round;
            //closeButton.skBorderStrokeColor = skColor.FromArgb(255, 84, 77);
            //closeButton.skBorderRadius = new skBorderRadius(6);
            closeButton.Click += (o, s) => {

                NSApplication.SharedApplication.Terminate(NSApplication.SharedApplication);
            };
            this.AddSubview(closeButton);

            // 创建最小化按钮
            skButton minButton = new skButton();
            minButton.skText = "";
            minButton.Size = new Size(12, 12);
            minButton.Location = new Point(32, this.Height - minButton.Height - 14);
            minButton.skBackgroundImage = MyResource.GetImage("btn_nav_minimize_4.png");
            minButton.skBackgroundImageState = skImageState.TwoState;
            minButton.skBackgroundColor = skColor.Transparent;
            //minButton.skBorderWidth =1;
            //minButton.skBorderType = skBorderType.Round;
            //minButton.skBorderStrokeColor = skColor.FromArgb(254, 180, 41);
            //minButton.skBorderRadius =new skBorderRadius(6);
            minButton.Click += (o, s) =>
            {
                this.Window.PerformMiniaturize(null);
            };
            this.AddSubview(minButton);

            //标题栏必须带上，否则关闭按钮无法显示出来
            NSWindowStyle style = NSWindowStyle.Titled;

            style |= NSWindowStyle.Closable;

            style |= NSWindowStyle.Miniaturizable;

            this.Window.StyleMask = style;
        }

        private void NotifyIconImageView_Click(object sender, EventArgs e)
        {
            this.NotifyIconImageView.Image = this.mAlternateImage;
            this.NotifyIcon.DrawStatusBarBackground(new CGRect(0, 0, 18, 18), true);
            this.NotifyIcon.PopUpStatusItemMenu(this.cmsBottom);
        }

        private void NotifyIcon_MouseDoubleClick(object sender, EventArgs e)
        {
            this.Show();
        }

        private void tsmiExit_Click(object sender, EventArgs e)
        {

            this.Hide();

            this.ShowInTaskbar = false;

            this.NotifyIcon.Visible = false;

            OnExitApp();
        }

        private static void OnExitApp()
        {
            MyLog.WriteLine("OnExitApp -> Kill Process");

            ThreadMgr.Start(() => {
                Thread.Sleep(500);

                Process.GetCurrentProcess().Kill();
            });
        }

        private void OnMenuClosed(object sender, EventArgs e)
        {
            this.NotifyIconImageView.Image = this.mNormalImage;
            this.NotifyIcon.DrawStatusBarBackground(Bounds, false);
        }

        /// <summary>开启或关闭监督模式连接失败界面ui处理</summary>
        private void ConnectionFailedUIHandle()
        {
            if (this.btnConnectionFailedTip.Visible)
            {
                this.btnConnectionFailedTip.Location = new Point(0, 0);
                this.btnConnectionFailedTryAgain.Location = new Point(0, this.btnConnectionFailedTip.Bottom + 20);
            }
            else
            {
                this.btnConnectionFailedTryAgain.Location = new Point(0, 0);
            }
            this.lblConnectionFailedNote.Location = new Point(0, this.btnConnectionFailedTryAgain.Bottom + 40);
            this.lblConnectionFailedTitle.Location = new Point(0, this.lblConnectionFailedNote.Bottom + 14);

            this.pnlConnectionFailedSub.Size = new Size(500, this.lblConnectionFailedTitle.Bottom);
            this.pnlConnectionFailedSub.Location = new Point(this.pnlConnectionFailedSub.Location.X, (this.Height - this.btnTitle.Height - this.pnlConnectionFailedSub.Height) / 2);
        }

        /// <summary>开启监督模式连接成功界面ui处理</summary>
        private void EnableConnectedSuccessSubUIHandle()
        {
            this.btnStartEnableSupervisionMode.Location = new Point(0, 0);
            this.lblEnableConnectedSuccessNotice.Location = new Point(0, this.btnStartEnableSupervisionMode.Bottom + 41);
            this.lblEnableConnectedSuccessDescribe.Location = new Point(0, this.lblEnableConnectedSuccessNotice.Bottom + 23);
            this.lblEnableConnectedSuccessTitle.Location = new Point(0, this.lblEnableConnectedSuccessDescribe.Bottom + 23);

            this.pnlEnableConnectedSuccessSub.Size = new Size(500, this.lblEnableConnectedSuccessTitle.Bottom);
            this.pnlEnableConnectedSuccessSub.Location = new Point(this.pnlEnableConnectedSuccessSub.Location.X, (this.Height - this.btnTitle.Height - this.pnlEnableConnectedSuccessSub.Height) / 2);
        }

        /// <summary>关闭监督模式连接成功界面ui处理</summary>
        private void DisableConnectedSuccessUIHandle()
        {
            this.btnStartDisableSupervisionMode.Location = new Point(0, 0);
            this.lblDisableConnectedSuccessNotice.Location = new Point(0, this.btnStartDisableSupervisionMode.Bottom + 41);
            this.lblDisableConnectedSuccessDescribe.Location = new Point(0, this.lblDisableConnectedSuccessNotice.Bottom + 23);
            this.lblDisableConnectedSuccessTitle.Location = new Point(0, this.lblDisableConnectedSuccessDescribe.Bottom + 23);

            this.pnlDisableConnectedSuccessSub.Size = new Size(500, this.lblDisableConnectedSuccessTitle.Bottom);
            this.pnlDisableConnectedSuccessSub.Location = new Point(this.pnlDisableConnectedSuccessSub.Location.X, (this.Height - this.btnTitle.Height - this.pnlDisableConnectedSuccessSub.Height) / 2);
        }

        /// <summary>开启监督模式完成成功界面ui处理</summary>
        private void EnableSupervisionModeSuccessUIHandle()
        {
            this.btnEnableSupervisionModeFinish.Location = new Point(this.btnEnableSupervisionModeFinish.Location.X, 151);
            this.lblStartSuccessfullyDescribe.Location = new Point(this.lblStartSuccessfullyDescribe.Location.X, 212);
            this.lblStartSuccessfullyTitle.Location = new Point(this.lblStartSuccessfullyTitle.Location.X, 250);
            this.picStartSuccessfullyImage.Location = new Point(this.picStartSuccessfullyImage.Location.X, 299);
        }

        /// <summary>关闭监督模式完成成功界面ui处理</summary>
        private void DeactivatingSupervisionModeUIHandle()
        {
            this.btnDeactivatingSupervisionModeBack.Location = new Point(this.btnDeactivatingSupervisionModeBack.Location.X, 151);

            this.lblDeactivatingSupervisionModeDeviceName.skAutoSize = true;
            this.lblDeactivatingSupervisionModeDeviceModel.skAutoSize = true;
            this.lblDeactivatingSupervisionModeDeviceNameContent.skAutoSize = true;
            this.lblDeactivatingSupervisionModeDeviceModelContent.skAutoSize = true;
            int x = lblDeactivatingSupervisionModeDeviceName.Right;
            if (lblDeactivatingSupervisionModeDeviceModel.Right > x)
                x = lblDeactivatingSupervisionModeDeviceModel.Right;

            this.lblDeactivatingSupervisionModeDeviceNameContent.Location = new Point(x, this.lblDeactivatingSupervisionModeDeviceName.Location.Y);
            this.lblDeactivatingSupervisionModeDeviceModelContent.Location = new Point(x, this.lblDeactivatingSupervisionModeDeviceModel.Location.Y);
        }

        /// <summary>关闭监督模式完成成功界面ui处理</summary>
        private void DeviceNotDetectedUIHandle()
        {
            this.lblDeviceNotDetectedTitle.Location = new Point(this.lblDeviceNotDetectedTitle.Location.X, this.pnlDeviceNotDetected.Height - 50 - this.lblDeviceNotDetectedTitle.Height);
        }


        /// <summary>关闭监督模式完成成功界面ui处理</summary>
        private void ActivatingSupervisionModeUIHandle()
        {
            this.lblActivatingSupervisionModeDeviceName.skAutoSize = true;
            this.lblActivatingSupervisionModeDeviceModel.skAutoSize = true;
            this.lblActivatingSupervisionModeDeviceNameContent.skAutoSize = true;
            this.lblActivatingSupervisionModeDeviceModelContent.skAutoSize = true;
            int x = lblActivatingSupervisionModeDeviceName.Right;
            if (lblActivatingSupervisionModeDeviceModel.Right > x)
                x = lblActivatingSupervisionModeDeviceModel.Right;

            this.lblActivatingSupervisionModeDeviceNameContent.Location = new Point(x, this.lblActivatingSupervisionModeDeviceName.Location.Y);
            this.lblActivatingSupervisionModeDeviceModelContent.Location = new Point(x, this.lblActivatingSupervisionModeDeviceModel.Location.Y);
        }

        /// <summary>选择模式 显示处理</summary>
        private void ShowSelectModeHandle()
        {
            this.lblSelectSituationTitle.Location = new Point(this.lblSelectSituationTitle.Left, this.pnlSelectSituation.Height - 61 -this.lblSelectSituationTitle.Height);
            this.lblEnableSupervisionModeContent.Location = new Point(this.lblSelectSituationEnableDescribe.Location.X, this.lblSelectSituationEnableDescribe.Location.Y + this.lblSelectSituationEnableDescribe.Height);
            this.lblDisableSupervisionModeContent.Location = new Point(this.lblSelectSituationDisableDescribe.Location.X, this.lblSelectSituationDisableDescribe.Location.Y + this.lblSelectSituationDisableDescribe.Height);

            int enableSubPanelHeight = this.lblSelectSituationEnableDescribe.Height + this.lblEnableSupervisionModeContent.Height + 42;
            int disableSubPanelHeight = this.lblSelectSituationDisableDescribe.Height + this.lblDisableSupervisionModeContent.Height + 42;
            int subPanelHeight = enableSubPanelHeight >= disableSubPanelHeight ? enableSubPanelHeight : disableSubPanelHeight;

            this.pnlSelectSituationEnableSub.Size = new Size(312, subPanelHeight);
            this.pnlSelectSituationDisableSub.Size = new Size(312, subPanelHeight);

            int enablePanelHeight = this.picSelectSituationEnableImage.Height + this.pnlSelectSituationEnableSub.Height + 50;
            int disablePanelHeight = this.picSelectSituationDisableImage.Height + this.pnlSelectSituationDisableSub.Height + 50;
            int panelHeight = enablePanelHeight >= disablePanelHeight ? enablePanelHeight : disablePanelHeight;

            this.pnlSelectSituationEnable.Size = new Size(340, panelHeight);
            this.pnlSelectSituationDisable.Size = new Size(340, panelHeight);

            //this.lblSelectSituationTitle.Location = new Point(this.lblSelectSituationTitle.Location.X,
            //    (this.pnlSelectSituation.Height - (this.lblSelectSituationTitle.Height + 25 + this.pnlSelectSituationEnable.Height)) / 2);

            int selectLocationY = this.lblSelectSituationTitle.Top - 40;
            this.pnlSelectSituationEnable.Location = new Point(this.pnlSelectSituationEnable.Location.X, selectLocationY - this.pnlSelectSituationEnable.Height);
            this.picSelectSituationEnableImage.Location = new Point(this.picSelectSituationEnableImage.Left, this.pnlSelectSituationEnable.Height - 20 - this.picSelectSituationEnableImage.Height);
            this.pnlSelectSituationEnableSub.Location = new Point(this.pnlSelectSituationEnableSub.Left, this.picSelectSituationEnableImage.Top -12 - this.pnlSelectSituationEnableSub.Height);
            this.lblSelectSituationEnableDescribe.Location = new Point(this.lblSelectSituationEnableDescribe.Left, this.pnlSelectSituationEnableSub.Height - 15 - this.lblSelectSituationEnableDescribe.Height);
            this.lblEnableSupervisionModeContent.Location = new Point(this.lblEnableSupervisionModeContent.Left, this.lblSelectSituationEnableDescribe.Top - 13 - this.lblEnableSupervisionModeContent.Height);
            this.btnSelectSituationEnable.Location = new Point(this.btnSelectSituationEnable.Left, this.pnlSelectSituationEnableSub.Height - 15 - this.btnSelectSituationEnable.Height);

            this.pnlSelectSituationDisable.Location = new Point(this.pnlSelectSituationDisable.Location.X, selectLocationY - this.pnlSelectSituationDisable.Height);
            this.picSelectSituationDisableImage.Location = new Point(this.picSelectSituationDisableImage.Left, this.pnlSelectSituationDisable.Height - 20 - this.picSelectSituationDisableImage.Height);
            this.pnlSelectSituationDisableSub.Location = new Point(this.pnlSelectSituationDisableSub.Left, this.picSelectSituationDisableImage.Top - 12 - this.pnlSelectSituationDisableSub.Height);
            this.lblSelectSituationDisableDescribe.Location = new Point(this.lblSelectSituationDisableDescribe.Left, this.pnlSelectSituationDisableSub.Height - 15 - this.lblSelectSituationDisableDescribe.Height);
            this.lblDisableSupervisionModeContent.Location = new Point(this.lblDisableSupervisionModeContent.Left, this.lblSelectSituationDisableDescribe.Top - 13 - this.lblDisableSupervisionModeContent.Height);
            this.btnSelectSituationDisable.Location = new Point(this.btnSelectSituationDisable.Left, this.pnlSelectSituationDisableSub.Height - 15 - this.btnSelectSituationDisable.Height);

        }

        /// <summary></summary>
        private void ShowFirstGuideHandle()
        {
            int locationX = 82;
            int marginTopBottom = (this.pnlFirstGuide.Height - (this.lblFirstGuideTitle.Height + this.lblFirstGuideContent.Height + this.btnFirstGuideLearnMore.Height + this.lblFirstGuideNote.Height + this.btnFirstGuideNoDevice.Height + 80)) / 2;

            this.lblFirstGuideTitle.Location = new Point(locationX, this.pnlFirstGuide.Height - (marginTopBottom + this.lblFirstGuideTitle.Height));
            this.picFirstGuideTitle.Location = new Point(88, this.lblFirstGuideTitle.Bottom - this.picFirstGuideTitle.Height - 1);
            this.picFirstGuideTitle.BringToFront();

            this.lblFirstGuideContent.Location = new Point(locationX, this.lblFirstGuideTitle.Top - 16 - this.lblFirstGuideContent.Height);
            this.btnFirstGuideLearnMore.Location = new Point(locationX, this.lblFirstGuideContent.Top - 12 - this.btnFirstGuideLearnMore.Height);
            this.lblFirstGuideNote.Location = new Point(locationX, this.btnFirstGuideLearnMore.Top - 51 - this.lblFirstGuideNote.Height);

            this.btnFirstGuideNoDevice.Location = new Point(locationX, this.lblFirstGuideNote.Top - 19 - this.btnFirstGuideNoDevice.Height);
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);

            //MyForm.SetScreenCenter(this, true);

            //if (!this.mCheckiTunesResult)
            //    this.mTimer = TimerMgr.Create(0.5, this.InstalliTunesDriveHandle);

            //发送日活数据
            TimerMgr.Create(3600, ChargeHelperForCast.SendLastRunInfo, callbackImediateliy: true);
        }

        private void picDevice_DoubleClick(object sender, EventArgs e)
        {
            MyTest.SetDoubleClick();
        }

        public void OnTestCallback(object sender, TestArg e)
        {
            ///存在notshowtestmode.dll文件则不显示测试模式
            if (File.Exists(Path.Combine(Folder.AppFolder, "notshowtestmode.dll")))
                return;

            this.lblInTestMode.Visible = e.Visible;
            this.lblInTestMode.skText = e.Title;
            this.mStatusBarLableText = e.Title;          
        }

        private void SetControlEnabled(Control ctl, bool blnEnabled)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new ThreadStart(() => {
                    this.SetControlEnabled(ctl, blnEnabled);
                }));
            }
            else
            {
                ctl.Enabled = blnEnabled;
            }
        }

        private void SetControlEnabled(ToolStripMenuItem ctl, bool blnEnabled)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new ThreadStart(() => {
                    this.SetControlEnabled(ctl, blnEnabled);
                }));
            }
            else
            {
                ctl.Enabled = blnEnabled;
            }
        }

        /// <summary>首页——开启监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void pnlSelectSituationEnable_Click(object sender, MouseEventArgs e)
        {
            this.EnableDeviceSupervisionMode();
        }

        /// <summary>首页——开启监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void lblSelectSituationEnableDescribe_Click(object sender, EventArgs e)
        {
            //this.EnableDeviceSupervisionMode();
        }

        /// <summary>首页——开启监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void pnlSelectSituationEnableSub_Click(object sender, EventArgs e)
        {
            //this.EnableDeviceSupervisionMode();
        }

        /// <summary>首页——开启监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSelectSituationEnable_Click(object sender, EventArgs e)
        {
            //this.EnableDeviceSupervisionMode();
        }

        /// <summary>首页——开启监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void picSelectSituationEnableImage_Click(object sender, EventArgs e)
        {
            //this.EnableDeviceSupervisionMode();
        }

        /// <summary>连接引导页 返回按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnUsbConnectTipBack_Click(object sender, EventArgs e)
        {
            this.SelectFirstPage();
        }

        /// <summary>设备未信任提示页 返回按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnConnectingGuideBack_Click(object sender, EventArgs e)
        {
            this.SelectFirstPage();
        }

        /// <summary>没有设备？ 引导 按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDeviceNotDetectedBack_Click(object sender, EventArgs e)
        {
            if (this.mPreviousPanel != null)
                this.InitPanel(this.mPreviousPanel);
            else
                this.SelectConnectGuide();
        }

        /// <summary>关闭监督模式  完成按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDisableSupervisionModeFinish_Click(object sender, EventArgs e)
        {
            this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.DisableSupervisionModeFinish);

            this.mSelectDeviceMode = SelectDeviceMode.Normal;

            this.SelectFirstPage();
        }

        /// <summary>首页——关闭监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void picSelectSituationDisableImage_Click(object sender, EventArgs e)
        {
            //this.DisableDeviceSupervisionMode();
        }

        /// <summary>首页——关闭监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void pnlSelectSituationDisable_Click(object sender, EventArgs e)
        {
            this.DisableDeviceSupervisionMode();
        }

        /// <summary>首页——关闭监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void lblSelectSituationDisableDescribe_Click(object sender, EventArgs e)
        {
            //this.DisableDeviceSupervisionMode();
        }

        /// <summary>首页——关闭监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void pnlSelectSituationDisableSub_Click(object sender, EventArgs e)
        {
            //this.DisableDeviceSupervisionMode();
        }

        /// <summary>首页——关闭监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSelectSituationDisable_Click(object sender, EventArgs e)
        {
            //this.DisableDeviceSupervisionMode();
        }

        /// <summary>正在连接设备 返回按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnConnectingBack_Click(object sender, EventArgs e)
        {
            this.SelectFirstPage();
        }

        /// <summary>切换语种</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnLanguageSetting_Click(object sender, EventArgs e)
        {
            //语言界面
            this.cmsLanguage.ClearItems();

            string shortName;

            foreach (LangInfo info in this.Language.Languages)
            {
                shortName = this.LanguageDisplayNameHandle(info.LangDisplayName);

                NSMenuItem nSMenuItem = new NSMenuItem(shortName);

                if (info.LangDisplayName == LanguageInterface.Instance().CurrentLanguage.LangDisplayName)
                    nSMenuItem.Image = MyResource.GetImage("ic_selected.png");
                else
                    nSMenuItem.Image = MyResource.GetImage("ic_selected22.png");

                this.cmsLanguage.AddItem(nSMenuItem);
            }
            this.cmsLanguage.Show(this.btnLanguageSetting, new Point(0, this.btnLanguageSetting.Height));
        }

        /// <summary>选择语种</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void cmsLanguage_ItemClicked(object sender, EventArgs e)
        {
            NSMenuItem menuItem = sender as NSMenuItem;
            if (menuItem != null)
            {
                string selectLanguage = this.LanguageDisplayNameHandle(menuItem.Title.ToString()); //selectLanguage; 

                string currentLanguageDisplayName = this.LanguageDisplayNameHandle(this.mCurrentLangDisplayName);
                if (selectLanguage == currentLanguageDisplayName)
                    return;

                //this.btnLanguageSetting.skText = selectLanguage;
                //this.btnLanguageSetting.Location = new Point(940 - 20 - this.btnLanguageSetting.Width, this.btnLanguageSetting.Location.Y);

                //if (this.btnLanguageSetting.skText == currentLanguageDisplayName)
                //    return;

                //foreach (LangInfo info in this.Language.Languages)
                //{
                //    if (this.LanguageDisplayNameHandle(info.LangDisplayName) != this.btnLanguageSetting.Text)
                //        continue;

                //    this.mCurrentLangDisplayName = info.LangDisplayName;
                //    SettingMgr.SetValue(KeySession.Language, info.LangName);
                //    this.InitLauncher(info.LangName);

                //    bool isConnectFailed = false;
                //    if (this.mCurrentPanel.Name == "pnlConnectionFailed" && this.btnConnectionFailedTip.Visible == true)
                //    {
                //        isConnectFailed = true;
                //    }

                //    this.InitPanel(this.mCurrentPanel, isConnectFailed);
                //    if (this.mDevice != null)
                //    {
                //        this.ConnectedSuccessTitleHandle(this.lblEnableConnectedSuccessTitle, this.mDevice.DeviceName);  //Device {0} has successfully connected.
                //        this.ConnectedSuccessTitleHandle(this.lblDisableConnectedSuccessTitle, this.mDevice.DeviceName);  //Device {0} has successfully connected.
                //    }
                //}

                using (kidLanguageChange frmLanguageChange = new kidLanguageChange(selectLanguage))
                {
                    if (frmLanguageChange.ShowDialog(this) != DialogResult.OK)
                        return;

                    this.btnLanguageSetting.skText = selectLanguage;

                    if (this.btnLanguageSetting.skText == currentLanguageDisplayName)
                        return;

                    foreach (LangInfo info in this.Language.Languages)
                    {
                        if (this.LanguageDisplayNameHandle(info.LangDisplayName) != this.btnLanguageSetting.Text)
                            continue;

                        this.mCurrentLangDisplayName = info.LangDisplayName;
                        SettingMgr.SetValue(KeySession.Language, info.LangName);
                        this.InitLauncher(info.LangName);
                    }
                    AppRestarter.Restart();
                }
            }
        }

        /// <summary>开始 开启监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnStartEnableSupervisionMode_Click(object sender, EventArgs e)
        {
            if (this.mDevice == null)
            {
                this.SelectConnectGuide();
            }
            else
            {
                if (this.mDevice.FindMyPhone)
                    this.InitPanel(this.pnlCloseFindMyiPhoneTip);
                else
                    this.ActivateSuperviseStart();
            }
        }

        /// <summary>开始 关闭监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnStartDisableSupervisionMode_Click(object sender, EventArgs e)
        {
            //this.Monitor(false);
            //return;
            if (this.mDevice == null)
            {
                this.SelectConnectGuide();
            }
            else
            {
                if (this.mDevice.FindMyPhone)
                    this.InitPanel(this.pnlCloseFindMyiPhoneTip);
                else
                    this.DeactivateSuperviseStart();
            }
        }

        /// <summary>开启监督模式 完成按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnEnableSupervisionModeFinish_Click(object sender, EventArgs e)
        {
            this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.EnableSupervisionModeFinish);

            this.mSelectDeviceMode = SelectDeviceMode.Normal;

            this.SelectFirstPage();
        }

        /// <summary>开启监督模式 返回按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnActivatingSupervisionModeBack_Click(object sender, EventArgs e)
        {
            this.SelectFirstPage();
        }

        /// <summary>连接成功 返回按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnConnectedSuccessfullyBack_Click(object sender, EventArgs e)
        {
            this.SelectFirstPage();
        }

        /// <summary></summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCloseSuperviseBack_Click(object sender, EventArgs e)
        {
            this.SelectFirstPage();
        }

        /// <summary>关闭监督模式 返回按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDeactivatingSupervisionModeBack_Click(object sender, EventArgs e)
        {
            this.SelectFirstPage();
        }

        /// <summary>连接/执行失败 返回按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnConnectionFailedBack_Click(object sender, EventArgs e)
        {
            this.SelectFirstPage();
        }

        /// <summary>连接/执行失败 重试按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnConnectionFailedTryAgain_Click(object sender, EventArgs e)
        {
            ThreadMgr.Start(() => {
                this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.ConnectionRetry);
            });

            if (this.lblConnectionFailedTitle.skText == this.Language.GetString("Common.ConnectFailed"))
            {
                this.DeviceConnectHandle();
            }
            else
            {
                if (this.HaveNoDevice())
                {
                    this.InitPanel(this.pnlUsbConnectTip);
                }
                else
                {
                    if (this.mSelectDeviceMode == SelectDeviceMode.Activating)
                        this.ActivateSuperviseStart();
                    else
                        this.DeactivateSuperviseStart();
                }
            }
        }

        private string LanguageDisplayNameHandle(string langDisplayName)
        {
            string result = langDisplayName;

            if (!langDisplayName.Contains("中文") && langDisplayName.Contains("("))
                result = langDisplayName.Substring(0, langDisplayName.IndexOf('(')).ToString();

            return result;
        }

        private void pnlSelectSituationEnable_MouseEnter(object sender, EventArgs e)
        {
            this.pnlSelectSituationEnable.skMouseStateCustom = skMouseState.MouseHover;

            this.pnlSelectSituationEnable.Invalidate();
        }

        private void pnlSelectSituationDisable_MouseEnter(object sender, EventArgs e)
        {
            this.pnlSelectSituationDisable.skMouseStateCustom = skMouseState.MouseHover;

            this.pnlSelectSituationDisable.Invalidate();
        }

        private void pnlSelectSituation_MouseEnter(object sender, EventArgs e)
        {
            if (this.pnlSelectSituationEnable.skMouseStateCustom != skMouseState.MouseLeave)
            {
                this.pnlSelectSituationEnable.skMouseStateCustom = skMouseState.MouseLeave;
                this.pnlSelectSituationEnable.Invalidate();
            }

            if (this.pnlSelectSituationDisable.skMouseStateCustom != skMouseState.MouseLeave)
            {
                this.pnlSelectSituationDisable.skMouseStateCustom = skMouseState.MouseLeave;
                this.pnlSelectSituationDisable.Invalidate();
            }
        }

        private void lblSelectSituationTitle_DoubleClick(object sender, EventArgs e)
        {
            this.picDevice_DoubleClick(sender, e);
        }

        private void btnCloseFindMyiPhoneTip_Click(object sender, EventArgs e)
        {
            this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.UnableToCloseFindMyiPhone);

            Common.OpenUrl(string.Format("https://parental-control.flashget.com/{0}/how-to-fix-the-issue-of-not-being-able-to-turn-off-find-my-iphone", this.Language.CurrentLanguage.LangWithoutRegion));
        }

        private void btnCloseFindMyiPhoneBack_Click(object sender, EventArgs e)
        {
            //回到开启/关闭监督模式
            if (this.mSelectDeviceMode == SelectDeviceMode.Enable)
                this.InitPanel(this.pnlEnableConnectedSuccess);
            else
                this.InitPanel(this.pnlDisableConnectedSuccess);
        }

        /// <summary>问题反馈</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnFeedBack_Click(object sender, EventArgs e)
        {
            //Form form = MyForm.GetMainForm(typeof(frmFeedback).Name, false);

            //if (form == null)
            //{
            //    frmFeedback frmFeedback = new frmFeedback();
            //    frmFeedback.ShowDialog();
            //}
        }

        /// <summary></summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnUsbConnectQuestion_Click(object sender, EventArgs e)
        {
            this.InitPanel(this.pnlDeviceNotDetected);

            this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.UsbConnectNoDevice);
        }

        private void btnConnectionFailedTip_Click(object sender, EventArgs e)
        {
            this.InitPanel(this.pnlDeviceNotDetected);

            this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.ConnectedFailedNoDevice);
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            ////不响应DPI缩放
            //MyRegistry.SetDpiAwareness(DpiAwareness.unaware);

            ChargeHelperForCast.SetDailyActiveData("FlashGet Kids Connector");

            base.OnFormClosing(e);
        }

        private void btnUploadLog_Click(object sender, EventArgs e)
        {
            //skMsgInfoNew skMsgInfo = this.GetDefaultMsgInfo("请输入JIRA任务的完整Id，用于上传日志到JIRA上", "上传日志", this.Language);

            //skMsgInfo.InputBoxInfo.Visible = true;
            //skMsgInfo.InputBoxInfo.ButtonClear = new skMsgButton
            //{
            //    Visible = true,
            //    Size = new Size(16, 16),
            //    skIconState = skImageState.FourState,
            //    Padding = new Padding(0, 0, 4, 0),
            //    skIcon = MyResource.GetImage("btn_4_cancel.png")
            //};
            //skMsgInfo.MessageInfo.Padding = new Padding(0, 0, 0, 6);
            //skMsgInfo.InputBoxInfo.IsPasswordMode = false;

            //if (MsgBoxMgr.Show(this, skMsgInfo) != DialogResult.OK)
            //    return;

            //this.DoCreateLogZip(skMsgInfo.InputBoxInfo.skText);
        }

        private void cmsEnableConnectedSuccessDevices_ItemClicked(object sender, EventArgs e)
        {
            ToolStripItem menuItem = sender as ToolStripItem;
            if (menuItem != null)
            { 
                string selectDeviceName = menuItem.Text.ToString();

                foreach (iPhoneDevice device in this.mDevMgr.ConnectedDevices)
                {
                    if (selectDeviceName != device.DeviceName)
                    {
                        continue;
                    }
                    else
                    {
                        this.mDevice = device;
                        break;
                    }
                }

                if (this.mSelectDeviceMode == SelectDeviceMode.Enable)
                {
                    this.btnEnableConnectedSuccessDevice.skText = selectDeviceName;

                    this.ConnectedSuccessTitleHandle(this.lblEnableConnectedSuccessTitle, selectDeviceName);
                }
                else
                {
                    this.btnDisableConnectedSuccessDevice.skText = selectDeviceName;

                    this.ConnectedSuccessTitleHandle(this.lblDisableConnectedSuccessTitle, selectDeviceName);
                }
            }
        }

        private void btnDisableConnectedSuccessDevice_Click(object sender, EventArgs e)
        {
            //关闭监督模式 设备列表
            this.InitDeviceMenu(this.btnDisableConnectedSuccessDevice, true);
        }

        private void btnEnableConnectedSuccessDevice_Click(object sender, EventArgs e)
        {
            //开启监督模式 设备列表
            this.InitDeviceMenu(this.btnEnableConnectedSuccessDevice, true);
        }

        private void btnTurnOffFindMyiPhone_Click(object sender, EventArgs e)
        {
            if (this.btnTurnOffFindMyiPhone.skText.Equals(this.Language.GetString("Parental.Connect.turnofffindmyiphone.button1"))) //已关闭
                this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.TurnedOffFindMyiPhone);
            else
                this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.RecheckCloseFindMyiPhone);

            if (this.mDevice.FindMyPhone)
            {
                this.SetCloseFindMyiPhoneRecheckLabelStyle(true);

                this.btnTurnOffFindMyiPhone.skText = this.Language.GetString("Parental.Connect.turnofffindmyiphone.button2"); //"Recheck"; 

                this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.CloseFindMyiPhoneRecheck);
            }
            else
            {
                this.SetCloseFindMyiPhoneRecheckLabelStyle();

                this.btnTurnOffFindMyiPhone.skText = this.Language.GetString("Parental.Connect.turnofffindmyiphone.button1"); //Yes,turned off;

                //回到开启/关闭监督模式
                if (this.mSelectDeviceMode == SelectDeviceMode.Enable)
                    this.ActivateSuperviseStart();
                else
                    this.DeactivateSuperviseStart();
            }
        }

        private void btnFirstGuideLearnMore_Click(object sender, EventArgs e)
        {
            this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.FirstGuideLearnMore);

            Common.OpenUrl(string.Format(MyUrl.More, Language.CurrentLanguage.LangWithoutRegion));
        }

        private void btnFirstGuideNoDevice_Click(object sender, EventArgs e)
        {
            this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.FirstGuideNoDevice);

            this.InitPanel(this.pnlDeviceNotDetected);
        }

        private void lblEnableSupervisionModeContent_Click(object sender, EventArgs e)
        {
            this.EnableDeviceSupervisionMode();
        }

        private void lblDisableSupervisionModeContent_Click(object sender, EventArgs e)
        {
            this.DisableDeviceSupervisionMode();
        }
    }

    /// <summary>
    /// 重启
    /// </summary>
    public static class AppRestarter
    {
        // 检测当前是否在应用包内运行
        public static bool IsRunningFromBundle()
        {
            if (!RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
                return false;

            string path = AppDomain.CurrentDomain.BaseDirectory;
            return path.Contains(".app/Contents/");
        }

        // 获取应用包路径
        public static string GetAppBundlePath()
        {
            if (!IsRunningFromBundle())
            {
                MyLog.LogFile("Application is not running from bundle", "Restart");
                return "";
            }

            // 应用包路径结构: /Applications/MyApp.app/Contents/MacOS/
            string exePath = Process.GetCurrentProcess().MainModule.FileName;
            int bundleIndex = exePath.IndexOf(".app/Contents/MacOS/", StringComparison.Ordinal);

            if (bundleIndex == -1)
            {
                MyLog.LogFile("Invalid bundle structure", "Restart");
                return "";
            }

            return exePath.Substring(0, bundleIndex + ".app".Length);
        }

        // 重启应用
        public static void Restart()
        {
            if (!RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
            {
                MyLog.LogFile("This method is for macOS only","Restart");
                return;
            }

            // 准备重启脚本
            string restartScriptPath = CreateRestartScript();
            if (string.IsNullOrEmpty(null))
                return;

            // 启动重启进程
            ProcessStartInfo startInfo = new ProcessStartInfo
            {
                FileName = "/bin/bash",
                Arguments = $"-c \"chmod +x '{restartScriptPath}'; '{restartScriptPath}'\"",
                UseShellExecute = false,
                CreateNoWindow = true
            };
            
            Process.Start(startInfo);

            NSApplication.SharedApplication.Terminate(NSApplication.SharedApplication);
        }

        // 创建重启脚本
        private static string CreateRestartScript()
        {
            string scriptPath = Path.Combine(Path.GetTempPath(), $"restart_{Guid.NewGuid()}.sh");
            string appPath = GetAppBundlePath();

            if (string.IsNullOrEmpty(appPath))
                return null;

            // 创建Bash脚本内容
            string scriptContent = $@"#!/bin/bash
# 等待当前进程退出
sleep 0.5

# 启动新实例
open -n ""{appPath}""

# 删除临时脚本
rm -f ""{scriptPath}""
";

            File.WriteAllText(scriptPath, scriptContent);
            return scriptPath;
        }
    }

}
