﻿using System;
using System.Collections.Generic;

using WebKit;
using Foundation;
using ObjCRuntime;
using WVJBMessage = Foundation.NSDictionary;

namespace iTong.CoreModule
{
    public class WKWebViewJavascriptBridge : NSObject, IWKNavigationDelegate, WebViewJavascriptBridgeBaseDelegate
    {
        WKWebView _webView;
        WKNavigationDelegate _webViewDelegate;
        WebViewJavascriptBridgeBase _base;

        public static void enableLogging()
        {
            WebViewJavascriptBridgeBase.enableLogging();
        }

        public static WKWebViewJavascriptBridge bridgeForWebView(WKWebView webView)
        {
            WKWebViewJavascriptBridge bridge = new WKWebViewJavascriptBridge();

            bridge._setupInstance(webView);
            bridge.reset();

            return bridge;
        }

        private WKWebViewJavascriptBridge()
        {

        }

        ~WKWebViewJavascriptBridge()
        {
            try
            {
                if (_webView != null && _webView.NavigationDelegate != null)
                    _webView.NavigationDelegate = null;

                _base = null;
                _webView = null;
                _webViewDelegate = null;
            }
            catch (Exception ex)            
            {
                Console.WriteLine(ex.ToString());
            }
        }

        public void send(object objData, WVJBResponseCallback callback = null, string handlerName = "")
        {
            _base.sendData(objData, callback, handlerName);
        }

        public void callHandler(string handlerName, object objData = null, WVJBResponseCallback callback = null)
        {
            _base.sendData(objData, callback, handlerName);
        }

        public void registerHandler(string handlerName, WVJBHandler handler)
        {
            _base.messageHandlers[handlerName] = handler;
        }

        public void removeHandler(string handlerName)
        {
            if (_base.messageHandlers.ContainsKey(handlerName))
                _base.messageHandlers.Remove(handlerName);
        }

        public void reset()
        {
            _base.reset();
        }

        public void setWebViewDelegate(WKNavigationDelegate webViewDelegate)
        {
            _webViewDelegate = webViewDelegate;
        }

        public void disableJavscriptAlertBoxSafetyTimeout()
        {
            _base.disableJavscriptAlertBoxSafetyTimeout();
        }

        public void _setupInstance(WKWebView webView)
        {
            _webView = webView;

            if (_webView.NavigationDelegate != null)
            {
                if (_webView.NavigationDelegate is skWKNavigationDelegate)
                    _webViewDelegate = _webView.NavigationDelegate as skWKNavigationDelegate;
                else
                    _webViewDelegate = _webView.NavigationDelegate as WKNavigationDelegate;
            }

            _webView.NavigationDelegate = this;
            _base = new WebViewJavascriptBridgeBase();
            _base.BridgeDelegate = this;
        }

        public void WKFlushMessageQueue()
        {
            _webView.EvaluateJavaScript(_base.webViewJavascriptFetchQueyCommand(), (result, error) =>
             {
                 if (error != null)
                     Console.WriteLine(string.Format("WebViewJavascriptBridge: WARNING: Error when trying to fetch data from WKWebView: {0}", error.ToString()));

                 string strMessage = string.Empty;
                 if (result != null)
                     strMessage = result.ToString();

                 _base.flushMessageQueue(strMessage);
             });
        }

        [Export("webView:didFinishNavigation:")]
        public void DidFinishNavigation(WKWebView webView, WKNavigation navigation)
        {
            if (webView != _webView)
                return;

            if (_webViewDelegate != null)
                _webViewDelegate.DidFinishNavigation(webView, navigation);
        }

        [Export("webView:decidePolicyForNavigationResponse:decisionHandler:")]
        public void DecidePolicy(WKWebView webView, WKNavigationResponse navigationResponse, Action<WKNavigationResponsePolicy> decisionHandler)
        {
            if (webView != _webView)
                return;

            if (_webViewDelegate != null)
                _webViewDelegate.DecidePolicy(webView, navigationResponse, decisionHandler);
            else
                decisionHandler(WKNavigationResponsePolicy.Allow);
        }

        [Export("webView:didReceiveAuthenticationChallenge:completionHandler:")]
        public void DidReceiveAuthenticationChallenge(WKWebView webView, NSUrlAuthenticationChallenge challenge, Action<NSUrlSessionAuthChallengeDisposition, NSUrlCredential> completionHandler)
        {
            if (webView != _webView)
                return;

            if (_webViewDelegate != null)
                _webViewDelegate.DidReceiveAuthenticationChallenge(webView, challenge, completionHandler);
            else
                completionHandler(NSUrlSessionAuthChallengeDisposition.PerformDefaultHandling, null);
        }

        [Export("webView:decidePolicyForNavigationAction:decisionHandler:")]
        public void DecidePolicy(WKWebView webView, WKNavigationAction navigationAction, Action<WKNavigationActionPolicy> decisionHandler)
        {
            if (webView != _webView)
                return;

            NSUrl url = navigationAction.Request.Url;
            if (_base.isWebViewJavascriptBridgeURL(url))
            {
                if (_base.isBridgeLoadedURL(url))
                    _base.injectJavascriptFile();

                else if (_base.isQueueMessageURL(url))
                    this.WKFlushMessageQueue();

                else
                    _base.logUnkownMessage(url);

                decisionHandler(WKNavigationActionPolicy.Cancel);
                return;
            }

            if (_webViewDelegate != null)
                _webViewDelegate.DecidePolicy(webView, navigationAction, decisionHandler);
            else
                decisionHandler(WKNavigationActionPolicy.Allow);
        }

        [Export("webView:didStartProvisionalNavigation:")]
        public void DidStartProvisionalNavigation(WKWebView webView, WKNavigation navigation)
        {
            if (webView != _webView)
                return;

            if (_webViewDelegate != null)
                _webViewDelegate.DidStartProvisionalNavigation(webView, navigation);
        }

        [Export("webView:didFailNavigation:withError:")]
        public void DidFailNavigation(WKWebView webView, WKNavigation navigation, NSError error)
        {
            if (webView != _webView)
                return;

            if (_webViewDelegate != null)
                _webViewDelegate.DidFailNavigation(webView, navigation, error);
        }

        [Export("webView:didFailProvisionalNavigation:withError:")]
        public void DidFailProvisionalNavigation(WKWebView webView, WKNavigation navigation, NSError error)
        {
            if (webView != _webView)
                return;

            if (_webViewDelegate != null)
                _webViewDelegate.DidFailProvisionalNavigation(webView, navigation, error);
        }

        public string _evaluateJavascript(string javascriptCommand)
        {
            _webView.EvaluateJavaScript(javascriptCommand, null);

            return string.Empty;
        }
    }
}
