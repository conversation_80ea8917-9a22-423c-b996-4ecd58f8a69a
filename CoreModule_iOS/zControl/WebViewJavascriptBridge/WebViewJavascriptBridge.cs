﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;

#if MAC
using AppKit;
using UIWebView = WebKit.WebView;
using UIWebViewDelegate = WebKit.WebPolicyDelegate;
using IUIWebViewDelegate = WebKit.IWebPolicyDelegate;
#else
using UIKit;
#endif

using WebKit;
using Foundation;
using ObjCRuntime;
using WVJBMessage = Foundation.NSDictionary;

namespace iTong.CoreModule
{
    public class WebViewJavascriptBridge : NSObject, IUIWebViewDelegate, WebViewJavascriptBridgeBaseDelegate
    {
        UIWebView _webView;
        UIWebViewDelegate _webViewDelegate;
        WebViewJavascriptBridgeBase _base;

        public static void enableLogging()
        {
            WebViewJavascriptBridgeBase.enableLogging();
        }

        public static WebViewJavascriptBridge bridgeForWebView(UIWebView webView)
        {
            WebViewJavascriptBridge bridge = new WebViewJavascriptBridge();
            bridge._platformSpecificSetup(webView);

            return bridge;
        }

        private WebViewJavascriptBridge()
        {

        }

        ~WebViewJavascriptBridge()
        {
#if MAC
            if (_webView != null && _webView.PolicyDelegate != null)
                _webView.PolicyDelegate = null;
#else
            if (_webView != null && _webView.Delegate != null)
                _webView.Delegate = null;
#endif

            _base = null;
            _webView = null;
            _webViewDelegate = null;
        }


        public void send(object objData, WVJBResponseCallback callback = null, string handlerName = "")
        {
            _base.sendData(objData, callback, handlerName);
        }

        public void callHandler(string handlerName, object objData = null, WVJBResponseCallback callback = null)
        {
            _base.sendData(objData, callback, handlerName);
        }

        public void registerHandler(string handlerName, WVJBHandler handler)
        {
            _base.messageHandlers[handlerName] = handler;
        }

        public void removeHandler(string handlerName)
        {
            if (_base.messageHandlers.ContainsKey(handlerName))
                _base.messageHandlers.Remove(handlerName);
        }

        public void reset()
        {
            _base.reset();
        }

        public void setWebViewDelegate(UIWebViewDelegate webViewDelegate)
        {
            _webViewDelegate = webViewDelegate;
        }

        public void disableJavscriptAlertBoxSafetyTimeout()
        {
            _base.disableJavscriptAlertBoxSafetyTimeout();
        }



        public void _platformSpecificSetup(UIWebView webView)
        {
            _webView = webView;
#if MAC
            _webView.PolicyDelegate = this;
#else
            _webView.Delegate = this;
#endif
            _base = new WebViewJavascriptBridgeBase();
            _base.BridgeDelegate = this;
        }

        public string _evaluateJavascript(string javascriptCommand)
        {
#if MAC
            _webView.StringByEvaluatingJavaScriptFromString(javascriptCommand);
#else
            _webView.EvaluateJavascript(javascriptCommand);
#endif         

            return string.Empty;
        }

#if MAC

        //10.14 No Support this API
        //[Export("webView:decidePolicyForNavigationAction:request:frame:decisionListener:"), CompilerGenerated]
        //public virtual void DecidePolicyForNavigation(WebView webView, NSDictionary actionInformation, NSUrlRequest request, WebFrame frame, WebPolicyDecisionListener decisionToken)
        //{
        //    if (webView != _webView)
        //        return;

        //    NSUrl url = request.Url;
        //    if (_base.isWebViewJavascriptBridgeURL(url))
        //    {
        //        if (_base.isBridgeLoadedURL(url))
        //        {
        //            _base.injectJavascriptFile();
        //        }
        //        else if (_base.isQueueMessageURL(url))
        //        {
        //            string messageQueueString = this._evaluateJavascript(_base.webViewJavascriptFetchQueyCommand());
        //            _base.flushMessageQueue(messageQueueString);
        //        }
        //        else
        //        {
        //            _base.logUnkownMessage(url);
        //        }

        //        decisionToken.Ignore();
        //    }
        //    else if (_webViewDelegate != null)
        //    {
        //        _webViewDelegate.DecidePolicyForNavigation(webView, actionInformation, request, frame, decisionToken);
        //    }
        //    else
        //    {
        //        decisionToken.Use();
        //    }
        //}

#else
        [Export("webViewDidFinishLoad:")]
        public virtual void LoadingFinished(UIWebView webView)
        {
            if (webView != _webView)
                return;

            if (_webViewDelegate != null)
                _webViewDelegate.LoadingFinished(webView);
        }

        [Export("webView:didFailLoadWithError:")]
        public virtual void LoadFailed(UIWebView webView, NSError error)
        {
            if (webView != _webView)
                return;

            if (_webViewDelegate != null)
                _webViewDelegate.LoadFailed(webView, error);
        }

        [Export("webView:shouldStartLoadWithRequest:navigationType:")]
        public virtual bool ShouldStartLoad(UIWebView webView, NSUrlRequest request, UIWebViewNavigationType navigationType)
        {
            if (webView != _webView)
                return true;

            NSUrl url = request.Url;
            if (_base.isWebViewJavascriptBridgeURL(url))
            {
                if (_base.isBridgeLoadedURL(url))
                {
                    _base.injectJavascriptFile();
                }
                else if (_base.isQueueMessageURL(url))
                {
                    string messageQueueString = this._evaluateJavascript(_base.webViewJavascriptFetchQueyCommand());
                    _base.flushMessageQueue(messageQueueString);
                }
                else
                {
                    _base.logUnkownMessage(url);
                }

                return false;
            }

            if (_webViewDelegate != null)
                return _webViewDelegate.ShouldStartLoad(webView, request, navigationType);
            else
                return true;
        }

        [Export("webViewDidStartLoad:")]
        public virtual void LoadStarted(UIWebView webView)
        {
            if (webView != _webView)
                return;

            if (_webViewDelegate != null)
                _webViewDelegate.LoadStarted(webView);
        }

#endif

    }
}
