﻿using System;
using System.IO;
using System.Text;
using System.Collections.Generic;

using AppKit;
using Foundation;
using CoreGraphics;
using ImageKit;

using iTong.CoreFoundation;
using ObjCRuntime;

namespace iTong.CoreModule
{
    [Register("skCollectionViewDataSource")]
    public class skCollectionViewDataSource : NSCollectionViewDataSource
    {
        #region Constructors

        // Called when created from unmanaged code
        public skCollectionViewDataSource(IntPtr handle) : base(handle)
        {
            Initialize();
        }

        public skCollectionViewDataSource(NSObjectFlag t) : base(t)
        {
            Initialize();
        }

        [Export("init")]
        public skCollectionViewDataSource() : base()
        {
            this.Initialize();
        }

        private void Initialize()
        {

        }

        #endregion

        private skCollectionView mCollectionView;

        public skCollectionViewDataSource(skCollectionView collectionView) : base()
        {
            this.Initialize();

            this.mCollectionView = collectionView;
        }

        //[Export("collectionView:itemForRepresentedObjectAtIndexPath:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null)]
        public override NSCollectionViewItem GetItem(NSCollectionView collectionView, NSIndexPath indexPath)
        {
#if LOG
            Console.WriteLine("collectionView:itemForRepresentedObjectAtIndexPath:" + indexPath.ToString());
#endif

            NSCollectionViewItem item = null;

            if (this.mCollectionView != null)
            {
                item = this.mCollectionView.MakeItem(this.mCollectionView.CollectionViewItem, indexPath);
                skCollectionViewItem itemNew = item as skCollectionViewItem;
                if (item == null)
                    goto DoExit;

                skButton btn = itemNew.View;
                if (btn == null)
                    goto DoExit;

                this.mCollectionView.OnItemLoad(btn, new skCollectionViewArgs() { IndexPath = indexPath, Item = itemNew });

                //item.ImageView = new NSImageView();
                //item.TextField = new NSTextField();

                //item.ImageView.Image = imageData.ImageValue;
                //item.TextField.Cell.Title = imageData.ImageTitle;
                //item.Selected = imageData.ImageSelected;


            }
            else if (collectionView != null)
            {
                item = collectionView.MakeItem("NSCollectionViewItem", indexPath);
            }

        DoExit:
            return item;
        }

        //[Export("collectionView:numberOfItemsInSection:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null)]
        public override nint GetNumberofItems(NSCollectionView collectionView, nint section)
        {
            //return 0;

            int count = 0;

            if (this.mCollectionView != null)
                count = this.mCollectionView.GetGroupItemCount((int)section);

#if LOG
            Console.WriteLine("collectionView:numberOfItemsInSection:" + section.ToString() + "\tCount:" + count.ToString());
#endif

            return count;
        }

        //[Export("numberOfSectionsInCollectionView:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null)]
        public override nint GetNumberOfSections(NSCollectionView collectionView)
        {
            int count = 0;

            if (this.mCollectionView != null)
                count = this.mCollectionView.GetGroupCount();

#if LOG
            Console.WriteLine("numberOfSectionsInCollectionView:" + count.ToString());
#endif

            return count;
        }

        //[Export("collectionView:viewForSupplementaryElementOfKind:atIndexPath:")]
        public override NSView GetView(NSCollectionView collectionView, NSString kind, NSIndexPath indexPath)
        {
#if LOG
            Console.WriteLine("collectionView:viewForSupplementaryElementOfKind:atIndexPath:" + kind.ToString() + "\t" + indexPath.ToString());
#endif

            NSView item = null;

            if (this.mCollectionView != null)
            {
                bool isHeaderView = (kind == NSCollectionElementKind.SectionHeader);

                if (isHeaderView)
                {
                    //this.mCollectionView.RegisterClassForSupplementaryView(this.mCollectionView.ElementType, NSCollectionElementKind.SectionHeader, "SectionHeader");                    
                    item = this.mCollectionView.MakeSupplementaryView(kind, this.mCollectionView.SectionHeader, indexPath);
                }
                else
                {
                    //this.mCollectionView.RegisterClassForSupplementaryView(this.mCollectionView.ElementType, NSCollectionElementKind.SectionFooter, "SectionFooter");
                    item = this.mCollectionView.MakeSupplementaryView(kind, this.mCollectionView.SectionFooter, indexPath);
                }

                skImageData imageData = this.mCollectionView.GetGroup((int)indexPath.Section);
                if (imageData == null)
                    goto DoExit;

                skButton btn = item as skButton;
                if (btn == null)
                    goto DoExit;

                btn.SuspendLayout();
                btn.Name = imageData.GroupKey;
                btn.skTag = imageData;
                btn.skBadgeNumber = imageData.GroupCount;
                btn.skTrianglePosition = (imageData.GroupShowItems ? skTrianglePosition.Top : skTrianglePosition.Right);

                if (!btn.skHasInit)
                {
                    btn.skHasInit = true;
                    btn.skShowIconMore = true;
                    btn.skIconMore = null;
                    btn.skIconMoreSize = new CGSize(16, 16);
                    btn.skUseHandCursor = false;

                    btn.Click += (sender, e) => {
                        imageData.GroupShowItems = !imageData.GroupShowItems;
                        btn.skTrianglePosition = (imageData.GroupShowItems ? skTrianglePosition.Top : skTrianglePosition.Right);
                    };
                }

                if (isHeaderView)
                {
                    btn.skBackgroundColor = imageData.GroupHeaderBackgroundColor;
                    btn.skBadgeBackgroundColor = imageData.GroupHeaderBadgeBackgroundColor;
                    btn.skBadgeColor = imageData.GroupHeaderBadgeTextColor;
                    btn.skText = imageData.GroupHeaderText;
                    btn.skTextColor = imageData.GroupHeaderTextColor;
                    btn.skTextFont = imageData.GroupHeaderTextFont;
                    btn.skTextAlign = imageData.GroupHeaderTextAlign;
                    btn.Padding = imageData.GroupHeaderPadding;
                }
                else
                {
                    btn.skBackgroundColor = imageData.GroupFooterBackgroundColor;
                    btn.skText = imageData.GroupFooterText;
                    btn.skTextColor = imageData.GroupFooterTextColor;
                    btn.skTextFont = imageData.GroupFooterTextFont;
                    btn.skTextAlign = imageData.GroupFooterTextAlign;
                    btn.Padding = imageData.GroupHeaderPadding;
                }

                btn.ResumeLayout();
            }
            else if (collectionView != null)
            {
                item = collectionView.MakeSupplementaryView(kind, "NSCollectionViewElement", indexPath);
            }

        DoExit:
            return item;
        }
    }
}
