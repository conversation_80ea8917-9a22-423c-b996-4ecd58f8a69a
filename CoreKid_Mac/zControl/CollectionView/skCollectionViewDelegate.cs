﻿using System;
using System.IO;
using System.Text;
using System.Collections.Generic;

using AppKit;
using Foundation;
using CoreGraphics;
using ImageKit;

using iTong.CoreFoundation;
using ObjCRuntime;

namespace iTong.CoreModule
{
    [Register("skCollectionViewDelegate")]
    public class skCollectionViewDelegate : NSCollectionViewDelegate
    {
        #region Constructors

        // Called when created from unmanaged code
        public skCollectionViewDelegate(IntPtr handle) : base(handle)
        {
            this.Initialize();
        }

        // Called when created directly from a XIB file
        [Export("initWithCoder:")]
        public skCollectionViewDelegate(NSCoder coder) : base()
        {
            this.Initialize();
        }

        [Export("init")]
        public skCollectionViewDelegate() : base()
        {
            this.Initialize();
        }

        private void Initialize()
        {

        }

        #endregion

        public skCollectionViewDelegate(skCollectionView collectionView) : base()
        {
            this.mCollectionView = collectionView;
        }

        protected skCollectionView mCollectionView;
        protected bool mNeedUpdate = false;

        //[Export("collectionView:acceptDrop:indexPath:dropOperation:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null)]
        public override bool AcceptDrop(NSCollectionView collectionView, NSDraggingInfo draggingInfo, NSIndexPath indexPath, NSCollectionViewDropOperation dropOperation)
        {
            bool blnResult = false;

            if (this.mCollectionView != null)
                blnResult = this.mCollectionView.AllowDrop;

            return blnResult;
        }

        //[Export("collectionView:acceptDrop:index:dropOperation:")]
        public override bool AcceptDrop(NSCollectionView collectionView, NSDraggingInfo draggingInfo, nint index, NSCollectionViewDropOperation dropOperation)
        {
            bool blnResult = false;

            if (this.mCollectionView != null)
                blnResult = this.mCollectionView.AllowDrop;

            return blnResult;
        }

        //[Export("collectionView:canDragItemsAtIndexes:withEvent:")]
        public override bool CanDragItems(NSCollectionView collectionView, NSIndexSet indexes, NSEvent evt)
        {
            bool blnResult = false;

            if (this.mCollectionView != null)
                blnResult = this.mCollectionView.AllowDrag;

            return blnResult;
        }

        //[Export("collectionView:canDragItemsAtIndexPaths:withEvent:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null)]
        public override bool CanDragItems(NSCollectionView collectionView, NSSet indexPaths, NSEvent theEvent)
        {
            bool blnResult = false;

            if (this.mCollectionView != null)
                blnResult = this.mCollectionView.AllowDrag;

            return blnResult;
        }

        //[Export("collectionView:didEndDisplayingItem:forRepresentedObjectAtIndexPath:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null)]
        public override void DisplayingItemEnded(NSCollectionView collectionView, NSCollectionViewItem item, NSIndexPath indexPath)
        {
            Console.WriteLine("collectionView:didEndDisplayingItem:forRepresentedObjectAtIndexPath:");
            //throw new You_Should_Not_Call_base_In_This_Method();
        }

        //[Export("collectionView:didEndDisplayingSupplementaryView:forElementOfKind:atIndexPath:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null)]
        public override void DisplayingSupplementaryViewEnded(NSCollectionView collectionView, NSView view, string elementKind, NSIndexPath indexPath)
        {
            Console.WriteLine("collectionView:didEndDisplayingSupplementaryView:forElementOfKind:atIndexPath:");
            //throw new You_Should_Not_Call_base_In_This_Method();
        }

        //[Export("collectionView:draggingSession:endedAtPoint:dragOperation:")]
        public override void DraggingSessionEnded(NSCollectionView collectionView, NSDraggingSession draggingSession, CGPoint screenPoint, NSDragOperation dragOperation)
        {
            Console.WriteLine("collectionView:draggingSession:endedAtPoint:dragOperation:");
            //throw new You_Should_Not_Call_base_In_This_Method();
        }

        //[Export("collectionView:draggingSession:willBeginAtPoint:forItemsAtIndexes:")]
        public override void DraggingSessionWillBegin(NSCollectionView collectionView, NSDraggingSession draggingSession, CGPoint screenPoint, NSIndexSet indexes)
        {
            Console.WriteLine("collectionView:draggingSession:willBeginAtPoint:forItemsAtIndexes:");
            //throw new You_Should_Not_Call_base_In_This_Method();
        }

        //[Export("collectionView:draggingSession:willBeginAtPoint:forItemsAtIndexPaths:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null)]
        public override void DraggingSessionWillBegin(NSCollectionView collectionView, NSDraggingSession session, CGPoint screenPoint, NSSet indexPaths)
        {
            Console.WriteLine("collectionView:draggingSession:willBeginAtPoint:forItemsAtIndexPaths:");
            //throw new You_Should_Not_Call_base_In_This_Method();
        }

        //[Export("collectionView:draggingImageForItemsAtIndexPaths:withEvent:offset:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null)]
        public override NSImage GetDraggingImage(NSCollectionView collectionView, NSSet indexPaths, NSEvent theEvent, ref CGPoint dragImageOffset)
        {
            Console.WriteLine("collectionView:draggingImageForItemsAtIndexPaths:withEvent:offset:");
            //throw new You_Should_Not_Call_base_In_This_Method();

            return null;
        }

        //[Export("collectionView:namesOfPromisedFilesDroppedAtDestination:forDraggedItemsAtIndexPaths:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null), Deprecated(PlatformName.MacOSX, 10, 13, PlatformArchitecture.None, "Use 'NSFilePromiseReceiver' objects instead.")]
        public override string[] GetNamesOfPromisedFiles(NSCollectionView collectionView, NSUrl dropURL, NSSet indexPaths)
        {
            Console.WriteLine("collectionView:namesOfPromisedFilesDroppedAtDestination:forDraggedItemsAtIndexPaths:");
            //throw new You_Should_Not_Call_base_In_This_Method();
            List<string> listName = new List<string>();

            try
            {
                if (this.mCollectionView == null)
                    goto DoExit;

                NSIndexPath[] arrIndexPath = indexPaths.ToArray<NSIndexPath>();
                foreach (NSIndexPath indexPath in arrIndexPath)
                {
                    skImageData imageData = this.mCollectionView.GetImageData(indexPath);
                    if (imageData == null)
                        continue;

                    listName.Add(Path.GetFileName(imageData.Name));
                }
            }
            catch
            { }

        DoExit:
            return listName.ToArray();
        }

        //[Export("collectionView:pasteboardWriterForItemAtIndexPath:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null)]
        public override INSPasteboardWriting GetPasteboardWriter(NSCollectionView collectionView, NSIndexPath indexPath)
        {
            Console.WriteLine("collectionView:pasteboardWriterForItemAtIndexPath:");
            //throw new You_Should_Not_Call_base_In_This_Method();
            NSPasteboardWriting pasteboard = new NSPasteboardWriting();


            return pasteboard;
        }

        //[Export("collectionView:didChangeItemsAtIndexPaths:toHighlightState:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null)]
        public override void ItemsChanged(NSCollectionView collectionView, NSSet indexPaths, NSCollectionViewItemHighlightState highlightState)
        {
#if LOG
            Console.WriteLine("collectionView:didChangeItemsAtIndexPaths:toHighlightState:" + indexPaths.ToString());
            //throw new You_Should_Not_Call_base_In_This_Method();
#endif

            if (!this.mNeedUpdate)
                return;

            this.mNeedUpdate = false;
            this.mCollectionView.OnSelectChanged(this.mCollectionView, EventArgs.Empty);
        }

        //[Export("collectionView:didDeselectItemsAtIndexPaths:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null)]
        public override void ItemsDeselected(NSCollectionView collectionView, NSSet indexPaths)
        {
            this.mNeedUpdate = true;
#if LOG
            Console.WriteLine("collectionView:didDeselectItemsAtIndexPaths:" + indexPaths.ToString());
            //throw new You_Should_Not_Call_base_In_This_Method();
#endif

            if (this.mCollectionView == null)
                return;

            NSIndexPath[] arrIndexPath = indexPaths.ToArray<NSIndexPath>();
            if (arrIndexPath == null || arrIndexPath.Length == 0)
                return;

            foreach (skImageData item in this.mCollectionView.Items)
            {
                foreach (NSIndexPath indexPath in arrIndexPath)
                {
                    if (indexPath.Section == item.GroupIndex && indexPath.Item == item.ItemIndex)
                    {
                        item.Selected = false;//取消选中
                        break;
                    }
                }
            }

            //this.mCollectionView.OnSelectChanged(this.mCollectionView, EventArgs.Empty);
        }

        //[Export("collectionView:didSelectItemsAtIndexPaths:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null)]
        public override void ItemsSelected(NSCollectionView collectionView, NSSet indexPaths)
        {
            this.mNeedUpdate = true;
#if LOG
            Console.WriteLine("collectionView:didSelectItemsAtIndexPaths:" + indexPaths.ToString());
            //Console.WriteLine("collectionView:didSelectItemsAtIndexPaths:SelectionIndexPaths" + this.mCollectionView.SelectionIndexPaths.ToString());
            //throw new You_Should_Not_Call_base_In_This_Method();
#endif

            if (this.mCollectionView == null)
                return;

            NSIndexPath[] arrIndexPath = indexPaths.ToArray<NSIndexPath>();
            if (arrIndexPath == null || arrIndexPath.Length == 0)
                return;

            foreach (skImageData item in this.mCollectionView.Items)
            {
                foreach (NSIndexPath indexPath in arrIndexPath)
                {
                    if (indexPath.Section == item.GroupIndex && indexPath.Item == item.ItemIndex)
                    {
                        item.Selected = true;//选中
                        break;
                    }
                }
            }

            //this.mCollectionView.OnSelectChanged(this.mCollectionView, EventArgs.Empty);
        }

        //[Export("collectionView:namesOfPromisedFilesDroppedAtDestination:forDraggedItemsAtIndexes:"), Deprecated(PlatformName.MacOSX, 10, 13, PlatformArchitecture.None, "Use 'NSFilePromiseReceiver' objects instead.")]
        public override string[] NamesOfPromisedFilesDroppedAtDestination(NSCollectionView collectionView, NSUrl dropUrl, NSIndexSet indexes)
        {
#if LOG
            Console.WriteLine("collectionView:namesOfPromisedFilesDroppedAtDestination:forDraggedItemsAtIndexes:");
            //throw new You_Should_Not_Call_base_In_This_Method();
#endif


            List<string> listName = new List<string>();

            try
            {
                if (this.mCollectionView == null)
                    goto DoExit;

                nuint[] arrIndexPath = indexes.ToArray();
                foreach (nuint index in arrIndexPath)
                {
                    skImageData imageData = this.mCollectionView.GetImageData((int)index, 0);
                    if (imageData == null)
                        continue;

                    listName.Add(Path.GetFileName(imageData.Name));
                }
            }
            catch
            { }

        DoExit:
            return listName.ToArray();
        }


        //[Export("collectionView:pasteboardWriterForItemAtIndex:")]
        public override INSPasteboardWriting PasteboardWriterForItem(NSCollectionView collectionView, nuint index)
        {
#if LOG
            Console.WriteLine("collectionView:pasteboardWriterForItemAtIndex:");
            //throw new You_Should_Not_Call_base_In_This_Method();
#endif

            NSPasteboardWriting pasteboard = new NSPasteboardWriting();

            return pasteboard;
        }

        //[Export("collectionView:shouldChangeItemsAtIndexPaths:toHighlightState:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null)]
        public override NSSet ShouldChangeItems(NSCollectionView collectionView, NSSet indexPaths, NSCollectionViewItemHighlightState highlightState)
        {
            //Console.WriteLine("collectionView:shouldChangeItemsAtIndexPaths:toHighlightState:" + indexPaths.ToString());
            //throw new You_Should_Not_Call_base_In_This_Method();

            NSSet indexPathsNew = indexPaths;

            return indexPathsNew;
        }



        //[Export("collectionView:shouldSelectItemsAtIndexPaths:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null)]
        public override NSSet ShouldSelectItems(NSCollectionView collectionView, NSSet indexPaths)
        {
#if LOG
            Console.WriteLine("collectionView::" + indexPaths.ToString());
            //throw new You_Should_Not_Call_base_In_This_Method();
#endif

            NSSet indexPathsNew = indexPaths;

        //if (this.mCollectionView == null)
        //    goto DoExit;

        //NSIndexPath[] arrPaths = indexPaths.ToArray<NSIndexPath>();
        //if (arrPaths == null || arrPaths.Length == 0)
        //    goto DoExit;

        //foreach (NSIndexPath indexPath in arrPaths)
        //{
        //    if (indexPath.Length == 0)
        //        continue;

        //    tbImageData imageData = this.mCollectionView.GetImageData((int)indexPath.Item, (int)indexPath.Section);
        //    if (imageData == null)
        //        continue;

        //    //对数据源赋值，此操作一般由用户点击或者调用SelectedItems触发
        //    imageData.Selected = true;
        //}

        DoExit:
            return indexPathsNew;
        }

        //[Export("collectionView:shouldDeselectItemsAtIndexPaths:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null)]
        public override NSSet ShouldDeselectItems(NSCollectionView collectionView, NSSet indexPaths)
        {
#if LOG
            Console.WriteLine("collectionView:shouldDeselectItemsAtIndexPaths:" + indexPaths.ToString());
            //throw new You_Should_Not_Call_base_In_This_Method();
#endif

            NSSet indexPathsNew = indexPaths;

        //if (this.mCollectionView == null)
        //    goto DoExit;

        //if (this.mCollectionView.IsMultipleSelecting())
        //{
        //    indexPaths = new NSSet();
        //    goto DoExit;
        //}

        DoExit:
            return indexPathsNew;
        }



        //[Export("collectionView:transitionLayoutForOldLayout:newLayout:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null)]
        public override NSCollectionViewTransitionLayout TransitionLayout(NSCollectionView collectionView, NSCollectionViewLayout fromLayout, NSCollectionViewLayout toLayout)
        {
#if LOG
            Console.WriteLine("collectionView:transitionLayoutForOldLayout:newLayout:");
            //throw new You_Should_Not_Call_base_In_This_Method();
#endif

            return new NSCollectionViewTransitionLayout(fromLayout, toLayout);
        }

        //[Export("collectionView:updateDraggingItemsForDrag:")]
        public override void UpdateDraggingItemsForDrag(NSCollectionView collectionView, NSDraggingInfo draggingInfo)
        {
#if LOG
            Console.WriteLine("collectionView:updateDraggingItemsForDrag:");
            //throw new You_Should_Not_Call_base_In_This_Method();
#endif
        }

        //[Obsolete("Use 'ValidateDropOperation (NSCollectionView collectionView, NSDraggingInfo draggingInfo, ref NSIndexPath proposedDropIndexPath, ref NSCollectionViewDropOperation proposedDropOperation)' instead."), Mac(10, 11)]
        public override NSDragOperation ValidateDrop(NSCollectionView collectionView, NSDraggingInfo draggingInfo, out NSIndexPath proposedDropIndexPath, out NSCollectionViewDropOperation proposedDropOperation)
        {
            proposedDropIndexPath = null;
            proposedDropOperation = NSCollectionViewDropOperation.On;
            return this.ValidateDropOperation(collectionView, draggingInfo, ref proposedDropIndexPath, ref proposedDropOperation);
        }

        //[Export("collectionView:validateDrop:proposedIndex:dropOperation:")]
        public override NSDragOperation ValidateDrop(NSCollectionView collectionView, NSDraggingInfo draggingInfo, ref nint dropIndex, ref NSCollectionViewDropOperation dropOperation)
        {
#if LOG
            Console.WriteLine("collectionView:validateDrop:proposedIndex:dropOperation:");
            //throw new You_Should_Not_Call_base_In_This_Method();
#endif

            return NSDragOperation.All;
        }

        //[Export("collectionView:validateDrop:proposedIndexPath:dropOperation:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null)]
        public override NSDragOperation ValidateDropOperation(NSCollectionView collectionView, NSDraggingInfo draggingInfo, ref NSIndexPath proposedDropIndexPath, ref NSCollectionViewDropOperation proposedDropOperation)
        {
#if LOG
            Console.WriteLine("collectionView:validateDrop:proposedIndexPath:dropOperation:");
            //throw new You_Should_Not_Call_base_In_This_Method();
#endif

            return NSDragOperation.All;
        }

        //[Export("collectionView:willDisplayItem:forRepresentedObjectAtIndexPath:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null)]
        public override void WillDisplayItem(NSCollectionView collectionView, NSCollectionViewItem item, NSIndexPath indexPath)
        {
#if LOG
            Console.WriteLine("collectionView:willDisplayItem:forRepresentedObjectAtIndexPath:");
            //throw new You_Should_Not_Call_base_In_This_Method();
#endif
        }

        //[Export("collectionView:willDisplaySupplementaryView:forElementKind:atIndexPath:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null)]
        public override void WillDisplaySupplementaryView(NSCollectionView collectionView, NSView view, NSString elementKind, NSIndexPath indexPath)
        {
#if LOG
            Console.WriteLine("collectionView:willDisplaySupplementaryView:forElementKind:atIndexPath:");
            //throw new You_Should_Not_Call_base_In_This_Method();
#endif
        }

        //[Export("collectionView:writeItemsAtIndexes:toPasteboard:"), Deprecated(PlatformName.MacOSX, 10, 15, PlatformArchitecture.None, "Use the 'GetPasteboardWriter' method instead.")]
        public override bool WriteItems(NSCollectionView collectionView, NSIndexSet indexes, NSPasteboard toPasteboard)
        {
#if LOG
            Console.WriteLine("collectionView:writeItemsAtIndexes:toPasteboard:");
            //throw new You_Should_Not_Call_base_In_This_Method();
#endif

            //toPasteboard.ClearContents();
            //toPasteboard.DeclareTypes(new string[] { NSPasteboard.NSStringType }, null);
            //toPasteboard.SetStringForType(strValue, NSPasteboard.NSStringType);

            return true;
        }

        //[Export("collectionView:writeItemsAtIndexPaths:toPasteboard:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null), Deprecated(PlatformName.MacOSX, 10, 15, PlatformArchitecture.None, "Use the 'GetPasteboardWriter' method instead.")]
        public override bool WriteItems(NSCollectionView collectionView, NSSet indexPaths, NSPasteboard pasteboard)
        {
#if LOG
            Console.WriteLine("collectionView:writeItemsAtIndexPaths:toPasteboard:");
            //throw new You_Should_Not_Call_base_In_This_Method();
#endif

            return true;
        }
    }
}
