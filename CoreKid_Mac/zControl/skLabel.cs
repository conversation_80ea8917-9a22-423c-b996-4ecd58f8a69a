﻿using System;
using System.Drawing;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Collections.Generic;
using System.Text;
using System.IO;

using Foundation;
using CoreGraphics;
using AppKit;

using Color = AppKit.NSColor;
using Font = AppKit.NSFont;
using Image = AppKit.NSImage;
using ContentAlignment = iTong.CoreModule.ContentAlignment;

using Point = CoreGraphics.CGPoint;
using Size = CoreGraphics.CGSize;
using Rectangle = CoreGraphics.CGRect;


using iTong.CoreFoundation;
using System.Windows.Forms;

namespace iTong.CoreModule
{
    [Register("skLabel")]
    public class skLabel : skControlBorder
    {
        #region Constructors

        // Called when created from unmanaged code
        public skLabel(IntPtr handle) : base(handle)
        {
            this.Initialize();
        }

        // Called when created directly from a XIB file
        [Export("initWithCoder:")]
        public skLabel(NSCoder coder) : base(coder)
        {
            this.Initialize();
        }

        [Export("init")]
        public skLabel() : base()
        {
            this.Initialize();
        }

        private void Initialize()
        {
            this.mUseHandCursor = false;
        }

        #endregion

        public event EventHandler<skItemInfo> ItemClick;

        protected string mText = string.Empty;
        protected Color mTextColor = Color.Black;
        protected Color mTextColorHover = Color.Black;
        protected Color mTextColorDown = Color.Black;
        protected Color mTextColorDisable = Color.Gray;

        protected Color mTextColorCheck = Color.Black;
        protected Color mTextColorCheckHover = Color.Black;
        protected Color mTextColorCheckDown = Color.Black;
        protected Color mTextColorCheckDisable = Color.Gray;

        protected int mRowDiff = 0;
        protected bool mAutoHeight = false;

        protected Font mTextFont = MyFont.DefaultFont;

        protected Color mTextBoldColor = Color.Black;
        protected Color mTextItalicColor = Color.Black;
        protected Color mTextItalicBoldColor = Color.Black;
        protected Color mTextLinkColor = Color.FromRgb(68, 110, 252);
        protected NSTextAlignment mTextAlign = ContentAlignment.MiddleLeft;

        protected Dictionary<string, skItemInfo> mDictSpecial = new Dictionary<string, skItemInfo>();
        protected skItemInfo mMouseDownItem;

        protected int mLineSpace = 5;
        protected int mShowLineCount = -1;
        protected bool mAutoWidth = false;
        protected bool mWordWrapping = true;
        //protected List<string> mMatchKey = new List<string>()
        //{
        //    "bold",
        //    "italic",
        //    "italicbold"
        //};
        #region Property


        public Dictionary<string, skItemInfo> DictSpecial
        {
            get { return this.mDictSpecial; }
        }

        //Text
        public string skText
        {
            get { return this.mText; }
            set
            {
                if (value == null)
                    value = string.Empty;

                this.mText = value; this.AdjustSize(); this.NeedsDisplay = true;
            }
        }

        public NSTextAlignment skTextAlign
        {
            get { return this.mTextAlign; }
            set
            {
                this.mTextAlign = value; this.AdjustSize(); this.NeedsDisplay = true;
            }
        }

        public Color skTextColor
        {
            get { return this.mTextColor; }
            set
            {
                if (this.mTextColorHover == this.mTextColor)
                    this.mTextColorHover = value;

                if (this.mTextColorDown == this.mTextColor)
                    this.mTextColorDown = value;

                if (this.mTextColorDisable == this.mTextColor)
                    this.mTextColorDisable = value;

                this.mTextColor = value;

                this.NeedsDisplay = true;
            }
        }

        public Color skTextColorCheck
        {
            get { return this.mTextColorCheck; }
            set
            {
                if (this.mTextColorCheckHover == this.mTextColorCheck)
                    this.mTextColorCheckHover = value;

                if (this.mTextColorCheckDown == this.mTextColorCheck)
                    this.mTextColorCheckDown = value;

                if (this.mTextColorCheckDisable == this.mTextColorCheck)
                    this.mTextColorCheckDisable = value;

                this.mTextColorCheck = value;

                this.NeedsDisplay = true;
            }
        }

        public Color skTextColorHover
        {
            get { return this.mTextColorHover; }
            set { this.mTextColorHover = value; this.NeedsDisplay = true; }
        }

        public Color skTextColorDown
        {
            get { return this.mTextColorDown; }
            set { this.mTextColorDown = value; this.NeedsDisplay = true; }
        }

        public Color skTextColorDisable
        {
            get { return this.mTextColorDisable; }
            set { this.mTextColorDisable = value; this.NeedsDisplay = true; }
        }

        public Color skTextColorCheckHover
        {
            get { return this.mTextColorCheckHover; }
            set { this.mTextColorCheckHover = value; this.NeedsDisplay = true; }
        }

        public Color skTextColorCheckDown
        {
            get { return this.mTextColorCheckDown; }
            set { this.mTextColorCheckDown = value; this.NeedsDisplay = true; }
        }

        public Color skTextColorCheckDisable
        {
            get { return this.mTextColorCheckDisable; }
            set { this.mTextColorCheckDisable = value; this.NeedsDisplay = true; }
        }

        public Font skTextFont
        {
            get { return this.mTextFont; }
            set { this.mTextFont = value.ReplaceFont(); this.NeedsDisplay = true; }
        }

        public Color skTextBoldColor
        {
            get { return this.mTextBoldColor; }
            set { this.mTextBoldColor = value; this.NeedsDisplay = true; }
        }

        public Color skTextItalicColor
        {
            get { return this.mTextItalicColor; }
            set { this.mTextItalicColor = value; this.NeedsDisplay = true; }
        }

        public Color skTextItalicBoldColor
        {
            get { return this.mTextItalicBoldColor; }
            set { this.mTextItalicBoldColor = value; this.NeedsDisplay = true; }
        }

        public Color skTextLinkColor
        {
            get { return this.mTextLinkColor; }
            set { this.mTextLinkColor = value; this.NeedsDisplay = true; }
        }

        public int skRowDiff
        {
            get { return this.mRowDiff; }
            set { this.mRowDiff = value; this.NeedsDisplay = true; }
        }

        public bool skAutoHeight
        {
            get { return this.mAutoHeight; }
            set
            {
                this.mAutoHeight = value;
                this.AdjustSize();
                this.NeedsDisplay = true;
            }
        }

        public int skLineSpace
        {
            get { return mLineSpace; }
            set
            {
                mLineSpace = value;
                this.NeedsDisplay = true;
            }
        }

        public int skShowLineCount
        {
            get { return mShowLineCount; }
            set
            {
                mShowLineCount = value;
                this.NeedsDisplay = true;
            }
        }

        public bool skAutoWidth
        {
            get { return mAutoWidth; }
            set
            {
                mAutoWidth = value;
                this.NeedsDisplay = true;
            }
        }

        public bool skWordWrapping
        {
            get { return mWordWrapping; }
            set
            {
                mWordWrapping = value;
                this.NeedsDisplay = true;
            }
        }

        #endregion


        protected virtual Dictionary<int, string> FindText(string strText)
        {
            Dictionary<int, string> dict = new Dictionary<int, string>();

            if (string.IsNullOrEmpty(strText) || this.mDictSpecial.Count == 0)
                goto DoExit;

            foreach (string itemText in this.mDictSpecial.Keys)
            {
                int indexStart = 0;
                while (true)
                {
                    int indexFind = strText.IndexOf(itemText, indexStart);
                    if (indexFind < 0 || indexFind >= strText.Length)
                        break;

                    dict[indexFind] = itemText;
                    indexStart = indexFind + itemText.Length;
                }
            }

        DoExit:
            return dict;
        }

        protected virtual List<int> SortIndex(Dictionary<int, string> dict)
        {
            List<int> list = new List<int>();

            foreach (int index in dict.Keys)
                list.Add(index);

            list.Sort();

            return list;
        }

        public skItemInfo GetItemFirst(string strKey)
        {
            List<skItemInfo> list = GetItemList(strKey);
            if (list.Count > 0)
                return list[0];
            else
                return null;
        }

        public List<skItemInfo> GetItemList(string strKey)
        {
            List<skItemInfo> list = new List<skItemInfo>();

            foreach (skItemInfo item in this.mDictSpecial.Values)
            {
                if (string.Compare(item.IndexKey, strKey, true) == 0)
                    list.Add(item);
            }

            return list;
        }

        public virtual void SetText(string strText, string strKey = "")
        {
            List<string> listKey = new List<string>();
            if (!string.IsNullOrEmpty(strKey))
                listKey.Add(strKey);

            this.SetText(strText, listKey);
        }

        public virtual void SetText(string strText, List<string> listKey)
        {
            skContentFormat format = FormatText(strText, listKey);

            this.mDictSpecial.Clear();

            foreach (skItemInfo item in format.Items)
            {
                if (string.IsNullOrEmpty(item.Text))
                    continue;

                switch (item.IndexKey)
                {
                    case "bold":
                        item.TextColor = this.mTextBoldColor;
                        item.TextFont = MyFont.CreateFont(this.mTextFont.FamilyName, this.mTextFont.PointSize.ToSingle(), true);
                        break;

                    case "italic":
                        item.TextColor = this.mTextItalicColor;
                        item.TextFont = MyFont.CreateFont(this.mTextFont.FamilyName, this.mTextFont.PointSize.ToSingle(), true, false, true);
                        break;

                    case "italicbold":
                        item.TextColor = this.mTextItalicBoldColor;
                        item.TextFont = MyFont.CreateFont(this.mTextFont.FamilyName, this.mTextFont.PointSize.ToSingle(), true, true, true);
                        break;

                    case "link":
                        item.IsUnderLine = true;
                        item.TextColor = this.mTextLinkColor;
                        item.TextFont = MyFont.CreateFont(this.mTextFont.FamilyName, this.mTextFont.PointSize.ToSingle());
                        break;
                }

                this.mDictSpecial[item.Text] = item;
            }

            this.skText = format.Content;
        }

        public static skContentFormat FormatText(string strText, string strKey = "")
        {
            List<string> listKey = new List<string>();
            if (!string.IsNullOrEmpty(strKey))
                listKey.Add(strKey);

            return FormatText(strText, listKey);
        }

        public static skContentFormat FormatText(string strText, List<string> listKey)
        {
            skContentFormat format = new skContentFormat();
            format.Source = strText;

            string strTextToLower = strText.ToLower();

            try
            {
                if (listKey == null)
                    listKey = new List<string>();

                if (listKey.Count == 0)
                {
                    listKey.Add("bold");
                    listKey.Add("italic");
                    listKey.Add("italicbold");
                    listKey.Add("cursor");
                    listKey.Add("link");
                    listKey.Add("image_cell");
                    //listKey.Add("gotourl");
                    //listKey.Add("retry");
                    //listKey.Add("log");
                    //listKey.Add("password");
                    //listKey.Add("refresh");
                    //listKey.Add("authorize");
                    //listKey.Add("detail");
                    //listKey.Add("upgrade");
                    //listKey.Add("premium");
                    //listKey.Add("open");
                    //listKey.Add("how");
                    //listKey.Add("privacy");
                    //listKey.Add("eula");
                    //listKey.Add("exchange");
                }

                string keyStartRight = ">";
                foreach (string strKey in listKey)
                {
                    string keyStart = string.Format("<{0}", strKey);
                    string keyEnd = string.Format("</{0}>", strKey);

                    int indexStart = 0;
                    while (true)
                    {
                        //查找标签开始
                        int intStart = strTextToLower.IndexOf(keyStart, indexStart);
                        if (intStart < 0)
                            break;

                        indexStart = intStart + keyStart.Length;

                        //查找标签开始的右尖括号
                        int intStartRight = strTextToLower.IndexOf(keyStartRight, indexStart);
                        if (intStartRight < 0)
                            break;

                        indexStart = intStartRight + keyStartRight.Length;

                        skItemInfo item = null;

                        //<{0} />内容标签为空
                        if (strTextToLower.Substring(intStartRight - 1, 1) == "/")
                        {
                            item = new skItemInfo();
                            item.IndexKey = strKey;
                            item.IndexStart = intStart;
                            item.IndexStartRight = intStartRight + keyStartRight.Length;
                            item.IndexEnd = item.IndexStart;
                            item.IndexEndRight = item.IndexStartRight;
                            item.Text = string.Empty;
                        }
                        else
                        {
                            //查找标签结束                        
                            int intEnd = strTextToLower.IndexOf(keyEnd, indexStart);
                            if (intEnd < 0)
                                break;

                            indexStart = intEnd + keyEnd.Length;

                            item = new skItemInfo();
                            item.IndexKey = strKey;
                            item.IndexStart = intStart;
                            item.IndexStartRight = intStartRight + keyStartRight.Length;
                            item.IndexEnd = intEnd;
                            item.IndexEndRight = intEnd + keyEnd.Length;
                            item.Text = strText.Substring(item.IndexStartRight, item.IndexEnd - item.IndexStartRight);
                        }

                        format.Items.Add(item);
                    }
                }

                //按文本索引排序
                format.Items.Sort(new skItemInfoCompare());

                StringBuilder sb = new StringBuilder();

                int index = 0;
                foreach (skItemInfo item in format.Items)
                {
                    sb.Append(strText.Substring(index, item.IndexStart - index));
                    item.Index = sb.Length;//设置新索引
                    sb.Append(item.Text);

                    //更新最后索引
                    index = item.IndexEndRight;
                }

                //插入最后文本
                if (index < strTextToLower.Length)
                    sb.Append(strText.Substring(index));

                format.Content = sb.ToString();
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "FormatText");
            }

            return format;
        }

        public virtual void AddSpecial(skItemInfo item)
        {
            if (item == null || string.IsNullOrEmpty(item.Text))
                return;

            this.mDictSpecial[item.Text] = item;
            this.Invalidate();
        }

        public virtual void AddSpecial(string strText, Font textFont, Color textColor, string linkUrl = "", bool blnRefresh = true, NSTextAlignment textAlign = NSTextAlignment.Left, bool isUnderLine = false)
        {
            skItemInfo item = new skItemInfo();
            item.Text = strText;
            item.TextFont = textFont;
            item.TextColor = textColor;
            item.LinkUrl = linkUrl;
            item.TextAlign = textAlign;
            item.IsUnderLine = isUnderLine;

            this.mDictSpecial[item.Text] = item;

            if (blnRefresh)
                this.Invalidate();
        }

        public virtual void AddSpecialIcon(string strText, Image img, Size imgSize)
        {
            skItemInfo item = new skItemInfo();
            item.Text = strText;
            item.Icon = img;
            item.IconSize = imgSize;

            this.mDictSpecial[item.Text] = item;
            this.Invalidate();
        }

        public virtual void RemoveSpecial(skItemInfo item)
        {
            if (!this.mDictSpecial.ContainsKey(item.Text))
                return;

            this.mDictSpecial.Remove(item.Text);
            this.Invalidate();
        }

        public virtual void ClearSpecial()
        {
            this.mDictSpecial.Clear();
            this.Invalidate();
        }

        public virtual void UpdateSpecalFont(Font font)
        {
            foreach (skItemInfo item in this.mDictSpecial.Values)
            {
                item.TextFont = MyFont.CreateFont(font.PointSize.ToSingle(), item.TextFont.IsBold());
            }
        }

        public virtual void UpdateSpecalIconSize(Size iconSize)
        {
            foreach (skItemInfo item in this.mDictSpecial.Values)
            {
                item.IconSize = iconSize;
            }
        }

        protected Color GetTextColor()
        {
            Color txtColor = Color.Black;

            txtColor = this.mTextColor;
            switch (this.mMouseState)
            {
                case skMouseState.MouseHover:
                    txtColor = this.mTextColorHover;
                    break;

                case skMouseState.MouseDown:
                    txtColor = this.mTextColorDown;
                    break;
            }

            if (!this.CheckEnable())
                txtColor = this.mTextColorDisable;


            return txtColor;
        }

        public override void DrawRect(Rectangle dirtyRect)
        {
            base.DrawRect(dirtyRect);

            this.AutoLayout();
        }

        public virtual void AutoLayout()
        {
            try
            {
                //if (this.Name == "lblStartSetting")
                //{
                //    Console.WriteLine(this.mText);
                //}

                Color txtColor = this.GetTextColor();
                //int diffY = 2;
                //Rectangle rect = new Rectangle(this.Padding.Left, this.Padding.Top + diffY, this.Width - this.Padding.Horizontal, this.Height - this.Padding.Vertical - diffY);

                Rectangle rect = this.Bounds;

                if (this.mMaxSize.Width > 0 && rect.Width > this.mMaxSize.Width)
                {
                    rect.Width = this.mMaxSize.Width;
                }
                else if (this.mMinSize.Width > 0 && rect.Width < this.mMinSize.Width)
                {
                    rect.Width = this.mMinSize.Width;
                }
                else if(!this.mAutoHeight && this.mAutoSize)
                {
                    rect.Width = 10000;
                }

                if (this.mMaxSize.Height > 0 && rect.Height > this.mMaxSize.Height)
                {
                    rect.Height = this.mMaxSize.Height;
                }
                else if (this.mMinSize.Height > 0 && rect.Height < this.mMinSize.Height)
                {
                    rect.Height = this.mMinSize.Height;
                }

                List<object> listResult = skGuiHelper.DrawLabel(this.mText, rect, txtColor, this.mTextFont, this.mTextAlign, this.Padding, mLineSpace, this.mWordWrapping, this.mDictSpecial, this.mShowLineCount);
                nfloat textHeight = (nfloat)listResult[0];
                bool autoEllipsis = (bool)listResult[1];//是否被自动截断
                nfloat textWidth = (nfloat)listResult[2];

                if (textWidth == 0 || textHeight == 0)
                    return;

                //设置Tip
                this.mToolTip = autoEllipsis ? this.mText : string.Empty;
                                
                nfloat newWidth = textWidth;
                nfloat newHeight = textHeight;

                if (this.mMinSize.Width > newWidth)
                    newWidth = this.mMinSize.Width;

                if (this.mMinSize.Height > newHeight)
                    newHeight = this.mMinSize.Height;

                if (this.mAutoHeight)
                {
                    //不改变原有宽度
                    if (newWidth < rect.Width)
                        newWidth = rect.Width;
                }

                if (this.mAutoSize || this.mAutoHeight)
                {
                    if (newWidth != this.Width || newHeight != this.Height)
                    {
                        this.mSuspendLayout = true;
                        this.Frame = new CGRect(this.Left, this.Bottom - newHeight, newWidth, newHeight);
                        //this.Size = new Size(textWidth, textHeight);
                        this.mSuspendLayout = false;

                        this.Invalidate();
                    }
                }             
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "skLable.AutoLayout");
            }
        }

        public override void AdjustSize()
        {
            if (this.IsSuspendLayout)
                return;

            this.AutoLayout();
        }

        private skItemInfo GetItem(Point pos)
        {
            skItemInfo itemReturn = null;

            foreach (skItemInfo item in this.mDictSpecial.Values)
            {
                foreach (Rectangle rect in item.ListTextRect)
                {
                    if (OSHelper.OSType == OSType.MacOS)
                    {
                        pos = new Point(pos.X, this.Height - pos.Y);
                    }

                    if (rect.Contains(pos))
                    {
                        itemReturn = item;
                        goto DoExit;
                    }
                }
            }

        DoExit:
            return itemReturn;
        }

        protected override void OnMouseMove(MouseEventArgs e)
        {
            base.OnMouseMove(e);


            skItemInfo tmpItem = this.GetItem(e.Location);
            if (tmpItem != null && (!string.IsNullOrEmpty(tmpItem.LinkUrl)))
            {
                Cursor = Cursors.Hand;
            }
            else
            {
                Cursor = Cursors.Arrow;
            }
        }
        protected override void OnMouseDown(MouseEventArgs e)
        {
            base.OnMouseDown(e);

            this.mMouseDownItem = this.GetItem(e.Location);
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            base.OnMouseUp(e);
            
            skItemInfo tmpItem = this.GetItem(e.Location);
            if (tmpItem != null && tmpItem == this.mMouseDownItem)
            {
                if (!string.IsNullOrEmpty(tmpItem.LinkUrl) && tmpItem.LinkUrl.ToLower().Contains("http"))
                {
                    Common.OpenUrl(tmpItem.LinkUrl);
                }
                else
                {
                    if (this.ItemClick != null)
                        this.ItemClick(this, tmpItem);
                }
            }
        }

    }


}
