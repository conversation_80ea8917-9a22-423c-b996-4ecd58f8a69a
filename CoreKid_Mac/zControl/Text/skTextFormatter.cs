﻿using System;

using Foundation;
using Obj<PERSON>untime;

namespace iTong.CoreModule
{
    [Register("skTextFormatter")]
    public class skTextFormatter : NSFormatter
    {
        private skTextField mTextField;

        public skTextFormatter(skTextField textField)
        {
            this.mTextField = textField;
        }

        //[return: Release]
        //public override NSObject Copy(NSZone zone)
        //{
        //    Console.WriteLine(string.Format("Copy: zone={0}", zone.ToString()));

        //    return null;
        //}

        //public override void EncodeTo(NSCoder encoder)
        //{
        //    //Console.WriteLine(string.Format("EncodeTo: encoder={0}", encoder.ToString()));
        //}

        public override NSAttributedString GetAttributedString(NSObject obj, NSDictionary<NSString, NSObject> defaultAttributes)
        {
            NSMutableAttributedString dict = new NSMutableAttributedString(obj.ToString(), defaultAttributes);

            return dict;
        }

        public override string EditingStringFor(NSObject value)
        {
            //Console.WriteLine(string.Format("EditingStringFor: value={0}", value.ToString()));

            return value.ToString();
        }

        public override string StringFor(NSObject value)
        {
            //Console.WriteLine(string.Format("StringFor: value={0}", value.ToString()));

            return value.ToString();
        }

        public override bool GetObjectValue(out NSObject obj, string str, out NSString error)
        {
            //Console.WriteLine(string.Format("GetObjectValue: str={0}", str));

            obj = new NSString(str);
            error = null;

            return true;
        }

        /// <summary>
        /// Returns a Boolean value that indicates whether a partial string is valid.
        /// </summary>
        /// <returns>YES if partialString is an acceptable value, otherwise NO.</returns>
        /// <param name="partialString">The text currently in a cell.</param>
        /// <param name="newString">If partialString needs to be modified, upon return contains the replacement string.</param>
        /// <param name="error">If non-nil, if validation fails contains an NSString object that describes the problem.</param>
        [Export("isPartialStringValid:newEditingString:errorDescription:")]
        public virtual bool IsPartialStringValidNew(string partialString, ref string newString, out NSString error)
        {
            //Console.WriteLine(string.Format("IsPartialStringValid: partialString={0}", partialString));

            bool blnResult = true;

            skTextFieldEventArgs args = new skTextFieldEventArgs();
            args.NewString = partialString;

            this.mTextField.OnCheckInput(this.mTextField, args);

            blnResult = !args.Cancel;

            if (blnResult)
                newString = args.NewString;

            if (!string.IsNullOrEmpty(args.ErrMsg))
                error = new NSString(args.ErrMsg);
            else
                error = null;


            return blnResult;
        }

        /// <summary>
        /// YES if partialStringPtr is acceptable, otherwise NO.
        /// </summary>
        /// <returns><c>true</c>, if partial string valid new was ised, <c>false</c> otherwise.</returns>
        /// <param name="partialString">The new string to validate.</param>
        /// <param name="proposedSelRange">The selection range that will be used if the string is accepted or replaced.</param>
        /// <param name="origString">The original string, before the proposed change.</param>
        /// <param name="origSelRange">The selection range over which the change is to take place.</param>
        /// <param name="error">If non-nil, if validation fails contains an NSString object that describes the problem.</param>
        [Export("isPartialStringValid:proposedSelectedRange:originalString:originalSelectedRange:errorDescription:")]
        public virtual bool IsPartialStringValidNew(ref string partialString, ref NSRange proposedSelRange, string origString, NSRange origSelRange, out string error)
        {
            //Console.WriteLine(string.Format("IsPartialStringValid: origString={0}, \torigSelRange={1}", origString, origSelRange.ToString()));

            bool blnResult = true;

            if (proposedSelRange.Location == NSRange.NotFound || proposedSelRange.Length == NSRange.NotFound)
                proposedSelRange = new NSRange();

            skTextFieldEventArgs args = new skTextFieldEventArgs();
            args.OriginString = origString;
            args.OriginRange = origSelRange;
            args.NewString = partialString;
            args.NewRangeIfAccepted = proposedSelRange;

            this.mTextField.OnCheckInput(this.mTextField, args);

            blnResult = !args.Cancel;

            if (!string.IsNullOrEmpty(args.ErrMsg))
                error = new NSString(args.ErrMsg);
            else
                error = null;

            return blnResult;
        }

    }
}
