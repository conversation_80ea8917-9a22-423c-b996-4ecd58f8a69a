﻿using System;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Collections.Generic;

using AppKit;
using Foundation;
using ObjCRuntime;
using CoreGraphics;

using iTong.CoreFoundation;


namespace iTong.CoreModule
{
    public static class MyDylibExtend
    {

        private static readonly IntPtr selBoundingRectWithSize_Options_Context_Handle;
        private static readonly IntPtr selBeginSheetModalForWindow_CompletionHandler_Handle;

        private static readonly IntPtr class_ptr_NSString;
        private static readonly IntPtr class_ptr_NSAlert;

        static MyDylibExtend()
        {

            class_ptr_NSString = Class.GetHandle("NSString");
            class_ptr_NSAlert = Class.GetHandle("NSAlert");

            selBoundingRectWithSize_Options_Context_Handle = Selector.GetHandle("boundingRectWithSize:options:context:");
            selBeginSheetModalForWindow_CompletionHandler_Handle = Selector.GetHandle("beginSheetModalForWindow:completionHandler:");
        }

        [DllImport("/usr/lib/libobjc.dylib", EntryPoint = "objc_msgSend")]
        public static extern CGRect CGSize_objc_msgSend_IntPtr_IntPtr_IntPtr(IntPtr receiver, IntPtr selector, CGSize arg1, NSStringDrawingOptions arg2, IntPtr arg3, IntPtr arg4);

        [DllImport("/usr/lib/libobjc.dylib", EntryPoint = "objc_msgSend_stret")]
        public static extern void CGRect_objc_msgSend_stret_CGSize_UInt64_IntPtr(out CGRect retval, IntPtr receiver, IntPtr selector, CGSize arg1, ulong arg2, IntPtr arg3);

        [DllImport("/usr/lib/libobjc.dylib", EntryPoint = "objc_msgSend_stret")]
        public static extern void CGRect_objc_msgSend_stret_CGSize_UInt32_IntPtr(out CGRect retval, IntPtr receiver, IntPtr selector, CGSize arg1, uint arg2, IntPtr arg3);

        [Export("boundingRectWithSize:options:attributes:context:")]
        public static CGRect BoundingRectWithSize(this NSAttributedString strText, CGSize size, NSStringDrawingOptions options, NSStringDrawingContext context = null)
        {
            NSApplication.EnsureUIThread();
            CGRect rect;

            if (IntPtr.Size == 8)
                CGRect_objc_msgSend_stret_CGSize_UInt64_IntPtr(out rect, strText.Handle, selBoundingRectWithSize_Options_Context_Handle, size, (ulong)options, (context == null ? IntPtr.Zero : context.Handle));
            else
                CGRect_objc_msgSend_stret_CGSize_UInt32_IntPtr(out rect, strText.Handle, selBoundingRectWithSize_Options_Context_Handle, size, (uint)options, (context == null ? IntPtr.Zero : context.Handle));

            return rect;
        }

        //[Export("beginSheetModalForWindow:completionHandler:"), Introduced(PlatformName.MacOSX, 10, 9, PlatformArchitecture.All, null)]
        //public static unsafe void BeginSheet(this skWindow This, NSWindow ownerWindow, Action<NSModalResponse> handler)
        //{
        //    BlockLiteral* literalPtr;

        //    NSApplication.EnsureUIThread();

        //    if (handler == null)
        //    {
        //        literalPtr = null;
        //    }
        //    else
        //    {
        //        BlockLiteral literal = new BlockLiteral();
        //        literalPtr = &literal;

        //        literal.SetupBlockUnsafe(SDActionArity1V8.Handler, handler);
        //    }

        //    if (This.IsDirectBinding)
        //    {
        //        MyObjC.void_objc_msgSend_IntPtr_IntPtr(This.Handle, selBeginSheetModalForWindow_CompletionHandler_Handle, (ownerWindow == null) ? IntPtr.Zero : ownerWindow.Handle, (IntPtr)literalPtr);
        //    }
        //    else
        //    {
        //        MyObjC.void_objc_msgSendSuper_IntPtr_IntPtr(This.SuperHandle, selBeginSheetModalForWindow_CompletionHandler_Handle, (ownerWindow == null) ? IntPtr.Zero : ownerWindow.Handle, (IntPtr)literalPtr);
        //    }

        //    if (literalPtr != null)
        //        literalPtr->CleanupBlock();
        //}

        //public static int RunSheetMode(this skWindow This, NSWindow ownerWindow, NSApplication application)
        //{
        //    int result = -1000;

        //    if (application == null)
        //        application = NSApplication.SharedApplication;

        //    This.BeginSheet(ownerWindow,)

        //    return result;
        //}

    }

    [UnmanagedFunctionPointer(CallingConvention.Cdecl), UserDelegateType(typeof(Action<NSModalResponse>))]
    internal delegate void DActionArity1V8(IntPtr block, nint obj);

    internal static class SDActionArity1V8
    {
        // Fields
        internal static readonly DActionArity1V8 Handler = new DActionArity1V8(SDActionArity1V8.Invoke);

        // Methods
        [MonoPInvokeCallback(typeof(DActionArity1V8))]
        private static unsafe void Invoke(IntPtr block, nint obj)
        {
            BlockLiteral* literalPtr = (BlockLiteral*)block;
            Action<NSModalResponse> target = (Action<NSModalResponse>)literalPtr->Target;
            if (target != null)
            {
                target((NSModalResponse)(long)obj);
            }
        }
    }

    //[Register("__MonoMac_NSAlertDidEndDispatcher")]
    //internal class NSAlertDidEndDispatcher : NSObject
    //{
    //    // Fields
    //    private const string selector = "alertDidEnd:returnCode:contextInfo:";
    //    public static readonly Selector Selector = new Selector("alertDidEnd:returnCode:contextInfo:");
    //    private Action<nint> action;

    //    // Methods
    //    public NSAlertDidEndDispatcher(Action<nint> action)
    //    {
    //        this.action = action;
    //        base.IsDirectBinding = false;
    //        base.DangerousRetain();
    //    }

    //    [Export("alertDidEnd:returnCode:contextInfo:"), Preserve(Conditional = true)]
    //    public void OnAlertDidEnd(NSAlert alert, nint returnCode, IntPtr context)
    //    {
    //        try
    //        {
    //            if (this.action != null)
    //            {
    //                this.action(returnCode);
    //            }
    //        }
    //        finally
    //        {
    //            this.action = null;
    //            base.DangerousRelease();
    //        }
    //    }
    //}

}
