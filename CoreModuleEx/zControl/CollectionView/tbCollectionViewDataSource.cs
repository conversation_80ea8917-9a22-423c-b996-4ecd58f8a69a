﻿using System;
using System.IO;
using System.Text;
using System.Collections.Generic;

using AppKit;
using Foundation;
using CoreGraphics;
using ImageKit;

using iTong.CoreFoundation;
using ObjCRuntime;

namespace iTong.CoreModule
{
    [Register("tbCollectionViewDataSource")]
    public class tbCollectionViewDataSource : NSCollectionViewDataSource
    {
        #region Constructors

        // Called when created from unmanaged code
        public tbCollectionViewDataSource(IntPtr handle) : base(handle)
        {
            Initialize();
        }

        public tbCollectionViewDataSource(NSObjectFlag t) : base(t)
        {
            Initialize();
        }

        [Export("init")]
        public tbCollectionViewDataSource() : base()
        {
            this.Initialize();
        }

        private void Initialize()
        {

        }

        #endregion

        private tbCollectionView mCollectionView;

        public tbCollectionViewDataSource(tbCollectionView collectionView) : base()
        {
            this.Initialize();

            this.mCollectionView = collectionView;
        }

        //[Export("collectionView:itemForRepresentedObjectAtIndexPath:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null)]
        public override NSCollectionViewItem GetItem(NSCollectionView collectionView, NSIndexPath indexPath)
        {
#if LOG
            Console.WriteLine("collectionView:itemForRepresentedObjectAtIndexPath:" + indexPath.ToString());
#endif

            NSCollectionViewItem item = null;

            if (this.mCollectionView != null)
            {
                item = this.mCollectionView.MakeItem(this.mCollectionView.CollectionViewItem, indexPath);
                tbCollectionViewItem itemNew = item as tbCollectionViewItem;
                if (item == null)
                    goto DoExit;

                tbButton btn = itemNew.View;
                if (btn == null)
                    goto DoExit;       

                this.mCollectionView.OnItemLoad(btn, new tbCollectionViewArgs() { IndexPath = indexPath, Item = itemNew });

                //item.ImageView = new NSImageView();
                //item.TextField = new NSTextField();

                //item.ImageView.Image = imageData.ImageValue;
                //item.TextField.Cell.Title = imageData.ImageTitle;
                //item.Selected = imageData.ImageSelected;


            }
            else if (collectionView != null)
            {
                item = collectionView.MakeItem("NSCollectionViewItem", indexPath);
            }

        DoExit:
            return item;
        }

        //[Export("collectionView:numberOfItemsInSection:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null)]
        public override nint GetNumberofItems(NSCollectionView collectionView, nint section)
        {
            //return 0;

            int count = 0;

            if (this.mCollectionView != null)
                count = this.mCollectionView.GetGroupItemCount((int)section);

#if LOG
            Console.WriteLine("collectionView:numberOfItemsInSection:" + section.ToString() + "\tCount:" + count.ToString());
#endif

            return count;
        }

        //[Export("numberOfSectionsInCollectionView:"), Introduced(PlatformName.MacOSX, 10, 11, PlatformArchitecture.All, null)]
        public override nint GetNumberOfSections(NSCollectionView collectionView)
        {
            int count = 0;

            if (this.mCollectionView != null)
                count = this.mCollectionView.GetGroupCount();

#if LOG
            Console.WriteLine("numberOfSectionsInCollectionView:" + count.ToString());
#endif

            return count;
        }

        //[Export("collectionView:viewForSupplementaryElementOfKind:atIndexPath:")]
        public override NSView GetView(NSCollectionView collectionView, NSString kind, NSIndexPath indexPath)
        {
#if LOG
            Console.WriteLine("collectionView:viewForSupplementaryElementOfKind:atIndexPath:" + kind.ToString() + "\t" + indexPath.ToString());
#endif

            NSView item = null;

            if (this.mCollectionView != null)
            {
                bool isHeaderView = (kind == NSCollectionElementKind.SectionHeader);

                if (isHeaderView)
                {
                    //this.mCollectionView.RegisterClassForSupplementaryView(this.mCollectionView.ElementType, NSCollectionElementKind.SectionHeader, "SectionHeader");                    
                    item = this.mCollectionView.MakeSupplementaryView(kind, this.mCollectionView.SectionHeader, indexPath);
                }
                else
                {
                    //this.mCollectionView.RegisterClassForSupplementaryView(this.mCollectionView.ElementType, NSCollectionElementKind.SectionFooter, "SectionFooter");
                    item = this.mCollectionView.MakeSupplementaryView(kind, this.mCollectionView.SectionFooter, indexPath);
                }

                tbImageData imageData = this.mCollectionView.GetGroup((int)indexPath.Section);
                if (imageData == null)
                    goto DoExit;

                tbButton btn = item as tbButton;
                if (btn == null)
                    goto DoExit;

                btn.SuspendLayout();
                btn.tbName = imageData.GroupKey;                
                btn.tbTag = imageData;
                btn.tbBadgeNumber = imageData.GroupCount;
                btn.tbTrianglePosition = (imageData.GroupShowItems ? tbTrianglePosition.Top : tbTrianglePosition.Right);

                if (!btn.tbHasInit)
                {
                    btn.tbHasInit = true;
                    btn.tbShowIconMore = true;
                    btn.tbIconMore = null;
                    btn.tbShowIconMoreOnLeft = true;
                    btn.tbIconMoreSize = new CGSize(16, 16);
                    btn.tbUseHandCursor = false;

                    btn.Click += (sender, e) => {
                        imageData.GroupShowItems = !imageData.GroupShowItems;
                        btn.tbTrianglePosition = (imageData.GroupShowItems ? tbTrianglePosition.Top : tbTrianglePosition.Right);
                    };
                }

                if (isHeaderView)
                {
                    btn.tbBackgroundColor = imageData.GroupHeaderBackgroundColor;
                    btn.tbBadgeBackgroundColor = imageData.GroupHeaderBadgeBackgroundColor;
                    btn.tbBadgeTextColor = imageData.GroupHeaderBadgeTextColor;
                    btn.tbText = imageData.GroupHeaderText;
                    btn.tbTextColor = imageData.GroupHeaderTextColor;
                    btn.tbTextFont = imageData.GroupHeaderTextFont;
                    btn.tbTextAlign = imageData.GroupHeaderTextAlign;
                    btn.tbPadding = imageData.GroupHeaderPadding;
                }
                else
                {
                    btn.tbBackgroundColor = imageData.GroupFooterBackgroundColor;
                    btn.tbText = imageData.GroupFooterText;
                    btn.tbTextColor = imageData.GroupFooterTextColor;
                    btn.tbTextFont = imageData.GroupFooterTextFont;
                    btn.tbTextAlign = imageData.GroupFooterTextAlign;
                    btn.tbPadding = imageData.GroupHeaderPadding;
                }

                btn.ResumeLayout();
            }
            else if (collectionView != null)
            {
                item = collectionView.MakeSupplementaryView(kind, "NSCollectionViewElement", indexPath);
            }

        DoExit:
            return item;
        }
    }
}
