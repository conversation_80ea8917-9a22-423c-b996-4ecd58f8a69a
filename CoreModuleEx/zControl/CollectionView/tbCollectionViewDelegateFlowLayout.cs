﻿using System;
using System.IO;
using System.Text;
using System.Collections.Generic;

using AppKit;
using Foundation;
using CoreGraphics;
using ImageKit;

using iTong.CoreFoundation;
using ObjCRuntime;

namespace iTong.CoreModule
{
    [Register("tbCollectionViewDelegateFlowLayout")]
    public class tbCollectionViewDelegateFlowLayout : tbCollectionViewDelegate
    {
        #region Constructors

        // Called when created from unmanaged code
        public tbCollectionViewDelegateFlowLayout(IntPtr handle) : base(handle)
        {
            this.Initialize();
        }

        // Called when created directly from a XIB file
        [Export("initWithCoder:")]
        public tbCollectionViewDelegateFlowLayout(NSCoder coder) : base()
        {
            this.Initialize();
        }

        [Export("init")]
        public tbCollectionViewDelegateFlowLayout() : base()
        {
            this.Initialize();
        }

        private void Initialize()
        {

        }

        #endregion

        public tbCollectionViewDelegateFlowLayout(tbCollectionView collectionView) : base(collectionView)
        {
            this.mCollectionView = collectionView;
        }

        [Export("collectionView:layout:insetForSectionAtIndex:")]
        public virtual NSEdgeInsets InsetForSection(NSCollectionView collectionView, NSCollectionViewLayout collectionViewLayout, nint section)
        {
            //分组的边距设置:分组整体边距的优先级，始终高于内部边距的优先级
            Console.WriteLine("collectionView:layout:insetForSectionAtIndex:" + section.ToString());
            //throw new You_Should_Not_Call_base_In_This_Method();

            tbPadding padding = tbPadding.Empty;

            if (this.mCollectionView == null)
                goto DoExit;

            tbImageData group = this.mCollectionView.GetGroup((int)section);
            if (group == null)
                goto DoExit;

            padding = group.EdgeInsets;

        DoExit:
            if (padding.IsEmpty && this.mCollectionView != null)
                return this.mCollectionView.FlowLayout.SectionInset;
            else
                return new NSEdgeInsets(padding.Top, padding.Left, padding.Bottom, padding.Right);
        }

        [Export("collectionView:layout:minimumInteritemSpacingForSectionAtIndex:")]
        public virtual nfloat MinimumInteritemSpacingForSection(NSCollectionView collectionView, NSCollectionViewLayout collectionViewLayout, nint section)
        {
            Console.WriteLine("collectionView:layout:minimumInteritemSpacingForSectionAtIndex:" + section.ToString());
            //throw new You_Should_Not_Call_base_In_This_Method();

            //分组内的列间距
            int itemSpace = 0;

            if (this.mCollectionView == null)
                goto DoExit;

            tbImageData group = this.mCollectionView.GetGroup((int)section);
            if (group == null)
                goto DoExit;

            itemSpace = group.GroupItemSpace;

        DoExit:
            if (itemSpace == 0 && this.mCollectionView != null)
                return this.mCollectionView.FlowLayout.MinimumInteritemSpacing;
            else
                return itemSpace;
        }

        [Export("collectionView:layout:minimumLineSpacingForSectionAtIndex:")]
        public virtual nfloat MinimumLineSpacing(NSCollectionView collectionView, NSCollectionViewLayout collectionViewLayout, nint section)
        {
            Console.WriteLine("collectionView:layout:minimumLineSpacingForSectionAtIndex:" + section.ToString());
            //throw new You_Should_Not_Call_base_In_This_Method();

            //分组内的行间距
            int lineSpace = 0;

            if (this.mCollectionView == null)
                goto DoExit;

            tbImageData group = this.mCollectionView.GetGroup((int)section);
            if (group == null)
                goto DoExit;

            lineSpace = group.GroupLineSpace;

        DoExit:
            if (lineSpace == 0 && this.mCollectionView != null)
                return this.mCollectionView.FlowLayout.MinimumLineSpacing;
            else
                return lineSpace;
        }

        [Export("collectionView:layout:referenceSizeForHeaderInSection:")]
        public virtual CGSize ReferenceSizeForHeader(NSCollectionView collectionView, NSCollectionViewLayout collectionViewLayout, nint section)
        {
            //throw new You_Should_Not_Call_base_In_This_Method();

            //分组的底部大小
            int heightHeader = 0;

            if (this.mCollectionView == null)
                goto DoExit;

            if (!this.mCollectionView.ShowGroup)
                goto DoExit;

            tbImageData group = this.mCollectionView.GetGroup((int)section);
            if (group == null)
                goto DoExit;

            if (group.GroupHeaderHeight >= 0)
                heightHeader = group.GroupHeaderHeight;

            //heightHeader = 30;

        DoExit:
            Console.WriteLine("collectionView:layout:referenceSizeForHeaderInSection:" + section.ToString() + "\t Size:" + new CGSize(0, heightHeader).ToString());
            return new CGSize(0, heightHeader);
        }

        [Export("collectionView:layout:referenceSizeForFooterInSection:")]
        public virtual CGSize ReferenceSizeForFooter(NSCollectionView collectionView, NSCollectionViewLayout collectionViewLayout, nint section)
        {
            //throw new You_Should_Not_Call_base_In_This_Method();

            //分组的底部大小
            int heightFooter = 0;

            if (this.mCollectionView == null)
                goto DoExit;

            if (!this.mCollectionView.ShowGroup)
                goto DoExit;

            tbImageData group = this.mCollectionView.GetGroup((int)section);
            if (group == null)
                goto DoExit;

            if (group.GroupFooterHeight >= 0)
                heightFooter = group.GroupFooterHeight;

            //heightFooter = 30;

            DoExit:
            //Console.WriteLine("collectionView:layout:referenceSizeForFooterInSection:" + section.ToString() + "\t Size:" + new CGSize(0, heightFooter).ToString());
            return new CGSize(0, heightFooter);
        }

        [Export("collectionView:layout:sizeForItemAtIndexPath:")]
        public virtual CGSize SizeForItem(NSCollectionView collectionView, NSCollectionViewLayout collectionViewLayout, NSIndexPath indexPath)
        {
            //throw new You_Should_Not_Call_base_In_This_Method();

            //分组的元素大小
            CGSize sizeItem = CGSize.Empty;

            if (this.mCollectionView == null)
                goto DoExit;

            //默认赋值全局的大小
            sizeItem = this.mCollectionView.CellSize;

            tbImageData group = this.mCollectionView.GetGroup((int)indexPath.Section);
            if (group == null)
                goto DoExit;

            if (!group.GroupItemSize.IsEmpty)
                sizeItem = group.GroupItemSize;

            DoExit:
            //Console.WriteLine("collectionView:layout:sizeForItemAtIndexPath:" + sizeItem.ToString() + "\t" + indexPath.ToString());
            return sizeItem;
        }
    }
}
