using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;
using System.Threading;

using AppKit;
using Foundation;
using ObjCRuntime;
using AVFoundation;

using iTong.CoreFoundation;
using iTong.Android;
using Security;
using CoreGraphics;

namespace iTong.CoreModule
{
    public enum TCCServiceType
    {
        None,

        /// <summary>
        /// 辅助权限
        /// 这个服务涉及应用程序对辅助功能的访问权限。
        /// 启用此权限的应用程序可以控制其他应用程序的界面元素，通常用于提供辅助功能支持。
        /// </summary>
        kTCCServiceAccessibility,

        /// <summary>
        /// 开发功能权限
        /// 这个服务与开发工具相关，允许开发工具访问某些系统功能或数据，以便进行调试和开发。
        /// </summary>
        //kTCCServiceDeveloperTool,

        /// <summary>
        /// 鼠标、键盘输入权限
        /// 这个服务允许应用程序生成和发布事件到系统或其他应用程序，通常用于模拟用户输入。
        /// </summary>
        kTCCServicePostEvent,

        /// <summary>
        /// 截屏权限
        /// 这个服务控制应用程序对屏幕截图和屏幕录制的权限。启用后，应用程序可以捕获屏幕内容。
        /// </summary>
        kTCCServiceScreenCapture,

        /// <summary>
        /// 完全磁盘访问权限
        /// 这个服务允许应用程序访问用户的所有文件，而不仅仅是特定的文件或文件夹。此权限通常需要用户明确授权。
        /// </summary>
        kTCCServiceSystemPolicyAllFiles,

        /// <summary>
        /// 管理应用对麦克风的访问权限
        /// </summary>
        kTCCServiceMicrophone,

        /// <summary>
        /// 管理应用对摄像头的访问权限
        /// </summary>
        kTCCServiceCamera,
        /*
kTCCServiceAddressBook: 管理应用对通讯录的访问权限。
kTCCServiceAppleEvents: 管理应用通过Apple事件（Apple Events）与其他应用进行通信的权限。
kTCCServiceCalendar: 管理应用对日历数据的访问权限。
kTCCServiceCamera: 管理应用对摄像头的访问权限。
kTCCServiceContactsFull: 管理应用对完整通讯录的访问权限。
kTCCServiceContactsLimited: 管理应用对有限通讯录信息的访问权限。
kTCCServiceMotion: 管理应用对运动与健身数据的访问权限。
kTCCServicePhotos: 管理应用对照片库的访问权限。
kTCCServicePhotosAdd: 管理应用向照片库添加照片的权限。
kTCCServiceReminders: 管理应用对提醒事项的访问权限。
kTCCServiceSpeechRecognition: 管理应用使用语音识别功能的权限。
kTCCServiceSystemPolicyDesktopFolder: 管理应用对桌面文件夹的访问权限。
kTCCServiceSystemPolicyDocumentsFolder: 管理应用对文档文件夹的访问权限。
kTCCServiceSystemPolicyDownloadsFolder: 管理应用对下载文件夹的访问权限。
kTCCServiceSystemPolicyNetworkVolumes: 管理应用对网络卷的访问权限。
kTCCServiceSystemPolicyRemovableVolumes: 管理应用对可移动卷的访问权限。
kTCCServiceSystemPolicySysAdminFiles: 管理应用对系统管理文件的访问权限。
         */
    }

    public class AccessPowerEventArg
    {
        public TCCServiceType ServiceType = TCCServiceType.None;
        public bool Granted = false;
    }

    public class MyAPI
    {
        public static event EventHandler<AccessPowerEventArg> AccessPowerCallback;

        protected static void OnAccessPowerCallback(TCCServiceType serviceType, bool granted)
        {
            AccessPowerCallback?.Invoke(null, new AccessPowerEventArg { ServiceType = serviceType, Granted = granted });
        }

        // Import the necessary system library
        [DllImport("libSystem.dylib")]
        protected static extern IntPtr getpwuid(int uid);

        // Define the passwd structure
        [StructLayout(LayoutKind.Sequential)]
        protected struct Passwd
        {
            public IntPtr pw_name; // Username
            public IntPtr pw_passwd; // Password
            public uint pw_uid; // User ID
            public uint pw_gid; // Group ID
            public IntPtr pw_gecos; // User information
            public IntPtr pw_dir; // Home directory
            public IntPtr pw_shell; // Shell program
        }

        // Import the getuid function
        [DllImport("libc")]
        protected static extern int getuid();

        public static string Username { get; private set; }

        public static string HomeDirectory { get; private set; }

        static MyAPI()
        {
            try
            {
                // Get the current user's UID
                int uid = getuid();

                // Get the passwd structure for the current user
                IntPtr pwPtr = getpwuid(uid);

                if (pwPtr == IntPtr.Zero)
                {
                    Console.WriteLine("Failed to get user information.");
                    return;
                }

                // Marshal the pointer to the Passwd structure
                Passwd passwd = Marshal.PtrToStructure<Passwd>(pwPtr);

                // Get the username from the passwd structure
                Username = Marshal.PtrToStringAnsi(passwd.pw_name);

                // Get the Home directory from the passwd structure
                HomeDirectory = Marshal.PtrToStringAnsi(passwd.pw_dir);

                Console.WriteLine("Current User: " + Username);

                if (ParaMgr.IsWindow)
                    LoadAccesssPower();
            }
            catch (Exception ex)
            {
                WriteLine(ex.ToString());
            }
        }

        public static void Log(string msg)
        {
            MyLog.LogFile(msg, "API");
        }

        public static void WriteLine(string msg)
        {
            MyLog.WriteLine(msg, "API_DLL");
        }
      

        #region 屏幕录制ScreenCapture

        [DllImport(Constants.CoreGraphicsLibrary)]
        protected extern static IntPtr CGWindowListCopyWindowInfo(uint option, uint relativeToWindow);

        [DllImport(Constants.CoreGraphicsLibrary)]
        protected extern static uint CGMainDisplayID();

        [DllImport(Constants.CoreGraphicsLibrary)]
        protected static extern IntPtr CGDisplayCreateImage(uint displayId);

        [DllImport(Constants.CoreGraphicsLibrary)]
        protected extern static IntPtr CGDisplayCreateImageForRect(uint display, CGRect rect);

        // 导入Cocoa框架中的CGPreflightScreenCaptureAccess函数
        //[DllImport("/System/Library/Frameworks/CoreGraphics.framework/CoreGraphics")]
        [DllImport(Constants.CoreGraphicsLibrary)]
        public static extern bool CGPreflightScreenCaptureAccess();

        [DllImport(Constants.CoreGraphicsLibrary)]
        protected static extern void CGRequestScreenCaptureAccess(Action<bool> callback);

        [DllImport("/System/Library/Frameworks/AppKit.framework/AppKit")]
        protected static extern bool NSApplicationLoad();

        protected static bool mScreenCaptureAccessValue = false;
        protected static void OnScreenCaptureAccessRequested(bool granted)
        {
            mScreenCaptureAccessValue = granted;

            WriteLine(string.Format("ScreenCaptureAccessRequested = {0}", granted));

            MyReset.Set("CGRequestScreenCaptureAccess");
        }

        public static bool CheckHasScreenCaptureFromWindows()
        {
            bool blnResult = false;

            try
            {
                // kCGWindowListOptionOnScreenOnly, kCGNullWindowID
                const uint kCGWindowListOptionOnScreenOnly = 1;
                const uint kCGNullWindowID = 0;

                IntPtr hWindows = CGWindowListCopyWindowInfo(kCGWindowListOptionOnScreenOnly, kCGNullWindowID);
                if (hWindows == IntPtr.Zero)
                    goto DoExit;

                IList<object> listWindows = iTong.Device.CoreFoundation.ManagedTypeFromCFType(hWindows) as IList<object>;

                //释放资源
                iTong.Device.CoreFoundation.CFRelease(hWindows);

                if (listWindows == null)
                    goto DoExit;

                //int count = 0;
                //foreach (Dictionary<object, object> dict in listWindows)
                //{
                //    if (dict.ContainsKey("kCGWindowName"))
                //    {
                //        count++;
                //        continue;
                //    }

                //    if (dict.ContainsKey("kCGWindowSharingState"))
                //    {
                //        long state = Convert.ToInt64(dict["kCGWindowSharingState"]);
                //        if (state != 0)
                //        {
                //            count++;
                //        }
                //        else
                //        {
                //            string jsonItem = MyJson.SerializeToJsonStringAsFormat(dict);
                //            Console.WriteLine(jsonItem);

                //            if (dict.ContainsKey("kCGWindowBounds"))
                //            {
                //                Dictionary<object, object> dictBounds = dict["kCGWindowBounds"] as Dictionary<object, object>;
                //                if (dictBounds != null && dictBounds.ContainsKey("Width") && dictBounds.ContainsKey("Height"))
                //                {
                //                    long Width = Convert.ToInt64(dictBounds["Width"]);
                //                    long Height = Convert.ToInt64(dictBounds["Height"]);
                //                    if (Width == 0 || Height == 0)
                //                    {
                //                        count++;
                //                        continue;
                //                    }
                //                }
                //            }
                //        }

                //        continue;
                //    }
                //}

                //string json = MyJson.SerializeToJsonStringAsFormat(listWindows);

                //如果可检测到的窗口数 == 总数，就认为有权限
                //blnResult = listWindows.Count == count;

                int totalWindows = listWindows.Count;
                int accessibleWindows = 0;

                foreach (Dictionary<object, object> dict in listWindows)
                {
                    if (!dict.ContainsKey("kCGWindowSharingState") && !dict.ContainsKey("kCGWindowName")) // 提前检查关键属性，减少不必要的遍历
                        continue;

                    if (dict.ContainsKey("kCGWindowName"))
                    {
                        accessibleWindows++;
                        continue;
                    }

                    if (dict.TryGetValue("kCGWindowSharingState", out var stateObj)) // 使用TryGetValue替代ContainsKey + 索引访问，减少字典查找次数
                    {
                        long state = Convert.ToInt64(stateObj);
                        if (state != 0)
                        {
                            accessibleWindows++;
                            continue;
                        }

                        if (dict.TryGetValue("kCGWindowBounds", out var boundsObj) && boundsObj is Dictionary<object, object> bounds) //使用TryGetValue和类型检查
                        {
                            if (bounds.TryGetValue("Width", out var widthObj) && bounds.TryGetValue("Height", out var heightObj))
                            {
                                long width = Convert.ToInt64(widthObj);
                                long height = Convert.ToInt64(heightObj);
                                if (width == 0 || height == 0)
                                {
                                    accessibleWindows++;
                                }
                            }
                        }
                    }
                }
                // 如果可检测到的窗口数 == 总数，就认为有权限
                blnResult = totalWindows == accessibleWindows;

                {
                    /*
                        {
                            "kCGWindowLayer":-2147483603,
                            "kCGWindowMemoryUsage":1152,
                            "kCGWindowIsOnscreen":true,
                            "kCGWindowSharingState":0,
                            "kCGWindowOwnerPID":41415,
                            "kCGWindowNumber":30693,
                            "kCGWindowOwnerName":"访达",
                            "kCGWindowStoreType":1,
                            "kCGWindowBounds":{
                                "X":-1080,
                                "Height":1920,
                                "Y":-261,
                                "Width":1080
                            },
                            "kCGWindowAlpha":1
                        },
                       {
                        "kCGWindowLayer":-2147483624,
                        "kCGWindowAlpha":1,
                        "kCGWindowMemoryUsage":1152,
                        "kCGWindowIsOnscreen":true,
                        "kCGWindowSharingState":1,
                        "kCGWindowOwnerPID":41412,
                        "kCGWindowNumber":103,
                        "kCGWindowOwnerName":"程序坞",
                        "kCGWindowStoreType":1,
                        "kCGWindowBounds":{
                            "X":0,
                            "Height":1440,
                            "Y":0,
                            "Width":2560
                        },
                        "kCGWindowName":"Desktop Picture - Yosemite.jpg"
                    },
                     */
                }

            }
            catch (Exception ex)
            {
                Common.LogException(ex, "CheckHasScreenCaptureFromWindows");
            }

        DoExit:
            return blnResult;
        }

        /// <summary>
        /// 是否有屏幕录制权限
        /// </summary>
        /// <returns></returns>
        public static bool CheckHasScreenCapture()
        {
            try
            {
                //if (Common.OSVersion >= new Version("11.0"))
                //{
                //    //函数状态还是有缓存，故弃用
                //    AuthStatusForScreenCapture = CGPreflightScreenCaptureAccess();
                //}
                //else

                if (Common.OSVersion >= new Version("10.15"))
                {
                    
                    //RequestAccessPower(TCCServiceType.kTCCServiceScreenCapture);
                    AuthStatusForScreenCapture = CheckHasScreenCaptureFromWindows();
                }
                else
                {
                    AuthStatusForScreenCapture = true;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "CheckHasScreenCapture");
            }

            return AuthStatusForScreenCapture;
        }

        public static bool ShowScreenCapturePermissions()
        {
            bool blnResult = false;

            try
            {
                WriteLine(string.Format("ShowScreenCapturePermissions = {0}", "begin"));

                if (Common.OSVersion >= new Version(11, 0))
                {
                    blnResult = CGPreflightScreenCaptureAccess();
                    WriteLine(string.Format("CGPreflightScreenCaptureAccess = {0}", blnResult));

                    if (!blnResult)
                    {
                        CGRequestScreenCaptureAccess(OnScreenCaptureAccessRequested);
                        MyReset.WaitOne("CGRequestScreenCaptureAccess", "", 5);

                        blnResult = mScreenCaptureAccessValue;
                    }
                }
                else if (Common.OSVersion >= new Version(10, 15))
                {
                    WriteLine(string.Format("CGDisplayCreateImage = {0}", blnResult));

                    if (!blnResult)
                    {
                        CGDisplayCreateImage(CGMainDisplayID());

                        blnResult = mScreenCaptureAccessValue;
                    }
                }
                else
                {
                    blnResult = true;
                    //blnResult = NSApplicationLoad();
                    //WriteLine(string.Format("NSApplicationLoad = {0}", blnResult));
                }

                WriteLine(string.Format("ShowScreenCapturePermissions = {0}", "end"));
            }
            catch (Exception ex)
            {
                WriteLine(string.Format("ShowScreenCapturePermissions  Exception = {0}", ex.ToString()));
            }

            return blnResult;
        }

        public static void OpenSettingForScreenCapture()
        {
            if (Common.OSVersion < new Version(10, 15))
                return;

            Common.RunShell("/usr/bin/open", "x-apple.systempreferences:com.apple.preference.security?Privacy_ScreenCapture");
        }

        #endregion

        #region 辅助权限Accessibility

        /// <summary>辅助功能权限</summary>
        /// <returns></returns>
        [DllImport("/System/Library/Frameworks/ApplicationServices.framework/ApplicationServices")]
        public static extern bool AXIsProcessTrusted();


        [DllImport("/System/Library/Frameworks/ApplicationServices.framework/ApplicationServices")]
        protected static extern bool AXIsProcessTrustedWithOptions(IntPtr options);

        /// <summary>
        /// 是否有辅助功能权限
        /// </summary>
        /// <returns></returns>
        public static bool CheckHasAccessibility(bool AXTrustedCheckOptionPrompt = false)
        {
            bool blnResult = false;

            try
            {
                Dictionary<object, object> dict = new Dictionary<object, object>();
                dict.Add("AXTrustedCheckOptionPrompt", AXTrustedCheckOptionPrompt);// 设置为 true 会在没有权限时提示用户授予权限

                IntPtr hDict = iTong.Device.CoreFoundation.CFTypeFromManagedType(dict);

                // 检查应用程序是否被授予辅助功能权限
                blnResult = AXIsProcessTrustedWithOptions(hDict);

                iTong.Device.CoreFoundation.CFRelease(hDict);
            }
            catch (Exception ex)
            {
                // 捕获其他异常，并输出错误信息
                Console.WriteLine("RequestAccessAccessibility: " + ex.Message);
            }

            AuthStatusForAccessibility = blnResult;

            return blnResult;
        }

        public static bool ShowAccessibilityPermissions()
        {
            bool blnResult = CheckHasAccessibility(true);

            return blnResult;
        }

        public static void OpenSettingForAccessibility()
        {
            if (Common.OSVersion < new Version(10, 9))
                return;

            Common.RunShell("/usr/bin/open", "x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility");
        }

        #endregion

        #region 完全磁盘访问权限AllFiles

        /// <summary>
        /// 是否有完全磁盘访问权限
        /// </summary>
        /// <returns></returns>
        public static bool CheckHasFullDiskAccess(int waitSecond = 2)
        {
            bool blnResult = false;

            try
            {
                if (Common.OSVersion < new Version("10.14"))
                {
                    blnResult = true;
                    goto DoExit;
                }

                // 获取当前用户的主目录
                string homeDirectory = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);

                // 检查用户是否为沙盒环境
                var environment = NSProcessInfo.ProcessInfo.Environment;
                if (!environment.ContainsKey((NSString)"APP_SANDBOX_CONTAINER_ID"))
                {
                    // 如果不是沙盒，需要通过 getpwuid/getuid 查询真实的用户目录
                    if (!string.IsNullOrEmpty(HomeDirectory))
                        homeDirectory = HomeDirectory;
                }

                // 检查 ~/Library/Safari 是否可访问
                string safariPath = Path.Combine(homeDirectory, "Library", "Safari");
                //if (Directory.Exists(safariPath))
                if (NSFileManager.DefaultManager.FileExists(safariPath))
                {
                    // 尝试读取文件，若能正常读取则认为具备完全磁盘访问权限，没有权限发生Exception，提示没有权限访问
                    string[] arrFile = Directory.GetFiles(safariPath);

                    //没有异常说明有完全磁盘访问权限
                    blnResult = true;
                }

                //try
                //{
                //    // 尝试访问一个通常需要完全磁盘访问权限的文件
                //    string path = "/Library/Application Support/com.apple.TCC/TCC.db";
                //    using (FileStream fileStream = File.OpenRead(path))
                //    {
                //        // 如果能够打开文件，则可能具有完全磁盘访问权限
                //        blnResult = true;
                //    }
                //}
                //catch (Exception ex)
                //{
                //    // 捕获其他异常，并输出错误信息
                //    Console.WriteLine("HasFullDiskAccess: " + ex.Message);
                //}
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "CheckHasFullDiskAccess");
            }

        DoExit:
            AuthStatusForAllFiles = blnResult;

            return blnResult;
        }

        /// <summary>
        /// 打开完全磁盘访问设置
        /// </summary>
        public static void OpenSettingForAllFill()
        {
            if (Common.OSVersion < new Version("10.14"))
                return;

            Common.RunShell("/usr/bin/open", "x-apple.systempreferences:com.apple.preference.security?Privacy_AllFiles");
        }

        #endregion

        #region 相机与麦克风权限

        protected static bool GetAuthorizationStatus(AVAuthorizationMediaType mediaType)
        {
            bool blnResult = false;

            if (Common.OSVersion < new Version("10.14"))
            {
                blnResult = true;
                goto DoExit;
            }

            // 检查当前麦克风权限状态
            AVAuthorizationStatus status = AVCaptureDevice.GetAuthorizationStatus(mediaType);

            Log(string.Format("AVAuthorizationMediaType = {0}, status = {1}", mediaType.ToString(), status));

            switch (status)
            {
                case AVAuthorizationStatus.Authorized:
                    Console.WriteLine("A status that indicates the user has explicitly granted an app permission to capture media.");
                    break;
                case AVAuthorizationStatus.Denied:
                    Console.WriteLine("A status that indicates the user has explicitly denied an app permission to capture media.");
                    break;
                case AVAuthorizationStatus.Restricted:
                    Console.WriteLine("A status that indicates the app isn’t permitted to use media capture devices.");
                    break;
                case AVAuthorizationStatus.NotDetermined:
                    // 请求麦克风权限
                    Console.WriteLine("A status that indicates the user hasn’t yet granted or denied authorization.");
                    break;
            }

            blnResult = (status == AVAuthorizationStatus.Authorized);

        DoExit:
            if (mediaType == AVAuthorizationMediaType.Video)
                AuthStatusForCamera = blnResult;
            else
                AuthStatusForMicrophone = blnResult; 

            return blnResult;
        }

        protected static bool RequestAccessForMediaType(AVAuthorizationMediaType mediaType)
        {
            bool blnResult = false;

            try
            {
                if (Common.OSVersion < new Version("10.14"))
                {
                    blnResult = true;
                    goto DoExit;
                }

                AVRequestAccessStatus callback = new AVRequestAccessStatus((bool granted) => {
                    Log(string.Format("RequestAccessForMediaType = {0}, granted = {1}", mediaType.ToString(), granted));

                    if (granted)
                        Console.WriteLine("A status that indicates the user has explicitly granted an app permission to capture media.");
                    else
                        Console.WriteLine("A status that indicates the user has explicitly denied an app permission to capture media.");

                    if (mediaType == AVAuthorizationMediaType.Video)
                        AuthStatusForCamera = granted;
                    else
                        AuthStatusForMicrophone = granted;

                    TCCServiceType serviceType = (mediaType == AVAuthorizationMediaType.Video ? TCCServiceType.kTCCServiceCamera : TCCServiceType.kTCCServiceMicrophone);
                    MyReset.Set("TCCAccessRequest", serviceType.ToString());

                    OnAccessPowerCallback(serviceType, granted);
                });


                // 请求权限
                AVCaptureDevice.RequestAccessForMediaType(AVMediaType.Audio, callback);
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "RequestAccessForMediaType.mediaType=" + mediaType.ToString());
            }

        DoExit:
            return blnResult;
        }

        /// <summary>是否有相机访问权限</summary>
        /// <returns></returns>
        public static bool CheckHasCameraAccess()
        {
            return GetAuthorizationStatus(AVAuthorizationMediaType.Video);
        }

        /// <summary>是否有麦克风访问权限</summary>
        /// <returns></returns>
        public static bool CheckHasMicrophoneAccess()
        {
            return GetAuthorizationStatus(AVAuthorizationMediaType.Audio);
        }

        /// <summary>
        /// 打开相机设置
        /// </summary>
        public static void OpenSettingForCamera()
        {
            if (Common.OSVersion < new Version("10.14"))
                return;

            Common.RunShell("/usr/bin/open", "x-apple.systempreferences:com.apple.preference.security?Privacy_Camera");
        }

        /// <summary>
        /// 打开麦克风设置
        /// </summary>
        public static void OpenSettingForMicrophone()
        {
            if (Common.OSVersion < new Version("10.14"))
                return;

            Common.RunShell("/usr/bin/open", "x-apple.systempreferences:com.apple.preference.security?Privacy_Microphone");
        }

        /// <summary>Mac设置永不休眠</summary>
        public static void SetNeverSleep()
        {
            SocketMgr.SendMsgFromClient(RSEvent.Create(RSEventType.NeverSleep));
        }

        /// <summary>Mac打开系统睡眠设置</summary>
        public static void OpenSettingForBattery()
        {
            Version osVersion = Common.OSVersion;

            if (osVersion < new Version("11.0"))
                return;

            else if (osVersion < new Version("13.0"))
                Common.RunShell("/usr/bin/open", "/System/Library/PreferencePanes/DesktopScreenEffectsPref.prefPane");

            else
                Common.RunShell("/usr/bin/open", "x-apple.systempreferences:com.apple.Lock-Screen-Settings.extension");
        }

        #endregion


        #region TCC

        internal static class TCCAccessRequestBlock
        {
            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            internal delegate void TCCAccessRequestBlockProxy(IntPtr block, bool granted);

            internal static readonly TCCAccessRequestBlockProxy Handler;

            static TCCAccessRequestBlock()
            {
                Handler = Invoke;
            }

            // Methods
            [MonoPInvokeCallback(typeof(TCCAccessRequestBlockProxy))]
            private static unsafe void Invoke(IntPtr block, bool granted)
            {
                BlockLiteral* descriptor = (BlockLiteral*)block;
                object objTarget = (descriptor->Target);
                Action<bool> callback = objTarget as Action<bool>;
                if (callback != null)
                    callback(granted);
            }
        }

        [UnmanagedFunctionPointer(CallingConvention.StdCall)]
        private delegate int TCCAccessRequestDelegate(IntPtr service, IntPtr options, IntPtr callback);

        public const string PathCoreFoundation = "/System/Library/Frameworks/CoreFoundation.framework/CoreFoundation";
        public const string PathTCC = "/System/Library/PrivateFrameworks/TCC.framework/TCC";

        [DllImport(PathCoreFoundation, EntryPoint = "__CFStringMakeConstantString", CallingConvention = CallingConvention.Cdecl)]
        public static extern IntPtr CFStringMakeConstantString(string strName);

        protected static IntPtr hTCC;
        protected static IntPtr hTCCAccessRequest;

        public static void TCCAccessRequest(TCCServiceType serviceType, Action<bool> callback = null)
        {
            if (hTCC == IntPtr.Zero)
                hTCC = API.LoadLibrary("/System/Library/PrivateFrameworks/TCC.framework/TCC");

            if (hTCC == IntPtr.Zero)
                return;

            if (hTCCAccessRequest == IntPtr.Zero)
                hTCCAccessRequest = API.GetFuncAddress(hTCC, "TCCAccessRequest");

            if (hTCCAccessRequest == IntPtr.Zero)
                return;

            if (callback == null)
            {
                callback = new Action<bool>((granted) => {
                    Log(string.Format("TCCServiceType = {0}, granted = {1}", serviceType.ToString(), granted));

                    switch (serviceType)
                    {
                        case TCCServiceType.kTCCServiceScreenCapture: AuthStatusForScreenCapture = granted; break;
                        case TCCServiceType.kTCCServiceAccessibility: AuthStatusForAccessibility = granted; break;
                        case TCCServiceType.kTCCServiceSystemPolicyAllFiles: AuthStatusForAllFiles = granted; break;
                        case TCCServiceType.kTCCServiceMicrophone: AuthStatusForMicrophone = granted; break;
                        case TCCServiceType.kTCCServiceCamera: AuthStatusForCamera = granted; break;
                    }

                    MyReset.Set("TCCAccessRequest", serviceType.ToString());

                    OnAccessPowerCallback(serviceType, granted);
                });
            }

            unsafe
            {
                TCCAccessRequestDelegate tCCAccess = (TCCAccessRequestDelegate)Marshal.GetDelegateForFunctionPointer(hTCCAccessRequest, typeof(TCCAccessRequestDelegate));
                if (tCCAccess == null)
                    return;

                BlockLiteral* blockCompletion_ptr;
                BlockLiteral blockCompletion = new BlockLiteral();
                blockCompletion_ptr = &blockCompletion;
                blockCompletion.SetupBlock(TCCAccessRequestBlock.Handler, callback);

                IntPtr hService = CFStringMakeConstantString(serviceType.ToString());

                int result = tCCAccess(hService, IntPtr.Zero, (IntPtr)blockCompletion_ptr);
            }
        }

        public static void TCCAccessPrint()
        {
            foreach (TCCServiceType type in System.Enum.GetValues(typeof(TCCServiceType)))
            {
                MyAPI.TCCAccessRequest(type, (a) => {
                    MyLog.WriteLine(string.Format("TCCAccessRequest -> {0} = {1}", type.ToString(), a.ToString()), "TCC");
                });
            }
        }

        public static bool TTCAccesssReset(string sku, TCCServiceType type = TCCServiceType.kTCCServiceCamera)
        {
            bool blnResult = false;

            string strType = type.ToString().Replace("kTCCService", "");

            string strLog = Common.RunShell("/usr/bin/tccutil", "reset", "", sku);

            return blnResult;
        }

        #endregion

        #region 权限的查询、开启


        public static bool AuthStatusForCamera { get; set; } = false;
        public static bool AuthStatusForMicrophone { get; set; } = false;
        public static bool AuthStatusForScreenCapture { get; set; } = false;
        public static bool AuthStatusForAccessibility { get; set; } = false;
        public static bool AuthStatusForAllFiles { get; set; } = false;



        protected static Thread mThreadLoadAccess;
        protected static DateTime mThreadRunTime = DateTime.Now;
        // 添加权限状态缓存，用于检测权限变更
        private static bool mPreviousScreenCapture = false;
        private static bool mPreviousAccessibility = false;
        private static bool mPreviousAllFiles = false;
        private static bool mPreviousMicrophone = false;
        private static bool mPermissionInitialized = false;

        public static void LoadAccesssPower()
        {
            if (ThreadMgr.CheckThreadIsAlive(mThreadLoadAccess))
                return;

            mThreadLoadAccess = ThreadMgr.Start(new ThreadStart(() => {
                mThreadRunTime = DateTime.Now;

                while (true)
                {
                    // 保存之前的权限状态
                    bool prevScreenCapture = AuthStatusForScreenCapture;
                    bool prevAccessibility = AuthStatusForAccessibility;
                    bool prevAllFiles = AuthStatusForAllFiles;
                    bool prevMicrophone = AuthStatusForMicrophone;

                    //CheckHasCameraAccess();       //是否有访问相机权限
                    CheckHasMicrophoneAccess();   //是否有访问麦克风权限
                    CheckHasFullDiskAccess();     //是否有完全磁盘访问权限

                    CheckHasAccessibility();      //是否有辅助功能权限
                    CheckHasScreenCapture();      //是否有屏幕录制权限

                    // 检测权限变更
                    if (mPermissionInitialized)
                    {
                        bool permissionChanged = false;

                        // 检测完全磁盘访问权限变更（这是最关键的权限）
                        if (prevAllFiles != AuthStatusForAllFiles)
                        {
                            WriteLine($"Full Disk Access permission changed: {prevAllFiles} -> {AuthStatusForAllFiles}");
                            permissionChanged = true;
                        }

                        // 检测屏幕录制权限变更
                        if (prevScreenCapture != AuthStatusForScreenCapture)
                        {
                            WriteLine($"Screen Capture permission changed: {prevScreenCapture} -> {AuthStatusForScreenCapture}");
                            permissionChanged = true;
                        }

                        // 检测辅助功能权限变更
                        if (prevAccessibility != AuthStatusForAccessibility)
                        {
                            WriteLine($"Accessibility permission changed: {prevAccessibility} -> {AuthStatusForAccessibility}");
                            permissionChanged = true;
                        }

                        // 如果权限发生变更且新权限被授予，触发应用重启
                        if (permissionChanged && (AuthStatusForAllFiles || AuthStatusForScreenCapture || AuthStatusForAccessibility))
                        {
                            WriteLine("Permission granted, triggering application restart...");

                            // 延迟一下确保权限状态稳定
                            Thread.Sleep(1000);

                            // 触发应用重启
                            TriggerApplicationRestart("Permission granted");
                            break;
                        }
                    }
                    else
                    {
                        mPermissionInitialized = true;
                    }

                    //回调界面
                    OnAccessPowerCallback(TCCServiceType.None, false);

                    if (AuthStatusForScreenCapture && AuthStatusForAccessibility && AuthStatusForAllFiles && AuthStatusForMicrophone)
                    {
                        //Thread.Sleep(10000);
                        break;
                    }
                    else
                    {
                        Thread.Sleep(2000);
                    }

                    if (DateTime.Now.Subtract(mThreadRunTime).TotalSeconds > 60)
                        break;
                }

            }));
        }


        /// <summary>
        /// 触发应用重启
        /// </summary>
        /// <param name="reason">重启原因</param>
        public static void TriggerApplicationRestart(string reason)
        {
            try
            {
                WriteLine($"TriggerApplicationRestart: {reason}");

                // 发送重启事件到Service进程
                SocketMgr.SendMsgFromClient(RSEvent.Create(RSEventType.ReStart));

                // 延迟一下让消息发送完成
                Thread.Sleep(500);

                // 使用新的系统状态清理和重启逻辑
                ThreadMgr.Start(() => {
                    try
                    {
                        // 先清理系统状态，解决Bundle ID冲突问题
                        ServiceMgr.ClearSystemApplicationState();

                        // 等待系统状态清理完成
                        Thread.Sleep(2000);

                        // 重新启动Service进程
                        ServiceMgr.LaunchApp(ServiceMgr.ServiceNameForRS_Service, Application.ExecutablePath, ParaMgr.CommandRunService);
                        Thread.Sleep(1000);

                        // 重新启动Windows进程
                        ServiceMgr.LaunchApp(ServiceMgr.ServiceNameForRS_Windows, Application.ExecutablePath, ParaMgr.CommandRunWidnows);

                        WriteLine("Application restart completed successfully");
                    }
                    catch (Exception ex)
                    {
                        WriteLine($"Error in restart thread: {ex}");
                    }
                });
            }
            catch (Exception ex)
            {
                WriteLine($"TriggerApplicationRestart error: {ex}");
            }
        }

        public static bool RequestAccessPower(TCCServiceType serviceType, int waitSecond = 0)
        {
            bool blnResult = false;

            switch (serviceType)
            {
                case TCCServiceType.kTCCServiceScreenCapture:
                    //TCCAccessRequest(serviceType);
                    OpenSettingForScreenCapture();
                    break;

                case TCCServiceType.kTCCServiceSystemPolicyAllFiles:
                    //TCCAccessRequest(serviceType);
                    OpenSettingForAllFill();
                    break;

                case TCCServiceType.kTCCServiceAccessibility:
                    //CheckHasAccessibility(true);
                    OpenSettingForAccessibility();
                    break;

                case TCCServiceType.kTCCServiceCamera:
                    RequestAccessForMediaType(AVAuthorizationMediaType.Video);
                    OpenSettingForCamera();
                    break;

                case TCCServiceType.kTCCServiceMicrophone:
                    RequestAccessForMediaType(AVAuthorizationMediaType.Audio);
                    OpenSettingForMicrophone();
                    break;
            }

            if (waitSecond > 0)
            {
                MyReset.WaitOne("TCCAccessRequest", serviceType.ToString(), waitSecond);
            }

            switch (serviceType)
            {
                case TCCServiceType.kTCCServiceScreenCapture: blnResult = AuthStatusForScreenCapture; break;
                case TCCServiceType.kTCCServiceAccessibility: blnResult = AuthStatusForAccessibility; break;
                case TCCServiceType.kTCCServiceSystemPolicyAllFiles: blnResult = AuthStatusForAllFiles; break;
                case TCCServiceType.kTCCServiceMicrophone: blnResult = AuthStatusForMicrophone; break;
                case TCCServiceType.kTCCServiceCamera: blnResult = AuthStatusForCamera; break;
            }

            return blnResult;
        }

        #endregion


        /// <summary>
        /// 锁屏
        /// </summary>
        /// <returns></returns>
        [DllImport("/System/Library/PrivateFrameworks/login.framework/Versions/A/login", EntryPoint = "SACSwitchToLoginWindow")]
        public static extern bool SACSwitchToLoginWindow();

        public static bool LockWorkStation()
        {
            //RunShell("/usr/bin/osascript", "-e", "tell application \"System Events\" to sleep"); //需要system events权限以及关闭屏幕后立即需要密码
            //RunShell("/usr/bin/osascript", "-e", "tell application \"System Events\" to keystroke \"q\" using {control down, command down}"); //需要system events权限
            if (ServiceMgr.IsRoot)
            {
                return false;
            }
            else
            {
                bool blnResult = SACSwitchToLoginWindow();
                return true;
            }
        }

        public static void SendMessage(string windowTitle)
        {

        }

        public static bool IsRDP()
        {
            return false;
        }

        public static bool SetWindowTopMost(IntPtr hWnd, bool topMost = true)
        {
            return false;
        }

        public static bool ReleaseCapture()
        {
            return false;
        }
    }
}
